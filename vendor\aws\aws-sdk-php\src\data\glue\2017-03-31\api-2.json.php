<?php
// This file was auto-generated from sdk-root/src/data/glue/2017-03-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-03-31', 'endpointPrefix' => 'glue', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'AWS Glue', 'serviceId' => 'Glue', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSGlue', 'uid' => 'glue-2017-03-31', ], 'operations' => [ 'BatchCreatePartition' => [ 'name' => 'BatchCreatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchCreatePartitionRequest', ], 'output' => [ 'shape' => 'BatchCreatePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchDeleteConnection' => [ 'name' => 'BatchDeleteConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteConnectionRequest', ], 'output' => [ 'shape' => 'BatchDeleteConnectionResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchDeletePartition' => [ 'name' => 'BatchDeletePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeletePartitionRequest', ], 'output' => [ 'shape' => 'BatchDeletePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchDeleteTable' => [ 'name' => 'BatchDeleteTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteTableRequest', ], 'output' => [ 'shape' => 'BatchDeleteTableResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchDeleteTableVersion' => [ 'name' => 'BatchDeleteTableVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteTableVersionRequest', ], 'output' => [ 'shape' => 'BatchDeleteTableVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'BatchGetPartition' => [ 'name' => 'BatchGetPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetPartitionRequest', ], 'output' => [ 'shape' => 'BatchGetPartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'BatchStopJobRun' => [ 'name' => 'BatchStopJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchStopJobRunRequest', ], 'output' => [ 'shape' => 'BatchStopJobRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateClassifier' => [ 'name' => 'CreateClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateClassifierRequest', ], 'output' => [ 'shape' => 'CreateClassifierResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateConnection' => [ 'name' => 'CreateConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConnectionRequest', ], 'output' => [ 'shape' => 'CreateConnectionResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateCrawler' => [ 'name' => 'CreateCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCrawlerRequest', ], 'output' => [ 'shape' => 'CreateCrawlerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateDatabase' => [ 'name' => 'CreateDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDatabaseRequest', ], 'output' => [ 'shape' => 'CreateDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateDevEndpoint' => [ 'name' => 'CreateDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDevEndpointRequest', ], 'output' => [ 'shape' => 'CreateDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'CreateJob' => [ 'name' => 'CreateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateJobRequest', ], 'output' => [ 'shape' => 'CreateJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreatePartition' => [ 'name' => 'CreatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePartitionRequest', ], 'output' => [ 'shape' => 'CreatePartitionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateScript' => [ 'name' => 'CreateScript', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateScriptRequest', ], 'output' => [ 'shape' => 'CreateScriptResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateTable' => [ 'name' => 'CreateTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTableRequest', ], 'output' => [ 'shape' => 'CreateTableResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'CreateTrigger' => [ 'name' => 'CreateTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTriggerRequest', ], 'output' => [ 'shape' => 'CreateTriggerResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'IdempotentParameterMismatchException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'CreateUserDefinedFunction' => [ 'name' => 'CreateUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'CreateUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'DeleteClassifier' => [ 'name' => 'DeleteClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteClassifierRequest', ], 'output' => [ 'shape' => 'DeleteClassifierResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteConnection' => [ 'name' => 'DeleteConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConnectionRequest', ], 'output' => [ 'shape' => 'DeleteConnectionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteCrawler' => [ 'name' => 'DeleteCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCrawlerRequest', ], 'output' => [ 'shape' => 'DeleteCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerRunningException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteDatabase' => [ 'name' => 'DeleteDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDatabaseRequest', ], 'output' => [ 'shape' => 'DeleteDatabaseResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteDevEndpoint' => [ 'name' => 'DeleteDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDevEndpointRequest', ], 'output' => [ 'shape' => 'DeleteDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteJob' => [ 'name' => 'DeleteJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteJobRequest', ], 'output' => [ 'shape' => 'DeleteJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeletePartition' => [ 'name' => 'DeletePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePartitionRequest', ], 'output' => [ 'shape' => 'DeletePartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteTable' => [ 'name' => 'DeleteTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTableRequest', ], 'output' => [ 'shape' => 'DeleteTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteTableVersion' => [ 'name' => 'DeleteTableVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTableVersionRequest', ], 'output' => [ 'shape' => 'DeleteTableVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'DeleteTrigger' => [ 'name' => 'DeleteTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTriggerRequest', ], 'output' => [ 'shape' => 'DeleteTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'DeleteUserDefinedFunction' => [ 'name' => 'DeleteUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'DeleteUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCatalogImportStatus' => [ 'name' => 'GetCatalogImportStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCatalogImportStatusRequest', ], 'output' => [ 'shape' => 'GetCatalogImportStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetClassifier' => [ 'name' => 'GetClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetClassifierRequest', ], 'output' => [ 'shape' => 'GetClassifierResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetClassifiers' => [ 'name' => 'GetClassifiers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetClassifiersRequest', ], 'output' => [ 'shape' => 'GetClassifiersResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetConnection' => [ 'name' => 'GetConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConnectionRequest', ], 'output' => [ 'shape' => 'GetConnectionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetConnections' => [ 'name' => 'GetConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetConnectionsRequest', ], 'output' => [ 'shape' => 'GetConnectionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCrawler' => [ 'name' => 'GetCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCrawlerRequest', ], 'output' => [ 'shape' => 'GetCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCrawlerMetrics' => [ 'name' => 'GetCrawlerMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCrawlerMetricsRequest', ], 'output' => [ 'shape' => 'GetCrawlerMetricsResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetCrawlers' => [ 'name' => 'GetCrawlers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCrawlersRequest', ], 'output' => [ 'shape' => 'GetCrawlersResponse', ], 'errors' => [ [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDatabase' => [ 'name' => 'GetDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDatabaseRequest', ], 'output' => [ 'shape' => 'GetDatabaseResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDatabases' => [ 'name' => 'GetDatabases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDatabasesRequest', ], 'output' => [ 'shape' => 'GetDatabasesResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDataflowGraph' => [ 'name' => 'GetDataflowGraph', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataflowGraphRequest', ], 'output' => [ 'shape' => 'GetDataflowGraphResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetDevEndpoint' => [ 'name' => 'GetDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDevEndpointRequest', ], 'output' => [ 'shape' => 'GetDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetDevEndpoints' => [ 'name' => 'GetDevEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDevEndpointsRequest', ], 'output' => [ 'shape' => 'GetDevEndpointsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'GetJob' => [ 'name' => 'GetJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRequest', ], 'output' => [ 'shape' => 'GetJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetJobRun' => [ 'name' => 'GetJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRunRequest', ], 'output' => [ 'shape' => 'GetJobRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetJobRuns' => [ 'name' => 'GetJobRuns', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobRunsRequest', ], 'output' => [ 'shape' => 'GetJobRunsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetJobs' => [ 'name' => 'GetJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetJobsRequest', ], 'output' => [ 'shape' => 'GetJobsResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetMapping' => [ 'name' => 'GetMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMappingRequest', ], 'output' => [ 'shape' => 'GetMappingResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'EntityNotFoundException', ], ], ], 'GetPartition' => [ 'name' => 'GetPartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartitionRequest', ], 'output' => [ 'shape' => 'GetPartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetPartitions' => [ 'name' => 'GetPartitions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartitionsRequest', ], 'output' => [ 'shape' => 'GetPartitionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetPlan' => [ 'name' => 'GetPlan', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPlanRequest', ], 'output' => [ 'shape' => 'GetPlanResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetTable' => [ 'name' => 'GetTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableRequest', ], 'output' => [ 'shape' => 'GetTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetTableVersion' => [ 'name' => 'GetTableVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableVersionRequest', ], 'output' => [ 'shape' => 'GetTableVersionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetTableVersions' => [ 'name' => 'GetTableVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTableVersionsRequest', ], 'output' => [ 'shape' => 'GetTableVersionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetTables' => [ 'name' => 'GetTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTablesRequest', ], 'output' => [ 'shape' => 'GetTablesResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'GetTrigger' => [ 'name' => 'GetTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTriggerRequest', ], 'output' => [ 'shape' => 'GetTriggerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetTriggers' => [ 'name' => 'GetTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTriggersRequest', ], 'output' => [ 'shape' => 'GetTriggersResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetUserDefinedFunction' => [ 'name' => 'GetUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'GetUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'GetUserDefinedFunctions' => [ 'name' => 'GetUserDefinedFunctions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserDefinedFunctionsRequest', ], 'output' => [ 'shape' => 'GetUserDefinedFunctionsResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InternalServiceException', ], ], ], 'ImportCatalogToGlue' => [ 'name' => 'ImportCatalogToGlue', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportCatalogToGlueRequest', ], 'output' => [ 'shape' => 'ImportCatalogToGlueResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'ResetJobBookmark' => [ 'name' => 'ResetJobBookmark', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetJobBookmarkRequest', ], 'output' => [ 'shape' => 'ResetJobBookmarkResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartCrawler' => [ 'name' => 'StartCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCrawlerRequest', ], 'output' => [ 'shape' => 'StartCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerRunningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartCrawlerSchedule' => [ 'name' => 'StartCrawlerSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCrawlerScheduleRequest', ], 'output' => [ 'shape' => 'StartCrawlerScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'SchedulerRunningException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'NoScheduleException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StartJobRun' => [ 'name' => 'StartJobRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartJobRunRequest', ], 'output' => [ 'shape' => 'StartJobRunResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StartTrigger' => [ 'name' => 'StartTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTriggerRequest', ], 'output' => [ 'shape' => 'StartTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], [ 'shape' => 'ConcurrentRunsExceededException', ], ], ], 'StopCrawler' => [ 'name' => 'StopCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopCrawlerRequest', ], 'output' => [ 'shape' => 'StopCrawlerResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerNotRunningException', ], [ 'shape' => 'CrawlerStoppingException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StopCrawlerSchedule' => [ 'name' => 'StopCrawlerSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopCrawlerScheduleRequest', ], 'output' => [ 'shape' => 'StopCrawlerScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'SchedulerNotRunningException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'StopTrigger' => [ 'name' => 'StopTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopTriggerRequest', ], 'output' => [ 'shape' => 'StopTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateClassifier' => [ 'name' => 'UpdateClassifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateClassifierRequest', ], 'output' => [ 'shape' => 'UpdateClassifierResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateConnection' => [ 'name' => 'UpdateConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConnectionRequest', ], 'output' => [ 'shape' => 'UpdateConnectionResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateCrawler' => [ 'name' => 'UpdateCrawler', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCrawlerRequest', ], 'output' => [ 'shape' => 'UpdateCrawlerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'CrawlerRunningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateCrawlerSchedule' => [ 'name' => 'UpdateCrawlerSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCrawlerScheduleRequest', ], 'output' => [ 'shape' => 'UpdateCrawlerScheduleResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'VersionMismatchException', ], [ 'shape' => 'SchedulerTransitioningException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateDatabase' => [ 'name' => 'UpdateDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDatabaseRequest', ], 'output' => [ 'shape' => 'UpdateDatabaseResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateDevEndpoint' => [ 'name' => 'UpdateDevEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDevEndpointRequest', ], 'output' => [ 'shape' => 'UpdateDevEndpointResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateJob' => [ 'name' => 'UpdateJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateJobRequest', ], 'output' => [ 'shape' => 'UpdateJobResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdatePartition' => [ 'name' => 'UpdatePartition', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePartitionRequest', ], 'output' => [ 'shape' => 'UpdatePartitionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], 'UpdateTable' => [ 'name' => 'UpdateTable', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTableRequest', ], 'output' => [ 'shape' => 'UpdateTableResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNumberLimitExceededException', ], ], ], 'UpdateTrigger' => [ 'name' => 'UpdateTrigger', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTriggerRequest', ], 'output' => [ 'shape' => 'UpdateTriggerResponse', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'OperationTimeoutException', ], [ 'shape' => 'ConcurrentModificationException', ], ], ], 'UpdateUserDefinedFunction' => [ 'name' => 'UpdateUserDefinedFunction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserDefinedFunctionRequest', ], 'output' => [ 'shape' => 'UpdateUserDefinedFunctionResponse', ], 'errors' => [ [ 'shape' => 'EntityNotFoundException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'InternalServiceException', ], [ 'shape' => 'OperationTimeoutException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Action' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'Arguments' => [ 'shape' => 'GenericMap', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], ], ], 'ActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Action', ], ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'AttemptCount' => [ 'type' => 'integer', ], 'BatchCreatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionInputList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionInputList' => [ 'shape' => 'PartitionInputList', ], ], ], 'BatchCreatePartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'PartitionErrors', ], ], ], 'BatchDeleteConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionNameList', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ConnectionNameList' => [ 'shape' => 'DeleteConnectionNameList', ], ], ], 'BatchDeleteConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Succeeded' => [ 'shape' => 'NameStringList', ], 'Errors' => [ 'shape' => 'ErrorByName', ], ], ], 'BatchDeletePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionsToDelete', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionsToDelete' => [ 'shape' => 'BatchDeletePartitionValueList', ], ], ], 'BatchDeletePartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'PartitionErrors', ], ], ], 'BatchDeletePartitionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueList', ], 'max' => 25, 'min' => 0, ], 'BatchDeleteTableNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 100, 'min' => 0, ], 'BatchDeleteTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TablesToDelete', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TablesToDelete' => [ 'shape' => 'BatchDeleteTableNameList', ], ], ], 'BatchDeleteTableResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'TableErrors', ], ], ], 'BatchDeleteTableVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VersionString', ], 'max' => 100, 'min' => 0, ], 'BatchDeleteTableVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'VersionIds', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'VersionIds' => [ 'shape' => 'BatchDeleteTableVersionList', ], ], ], 'BatchDeleteTableVersionResponse' => [ 'type' => 'structure', 'members' => [ 'Errors' => [ 'shape' => 'TableVersionErrors', ], ], ], 'BatchGetPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionsToGet', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionsToGet' => [ 'shape' => 'BatchGetPartitionValueList', ], ], ], 'BatchGetPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Partitions' => [ 'shape' => 'PartitionList', ], 'UnprocessedKeys' => [ 'shape' => 'BatchGetPartitionValueList', ], ], ], 'BatchGetPartitionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionValueList', ], 'max' => 1000, 'min' => 0, ], 'BatchStopJobRunError' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'IdString', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'BatchStopJobRunErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStopJobRunError', ], ], 'BatchStopJobRunJobRunIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdString', ], 'max' => 25, 'min' => 1, ], 'BatchStopJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'JobRunIds', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunIds' => [ 'shape' => 'BatchStopJobRunJobRunIdList', ], ], ], 'BatchStopJobRunResponse' => [ 'type' => 'structure', 'members' => [ 'SuccessfulSubmissions' => [ 'shape' => 'BatchStopJobRunSuccessfulSubmissionList', ], 'Errors' => [ 'shape' => 'BatchStopJobRunErrorList', ], ], ], 'BatchStopJobRunSuccessfulSubmission' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'IdString', ], ], ], 'BatchStopJobRunSuccessfulSubmissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BatchStopJobRunSuccessfulSubmission', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanNullable' => [ 'type' => 'boolean', ], 'BooleanValue' => [ 'type' => 'boolean', ], 'BoundedPartitionValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValueString', ], 'max' => 100, 'min' => 0, ], 'CatalogEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'CatalogEntry', ], ], 'CatalogEntry' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], ], ], 'CatalogIdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'CatalogImportStatus' => [ 'type' => 'structure', 'members' => [ 'ImportCompleted' => [ 'shape' => 'Boolean', ], 'ImportTime' => [ 'shape' => 'Timestamp', ], 'ImportedBy' => [ 'shape' => 'NameString', ], ], ], 'Classification' => [ 'type' => 'string', ], 'Classifier' => [ 'type' => 'structure', 'members' => [ 'GrokClassifier' => [ 'shape' => 'GrokClassifier', ], 'XMLClassifier' => [ 'shape' => 'XMLClassifier', ], 'JsonClassifier' => [ 'shape' => 'JsonClassifier', ], ], ], 'ClassifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Classifier', ], ], 'ClassifierNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'CodeGenArgName' => [ 'type' => 'string', ], 'CodeGenArgValue' => [ 'type' => 'string', ], 'CodeGenEdge' => [ 'type' => 'structure', 'required' => [ 'Source', 'Target', ], 'members' => [ 'Source' => [ 'shape' => 'CodeGenIdentifier', ], 'Target' => [ 'shape' => 'CodeGenIdentifier', ], 'TargetParameter' => [ 'shape' => 'CodeGenArgName', ], ], ], 'CodeGenIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z_][A-Za-z0-9_]*', ], 'CodeGenNode' => [ 'type' => 'structure', 'required' => [ 'Id', 'NodeType', 'Args', ], 'members' => [ 'Id' => [ 'shape' => 'CodeGenIdentifier', ], 'NodeType' => [ 'shape' => 'CodeGenNodeType', ], 'Args' => [ 'shape' => 'CodeGenNodeArgs', ], 'LineNumber' => [ 'shape' => 'Integer', ], ], ], 'CodeGenNodeArg' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'CodeGenArgName', ], 'Value' => [ 'shape' => 'CodeGenArgValue', ], 'Param' => [ 'shape' => 'Boolean', ], ], ], 'CodeGenNodeArgs' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeGenNodeArg', ], 'max' => 50, 'min' => 0, ], 'CodeGenNodeType' => [ 'type' => 'string', ], 'Column' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'ColumnTypeString', ], 'Comment' => [ 'shape' => 'CommentString', ], ], ], 'ColumnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Column', ], ], 'ColumnTypeString' => [ 'type' => 'string', 'max' => 131072, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'ColumnValueStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnValuesString', ], ], 'ColumnValuesString' => [ 'type' => 'string', ], 'CommentString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ConcurrentRunsExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Condition' => [ 'type' => 'structure', 'members' => [ 'LogicalOperator' => [ 'shape' => 'LogicalOperator', ], 'JobName' => [ 'shape' => 'NameString', ], 'State' => [ 'shape' => 'JobRunState', ], ], ], 'ConditionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Condition', ], ], 'Connection' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], 'PhysicalConnectionRequirements' => [ 'shape' => 'PhysicalConnectionRequirements', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedTime' => [ 'shape' => 'Timestamp', ], 'LastUpdatedBy' => [ 'shape' => 'NameString', ], ], ], 'ConnectionInput' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionType', 'ConnectionProperties', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'ConnectionType' => [ 'shape' => 'ConnectionType', ], 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ConnectionProperties' => [ 'shape' => 'ConnectionProperties', ], 'PhysicalConnectionRequirements' => [ 'shape' => 'PhysicalConnectionRequirements', ], ], ], 'ConnectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Connection', ], ], 'ConnectionName' => [ 'type' => 'string', ], 'ConnectionProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'ConnectionPropertyKey', ], 'value' => [ 'shape' => 'ValueString', ], 'max' => 100, 'min' => 0, ], 'ConnectionPropertyKey' => [ 'type' => 'string', 'enum' => [ 'HOST', 'PORT', 'USERNAME', 'PASSWORD', 'JDBC_DRIVER_JAR_URI', 'JDBC_DRIVER_CLASS_NAME', 'JDBC_ENGINE', 'JDBC_ENGINE_VERSION', 'CONFIG_FILES', 'INSTANCE_ID', 'JDBC_CONNECTION_URL', ], ], 'ConnectionType' => [ 'type' => 'string', 'enum' => [ 'JDBC', 'SFTP', ], ], 'ConnectionsList' => [ 'type' => 'structure', 'members' => [ 'Connections' => [ 'shape' => 'StringList', ], ], ], 'Crawler' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'Role', ], 'Targets' => [ 'shape' => 'CrawlerTargets', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Classifiers' => [ 'shape' => 'ClassifierNameList', ], 'SchemaChangePolicy' => [ 'shape' => 'SchemaChangePolicy', ], 'State' => [ 'shape' => 'CrawlerState', ], 'TablePrefix' => [ 'shape' => 'TablePrefix', ], 'Schedule' => [ 'shape' => 'Schedule', ], 'CrawlElapsedTime' => [ 'shape' => 'MillisecondsCount', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'LastCrawl' => [ 'shape' => 'LastCrawlInfo', ], 'Version' => [ 'shape' => 'VersionId', ], 'Configuration' => [ 'shape' => 'CrawlerConfiguration', ], ], ], 'CrawlerConfiguration' => [ 'type' => 'string', ], 'CrawlerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Crawler', ], ], 'CrawlerMetrics' => [ 'type' => 'structure', 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], 'TimeLeftSeconds' => [ 'shape' => 'NonNegativeDouble', ], 'StillEstimating' => [ 'shape' => 'Boolean', ], 'LastRuntimeSeconds' => [ 'shape' => 'NonNegativeDouble', ], 'MedianRuntimeSeconds' => [ 'shape' => 'NonNegativeDouble', ], 'TablesCreated' => [ 'shape' => 'NonNegativeInteger', ], 'TablesUpdated' => [ 'shape' => 'NonNegativeInteger', ], 'TablesDeleted' => [ 'shape' => 'NonNegativeInteger', ], ], ], 'CrawlerMetricsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CrawlerMetrics', ], ], 'CrawlerNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 100, 'min' => 0, ], 'CrawlerNotRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CrawlerRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CrawlerState' => [ 'type' => 'string', 'enum' => [ 'READY', 'RUNNING', 'STOPPING', ], ], 'CrawlerStoppingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'CrawlerTargets' => [ 'type' => 'structure', 'members' => [ 'S3Targets' => [ 'shape' => 'S3TargetList', ], 'JdbcTargets' => [ 'shape' => 'JdbcTargetList', ], 'DynamoDBTargets' => [ 'shape' => 'DynamoDBTargetList', ], ], ], 'CreateClassifierRequest' => [ 'type' => 'structure', 'members' => [ 'GrokClassifier' => [ 'shape' => 'CreateGrokClassifierRequest', ], 'XMLClassifier' => [ 'shape' => 'CreateXMLClassifierRequest', ], 'JsonClassifier' => [ 'shape' => 'CreateJsonClassifierRequest', ], ], ], 'CreateClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ConnectionInput' => [ 'shape' => 'ConnectionInput', ], ], ], 'CreateConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Role', 'DatabaseName', 'Targets', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'Role', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Targets' => [ 'shape' => 'CrawlerTargets', ], 'Schedule' => [ 'shape' => 'CronExpression', ], 'Classifiers' => [ 'shape' => 'ClassifierNameList', ], 'TablePrefix' => [ 'shape' => 'TablePrefix', ], 'SchemaChangePolicy' => [ 'shape' => 'SchemaChangePolicy', ], 'Configuration' => [ 'shape' => 'CrawlerConfiguration', ], ], ], 'CreateCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseInput' => [ 'shape' => 'DatabaseInput', ], ], ], 'CreateDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', 'RoleArn', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], 'SubnetId' => [ 'shape' => 'GenericString', ], 'PublicKey' => [ 'shape' => 'GenericString', ], 'NumberOfNodes' => [ 'shape' => 'IntegerValue', ], 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], ], ], 'CreateDevEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'GenericString', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], 'SubnetId' => [ 'shape' => 'GenericString', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'YarnEndpointAddress' => [ 'shape' => 'GenericString', ], 'ZeppelinRemoteSparkInterpreterPort' => [ 'shape' => 'IntegerValue', ], 'NumberOfNodes' => [ 'shape' => 'IntegerValue', ], 'AvailabilityZone' => [ 'shape' => 'GenericString', ], 'VpcId' => [ 'shape' => 'GenericString', ], 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], 'FailureReason' => [ 'shape' => 'GenericString', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampValue', ], ], ], 'CreateGrokClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Classification', 'Name', 'GrokPattern', ], 'members' => [ 'Classification' => [ 'shape' => 'Classification', ], 'Name' => [ 'shape' => 'NameString', ], 'GrokPattern' => [ 'shape' => 'GrokPattern', ], 'CustomPatterns' => [ 'shape' => 'CustomPatterns', ], ], ], 'CreateJobRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Role', 'Command', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LogUri' => [ 'shape' => 'UriString', ], 'Role' => [ 'shape' => 'RoleString', ], 'ExecutionProperty' => [ 'shape' => 'ExecutionProperty', ], 'Command' => [ 'shape' => 'JobCommand', ], 'DefaultArguments' => [ 'shape' => 'GenericMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], ], ], 'CreateJobResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateJsonClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'JsonPath', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'JsonPath' => [ 'shape' => 'JsonPath', ], ], ], 'CreatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionInput' => [ 'shape' => 'PartitionInput', ], ], ], 'CreatePartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateScriptRequest' => [ 'type' => 'structure', 'members' => [ 'DagNodes' => [ 'shape' => 'DagNodes', ], 'DagEdges' => [ 'shape' => 'DagEdges', ], 'Language' => [ 'shape' => 'Language', ], ], ], 'CreateScriptResponse' => [ 'type' => 'structure', 'members' => [ 'PythonScript' => [ 'shape' => 'PythonScript', ], 'ScalaCode' => [ 'shape' => 'ScalaCode', ], ], ], 'CreateTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableInput' => [ 'shape' => 'TableInput', ], ], ], 'CreateTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Type', 'Actions', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Type' => [ 'shape' => 'TriggerType', ], 'Schedule' => [ 'shape' => 'GenericString', ], 'Predicate' => [ 'shape' => 'Predicate', ], 'Actions' => [ 'shape' => 'ActionList', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'StartOnCreation' => [ 'shape' => 'BooleanValue', ], ], ], 'CreateTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'CreateUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionInput' => [ 'shape' => 'UserDefinedFunctionInput', ], ], ], 'CreateUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'CreateXMLClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Classification', 'Name', ], 'members' => [ 'Classification' => [ 'shape' => 'Classification', ], 'Name' => [ 'shape' => 'NameString', ], 'RowTag' => [ 'shape' => 'RowTag', ], ], ], 'CronExpression' => [ 'type' => 'string', ], 'CustomPatterns' => [ 'type' => 'string', 'max' => 16000, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DagEdges' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeGenEdge', ], ], 'DagNodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeGenNode', ], ], 'Database' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LocationUri' => [ 'shape' => 'URI', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], ], ], 'DatabaseInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LocationUri' => [ 'shape' => 'URI', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], ], ], 'DatabaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Database', ], ], 'DatabaseName' => [ 'type' => 'string', ], 'DeleteBehavior' => [ 'type' => 'string', 'enum' => [ 'LOG', 'DELETE_FROM_DATABASE', 'DEPRECATE_IN_DATABASE', ], ], 'DeleteClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteConnectionNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 25, 'min' => 0, ], 'DeleteConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'ConnectionName' => [ 'shape' => 'NameString', ], ], ], 'DeleteConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], ], ], 'DeleteDevEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'DeleteJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'DeletePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], ], ], 'DeletePartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTableVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'VersionId', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'DeleteTableVersionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'DeleteUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionName' => [ 'shape' => 'NameString', ], ], ], 'DeleteUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescriptionString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DescriptionStringRemovable' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'DevEndpoint' => [ 'type' => 'structure', 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'SecurityGroupIds' => [ 'shape' => 'StringList', ], 'SubnetId' => [ 'shape' => 'GenericString', ], 'YarnEndpointAddress' => [ 'shape' => 'GenericString', ], 'PrivateAddress' => [ 'shape' => 'GenericString', ], 'ZeppelinRemoteSparkInterpreterPort' => [ 'shape' => 'IntegerValue', ], 'PublicAddress' => [ 'shape' => 'GenericString', ], 'Status' => [ 'shape' => 'GenericString', ], 'NumberOfNodes' => [ 'shape' => 'IntegerValue', ], 'AvailabilityZone' => [ 'shape' => 'GenericString', ], 'VpcId' => [ 'shape' => 'GenericString', ], 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], 'FailureReason' => [ 'shape' => 'GenericString', ], 'LastUpdateStatus' => [ 'shape' => 'GenericString', ], 'CreatedTimestamp' => [ 'shape' => 'TimestampValue', ], 'LastModifiedTimestamp' => [ 'shape' => 'TimestampValue', ], 'PublicKey' => [ 'shape' => 'GenericString', ], ], ], 'DevEndpointCustomLibraries' => [ 'type' => 'structure', 'members' => [ 'ExtraPythonLibsS3Path' => [ 'shape' => 'GenericString', ], 'ExtraJarsS3Path' => [ 'shape' => 'GenericString', ], ], ], 'DevEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DevEndpoint', ], ], 'DynamoDBTarget' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'Path', ], ], ], 'DynamoDBTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DynamoDBTarget', ], ], 'EntityNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ErrorByName' => [ 'type' => 'map', 'key' => [ 'shape' => 'NameString', ], 'value' => [ 'shape' => 'ErrorDetail', ], ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'ErrorCode' => [ 'shape' => 'NameString', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], ], ], 'ErrorString' => [ 'type' => 'string', ], 'ExecutionProperty' => [ 'type' => 'structure', 'members' => [ 'MaxConcurrentRuns' => [ 'shape' => 'MaxConcurrentRuns', ], ], ], 'ExecutionTime' => [ 'type' => 'integer', ], 'FieldType' => [ 'type' => 'string', ], 'FilterString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'FormatString' => [ 'type' => 'string', 'max' => 128, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'GenericMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'GenericString', ], 'value' => [ 'shape' => 'GenericString', ], ], 'GenericString' => [ 'type' => 'string', ], 'GetCatalogImportStatusRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'GetCatalogImportStatusResponse' => [ 'type' => 'structure', 'members' => [ 'ImportStatus' => [ 'shape' => 'CatalogImportStatus', ], ], ], 'GetClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetClassifierResponse' => [ 'type' => 'structure', 'members' => [ 'Classifier' => [ 'shape' => 'Classifier', ], ], ], 'GetClassifiersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetClassifiersResponse' => [ 'type' => 'structure', 'members' => [ 'Classifiers' => [ 'shape' => 'ClassifierList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'Connection', ], ], ], 'GetConnectionsFilter' => [ 'type' => 'structure', 'members' => [ 'MatchCriteria' => [ 'shape' => 'MatchCriteria', ], 'ConnectionType' => [ 'shape' => 'ConnectionType', ], ], ], 'GetConnectionsRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Filter' => [ 'shape' => 'GetConnectionsFilter', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'ConnectionList' => [ 'shape' => 'ConnectionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlerMetricsRequest' => [ 'type' => 'structure', 'members' => [ 'CrawlerNameList' => [ 'shape' => 'CrawlerNameList', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlerMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'CrawlerMetricsList' => [ 'shape' => 'CrawlerMetricsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetCrawlerResponse' => [ 'type' => 'structure', 'members' => [ 'Crawler' => [ 'shape' => 'Crawler', ], ], ], 'GetCrawlersRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetCrawlersResponse' => [ 'type' => 'structure', 'members' => [ 'Crawlers' => [ 'shape' => 'CrawlerList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetDatabaseResponse' => [ 'type' => 'structure', 'members' => [ 'Database' => [ 'shape' => 'Database', ], ], ], 'GetDatabasesRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetDatabasesResponse' => [ 'type' => 'structure', 'required' => [ 'DatabaseList', ], 'members' => [ 'DatabaseList' => [ 'shape' => 'DatabaseList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetDataflowGraphRequest' => [ 'type' => 'structure', 'members' => [ 'PythonScript' => [ 'shape' => 'PythonScript', ], ], ], 'GetDataflowGraphResponse' => [ 'type' => 'structure', 'members' => [ 'DagNodes' => [ 'shape' => 'DagNodes', ], 'DagEdges' => [ 'shape' => 'DagEdges', ], ], ], 'GetDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], ], ], 'GetDevEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpoint' => [ 'shape' => 'DevEndpoint', ], ], ], 'GetDevEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetDevEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'DevEndpoints' => [ 'shape' => 'DevEndpointList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'GetJobResponse' => [ 'type' => 'structure', 'members' => [ 'Job' => [ 'shape' => 'Job', ], ], ], 'GetJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'RunId', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], 'PredecessorsIncluded' => [ 'shape' => 'BooleanValue', ], ], ], 'GetJobRunResponse' => [ 'type' => 'structure', 'members' => [ 'JobRun' => [ 'shape' => 'JobRun', ], ], ], 'GetJobRunsRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetJobRunsResponse' => [ 'type' => 'structure', 'members' => [ 'JobRuns' => [ 'shape' => 'JobRunList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetJobsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => 'JobList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetMappingRequest' => [ 'type' => 'structure', 'required' => [ 'Source', ], 'members' => [ 'Source' => [ 'shape' => 'CatalogEntry', ], 'Sinks' => [ 'shape' => 'CatalogEntries', ], 'Location' => [ 'shape' => 'Location', ], ], ], 'GetMappingResponse' => [ 'type' => 'structure', 'required' => [ 'Mapping', ], 'members' => [ 'Mapping' => [ 'shape' => 'MappingList', ], ], ], 'GetPartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValues', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValues' => [ 'shape' => 'ValueStringList', ], ], ], 'GetPartitionResponse' => [ 'type' => 'structure', 'members' => [ 'Partition' => [ 'shape' => 'Partition', ], ], ], 'GetPartitionsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'Expression' => [ 'shape' => 'PredicateString', ], 'NextToken' => [ 'shape' => 'Token', ], 'Segment' => [ 'shape' => 'Segment', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetPartitionsResponse' => [ 'type' => 'structure', 'members' => [ 'Partitions' => [ 'shape' => 'PartitionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetPlanRequest' => [ 'type' => 'structure', 'required' => [ 'Mapping', 'Source', ], 'members' => [ 'Mapping' => [ 'shape' => 'MappingList', ], 'Source' => [ 'shape' => 'CatalogEntry', ], 'Sinks' => [ 'shape' => 'CatalogEntries', ], 'Location' => [ 'shape' => 'Location', ], 'Language' => [ 'shape' => 'Language', ], ], ], 'GetPlanResponse' => [ 'type' => 'structure', 'members' => [ 'PythonScript' => [ 'shape' => 'PythonScript', ], 'ScalaCode' => [ 'shape' => 'ScalaCode', ], ], ], 'GetTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Name', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetTableResponse' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'Table', ], ], ], 'GetTableVersionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'GetTableVersionResponse' => [ 'type' => 'structure', 'members' => [ 'TableVersion' => [ 'shape' => 'TableVersion', ], ], ], 'GetTableVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableVersion', ], ], 'GetTableVersionsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetTableVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'TableVersions' => [ 'shape' => 'GetTableVersionsList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetTablesRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Expression' => [ 'shape' => 'FilterString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetTablesResponse' => [ 'type' => 'structure', 'members' => [ 'TableList' => [ 'shape' => 'TableList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GetTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'GetTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Trigger' => [ 'shape' => 'Trigger', ], ], ], 'GetTriggersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'GenericString', ], 'DependentJobName' => [ 'shape' => 'NameString', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetTriggersResponse' => [ 'type' => 'structure', 'members' => [ 'Triggers' => [ 'shape' => 'TriggerList', ], 'NextToken' => [ 'shape' => 'GenericString', ], ], ], 'GetUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionName', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionName' => [ 'shape' => 'NameString', ], ], ], 'GetUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [ 'UserDefinedFunction' => [ 'shape' => 'UserDefinedFunction', ], ], ], 'GetUserDefinedFunctionsRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'Pattern', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Pattern' => [ 'shape' => 'NameString', ], 'NextToken' => [ 'shape' => 'Token', ], 'MaxResults' => [ 'shape' => 'PageSize', ], ], ], 'GetUserDefinedFunctionsResponse' => [ 'type' => 'structure', 'members' => [ 'UserDefinedFunctions' => [ 'shape' => 'UserDefinedFunctionList', ], 'NextToken' => [ 'shape' => 'Token', ], ], ], 'GrokClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', 'Classification', 'GrokPattern', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'GrokPattern' => [ 'shape' => 'GrokPattern', ], 'CustomPatterns' => [ 'shape' => 'CustomPatterns', ], ], ], 'GrokPattern' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\t]*', ], 'IdString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'IdempotentParameterMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ImportCatalogToGlueRequest' => [ 'type' => 'structure', 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], ], ], 'ImportCatalogToGlueResponse' => [ 'type' => 'structure', 'members' => [], ], 'Integer' => [ 'type' => 'integer', ], 'IntegerFlag' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'IntegerValue' => [ 'type' => 'integer', ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, 'fault' => true, ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'JdbcTarget' => [ 'type' => 'structure', 'members' => [ 'ConnectionName' => [ 'shape' => 'ConnectionName', ], 'Path' => [ 'shape' => 'Path', ], 'Exclusions' => [ 'shape' => 'PathList', ], ], ], 'JdbcTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JdbcTarget', ], ], 'Job' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'LogUri' => [ 'shape' => 'UriString', ], 'Role' => [ 'shape' => 'RoleString', ], 'CreatedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'ExecutionProperty' => [ 'shape' => 'ExecutionProperty', ], 'Command' => [ 'shape' => 'JobCommand', ], 'DefaultArguments' => [ 'shape' => 'GenericMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], ], ], 'JobBookmarkEntry' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], 'Version' => [ 'shape' => 'IntegerValue', ], 'Run' => [ 'shape' => 'IntegerValue', ], 'Attempt' => [ 'shape' => 'IntegerValue', ], 'JobBookmark' => [ 'shape' => 'JsonValue', ], ], ], 'JobCommand' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'GenericString', ], 'ScriptLocation' => [ 'shape' => 'ScriptLocationString', ], ], ], 'JobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'JobName' => [ 'type' => 'string', ], 'JobRun' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'IdString', ], 'Attempt' => [ 'shape' => 'AttemptCount', ], 'PreviousRunId' => [ 'shape' => 'IdString', ], 'TriggerName' => [ 'shape' => 'NameString', ], 'JobName' => [ 'shape' => 'NameString', ], 'StartedOn' => [ 'shape' => 'TimestampValue', ], 'LastModifiedOn' => [ 'shape' => 'TimestampValue', ], 'CompletedOn' => [ 'shape' => 'TimestampValue', ], 'JobRunState' => [ 'shape' => 'JobRunState', ], 'Arguments' => [ 'shape' => 'GenericMap', ], 'ErrorMessage' => [ 'shape' => 'ErrorString', ], 'PredecessorRuns' => [ 'shape' => 'PredecessorList', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', ], 'ExecutionTime' => [ 'shape' => 'ExecutionTime', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], ], ], 'JobRunList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobRun', ], ], 'JobRunState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'RUNNING', 'STOPPING', 'STOPPED', 'SUCCEEDED', 'FAILED', 'TIMEOUT', ], ], 'JobUpdate' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'DescriptionString', ], 'LogUri' => [ 'shape' => 'UriString', ], 'Role' => [ 'shape' => 'RoleString', ], 'ExecutionProperty' => [ 'shape' => 'ExecutionProperty', ], 'Command' => [ 'shape' => 'JobCommand', ], 'DefaultArguments' => [ 'shape' => 'GenericMap', ], 'Connections' => [ 'shape' => 'ConnectionsList', ], 'MaxRetries' => [ 'shape' => 'MaxRetries', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], ], ], 'JsonClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', 'JsonPath', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'JsonPath' => [ 'shape' => 'JsonPath', ], ], ], 'JsonPath' => [ 'type' => 'string', ], 'JsonValue' => [ 'type' => 'string', ], 'KeyString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'Language' => [ 'type' => 'string', 'enum' => [ 'PYTHON', 'SCALA', ], ], 'LastCrawlInfo' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'LastCrawlStatus', ], 'ErrorMessage' => [ 'shape' => 'DescriptionString', ], 'LogGroup' => [ 'shape' => 'LogGroup', ], 'LogStream' => [ 'shape' => 'LogStream', ], 'MessagePrefix' => [ 'shape' => 'MessagePrefix', ], 'StartTime' => [ 'shape' => 'Timestamp', ], ], ], 'LastCrawlStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'CANCELLED', 'FAILED', ], ], 'Location' => [ 'type' => 'structure', 'members' => [ 'Jdbc' => [ 'shape' => 'CodeGenNodeArgs', ], 'S3' => [ 'shape' => 'CodeGenNodeArgs', ], 'DynamoDB' => [ 'shape' => 'CodeGenNodeArgs', ], ], ], 'LocationMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ColumnValuesString', ], 'value' => [ 'shape' => 'ColumnValuesString', ], ], 'LocationString' => [ 'type' => 'string', 'max' => 2056, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'LogGroup' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\.\\-_/#A-Za-z0-9]+', ], 'LogStream' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[^:*]*', ], 'Logical' => [ 'type' => 'string', 'enum' => [ 'AND', 'ANY', ], ], 'LogicalOperator' => [ 'type' => 'string', 'enum' => [ 'EQUALS', ], ], 'MappingEntry' => [ 'type' => 'structure', 'members' => [ 'SourceTable' => [ 'shape' => 'TableName', ], 'SourcePath' => [ 'shape' => 'SchemaPathString', ], 'SourceType' => [ 'shape' => 'FieldType', ], 'TargetTable' => [ 'shape' => 'TableName', ], 'TargetPath' => [ 'shape' => 'SchemaPathString', ], 'TargetType' => [ 'shape' => 'FieldType', ], ], ], 'MappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MappingEntry', ], ], 'MatchCriteria' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 10, 'min' => 0, ], 'MaxConcurrentRuns' => [ 'type' => 'integer', ], 'MaxRetries' => [ 'type' => 'integer', ], 'MessagePrefix' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'MessageString' => [ 'type' => 'string', ], 'MillisecondsCount' => [ 'type' => 'long', ], 'NameString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'NameStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], ], 'NoScheduleException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'NonNegativeDouble' => [ 'type' => 'double', 'min' => 0, ], 'NonNegativeInteger' => [ 'type' => 'integer', 'min' => 0, ], 'NotificationProperty' => [ 'type' => 'structure', 'members' => [ 'NotifyDelayAfter' => [ 'shape' => 'NotifyDelayAfter', ], ], ], 'NotifyDelayAfter' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'OperationTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'Order' => [ 'type' => 'structure', 'required' => [ 'Column', 'SortOrder', ], 'members' => [ 'Column' => [ 'shape' => 'NameString', ], 'SortOrder' => [ 'shape' => 'IntegerFlag', ], ], ], 'OrderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Order', ], ], 'PageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ParametersMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'KeyString', ], 'value' => [ 'shape' => 'ParametersMapValue', ], ], 'ParametersMapValue' => [ 'type' => 'string', 'max' => 512000, ], 'Partition' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], ], ], 'PartitionError' => [ 'type' => 'structure', 'members' => [ 'PartitionValues' => [ 'shape' => 'ValueStringList', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'PartitionErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionError', ], ], 'PartitionInput' => [ 'type' => 'structure', 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], ], ], 'PartitionInputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartitionInput', ], 'max' => 100, 'min' => 0, ], 'PartitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Partition', ], ], 'PartitionValueList' => [ 'type' => 'structure', 'required' => [ 'Values', ], 'members' => [ 'Values' => [ 'shape' => 'ValueStringList', ], ], ], 'Path' => [ 'type' => 'string', ], 'PathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Path', ], ], 'PhysicalConnectionRequirements' => [ 'type' => 'structure', 'members' => [ 'SubnetId' => [ 'shape' => 'NameString', ], 'SecurityGroupIdList' => [ 'shape' => 'SecurityGroupIdList', ], 'AvailabilityZone' => [ 'shape' => 'NameString', ], ], ], 'Predecessor' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'RunId' => [ 'shape' => 'IdString', ], ], ], 'PredecessorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Predecessor', ], ], 'Predicate' => [ 'type' => 'structure', 'members' => [ 'Logical' => [ 'shape' => 'Logical', ], 'Conditions' => [ 'shape' => 'ConditionList', ], ], ], 'PredicateString' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'PrincipalType' => [ 'type' => 'string', 'enum' => [ 'USER', 'ROLE', 'GROUP', ], ], 'PythonScript' => [ 'type' => 'string', ], 'ResetJobBookmarkRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'JobName', ], ], ], 'ResetJobBookmarkResponse' => [ 'type' => 'structure', 'members' => [ 'JobBookmarkEntry' => [ 'shape' => 'JobBookmarkEntry', ], ], ], 'ResourceNumberLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'JAR', 'FILE', 'ARCHIVE', ], ], 'ResourceUri' => [ 'type' => 'structure', 'members' => [ 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Uri' => [ 'shape' => 'URI', ], ], ], 'ResourceUriList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceUri', ], 'max' => 1000, 'min' => 0, ], 'Role' => [ 'type' => 'string', ], 'RoleArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:iam::\\d{12}:role/.*', ], 'RoleString' => [ 'type' => 'string', ], 'RowTag' => [ 'type' => 'string', ], 'S3Target' => [ 'type' => 'structure', 'members' => [ 'Path' => [ 'shape' => 'Path', ], 'Exclusions' => [ 'shape' => 'PathList', ], ], ], 'S3TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Target', ], ], 'ScalaCode' => [ 'type' => 'string', ], 'Schedule' => [ 'type' => 'structure', 'members' => [ 'ScheduleExpression' => [ 'shape' => 'CronExpression', ], 'State' => [ 'shape' => 'ScheduleState', ], ], ], 'ScheduleState' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'NOT_SCHEDULED', 'TRANSITIONING', ], ], 'SchedulerNotRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'SchedulerRunningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'SchedulerTransitioningException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'SchemaChangePolicy' => [ 'type' => 'structure', 'members' => [ 'UpdateBehavior' => [ 'shape' => 'UpdateBehavior', ], 'DeleteBehavior' => [ 'shape' => 'DeleteBehavior', ], ], ], 'SchemaPathString' => [ 'type' => 'string', ], 'ScriptLocationString' => [ 'type' => 'string', ], 'SecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NameString', ], 'max' => 50, 'min' => 0, ], 'Segment' => [ 'type' => 'structure', 'required' => [ 'SegmentNumber', 'TotalSegments', ], 'members' => [ 'SegmentNumber' => [ 'shape' => 'NonNegativeInteger', ], 'TotalSegments' => [ 'shape' => 'TotalSegmentsInteger', ], ], ], 'SerDeInfo' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'SerializationLibrary' => [ 'shape' => 'NameString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], ], ], 'SkewedInfo' => [ 'type' => 'structure', 'members' => [ 'SkewedColumnNames' => [ 'shape' => 'NameStringList', ], 'SkewedColumnValues' => [ 'shape' => 'ColumnValueStringList', ], 'SkewedColumnValueLocationMaps' => [ 'shape' => 'LocationMap', ], ], ], 'StartCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StartCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartCrawlerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], ], ], 'StartCrawlerScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'StartJobRunRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobRunId' => [ 'shape' => 'IdString', ], 'Arguments' => [ 'shape' => 'GenericMap', ], 'AllocatedCapacity' => [ 'shape' => 'IntegerValue', ], 'Timeout' => [ 'shape' => 'Timeout', ], 'NotificationProperty' => [ 'shape' => 'NotificationProperty', ], ], ], 'StartJobRunResponse' => [ 'type' => 'structure', 'members' => [ 'JobRunId' => [ 'shape' => 'IdString', ], ], ], 'StartTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StartTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StopCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StopCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopCrawlerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], ], ], 'StopCrawlerScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StopTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], ], ], 'StorageDescriptor' => [ 'type' => 'structure', 'members' => [ 'Columns' => [ 'shape' => 'ColumnList', ], 'Location' => [ 'shape' => 'LocationString', ], 'InputFormat' => [ 'shape' => 'FormatString', ], 'OutputFormat' => [ 'shape' => 'FormatString', ], 'Compressed' => [ 'shape' => 'Boolean', ], 'NumberOfBuckets' => [ 'shape' => 'Integer', ], 'SerdeInfo' => [ 'shape' => 'SerDeInfo', ], 'BucketColumns' => [ 'shape' => 'NameStringList', ], 'SortColumns' => [ 'shape' => 'OrderList', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'SkewedInfo' => [ 'shape' => 'SkewedInfo', ], 'StoredAsSubDirectories' => [ 'shape' => 'Boolean', ], ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GenericString', ], ], 'Table' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Owner' => [ 'shape' => 'NameString', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'UpdateTime' => [ 'shape' => 'Timestamp', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], 'Retention' => [ 'shape' => 'NonNegativeInteger', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'PartitionKeys' => [ 'shape' => 'ColumnList', ], 'ViewOriginalText' => [ 'shape' => 'ViewTextString', ], 'ViewExpandedText' => [ 'shape' => 'ViewTextString', ], 'TableType' => [ 'shape' => 'TableTypeString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], 'CreatedBy' => [ 'shape' => 'NameString', ], ], ], 'TableError' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'NameString', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'TableErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableError', ], ], 'TableInput' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Owner' => [ 'shape' => 'NameString', ], 'LastAccessTime' => [ 'shape' => 'Timestamp', ], 'LastAnalyzedTime' => [ 'shape' => 'Timestamp', ], 'Retention' => [ 'shape' => 'NonNegativeInteger', ], 'StorageDescriptor' => [ 'shape' => 'StorageDescriptor', ], 'PartitionKeys' => [ 'shape' => 'ColumnList', ], 'ViewOriginalText' => [ 'shape' => 'ViewTextString', ], 'ViewExpandedText' => [ 'shape' => 'ViewTextString', ], 'TableType' => [ 'shape' => 'TableTypeString', ], 'Parameters' => [ 'shape' => 'ParametersMap', ], ], ], 'TableList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Table', ], ], 'TableName' => [ 'type' => 'string', ], 'TablePrefix' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'TableTypeString' => [ 'type' => 'string', 'max' => 255, ], 'TableVersion' => [ 'type' => 'structure', 'members' => [ 'Table' => [ 'shape' => 'Table', ], 'VersionId' => [ 'shape' => 'VersionString', ], ], ], 'TableVersionError' => [ 'type' => 'structure', 'members' => [ 'TableName' => [ 'shape' => 'NameString', ], 'VersionId' => [ 'shape' => 'VersionString', ], 'ErrorDetail' => [ 'shape' => 'ErrorDetail', ], ], ], 'TableVersionErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableVersionError', ], ], 'Timeout' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampValue' => [ 'type' => 'timestamp', ], 'Token' => [ 'type' => 'string', ], 'TotalSegmentsInteger' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'Trigger' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Id' => [ 'shape' => 'IdString', ], 'Type' => [ 'shape' => 'TriggerType', ], 'State' => [ 'shape' => 'TriggerState', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Schedule' => [ 'shape' => 'GenericString', ], 'Actions' => [ 'shape' => 'ActionList', ], 'Predicate' => [ 'shape' => 'Predicate', ], ], ], 'TriggerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trigger', ], ], 'TriggerState' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATED', 'ACTIVATING', 'ACTIVATED', 'DEACTIVATING', 'DEACTIVATED', 'DELETING', 'UPDATING', ], ], 'TriggerType' => [ 'type' => 'string', 'enum' => [ 'SCHEDULED', 'CONDITIONAL', 'ON_DEMAND', ], ], 'TriggerUpdate' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Description' => [ 'shape' => 'DescriptionString', ], 'Schedule' => [ 'shape' => 'GenericString', ], 'Actions' => [ 'shape' => 'ActionList', ], 'Predicate' => [ 'shape' => 'Predicate', ], ], ], 'URI' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\r\\n\\t]*', ], 'UpdateBehavior' => [ 'type' => 'string', 'enum' => [ 'LOG', 'UPDATE_IN_DATABASE', ], ], 'UpdateClassifierRequest' => [ 'type' => 'structure', 'members' => [ 'GrokClassifier' => [ 'shape' => 'UpdateGrokClassifierRequest', ], 'XMLClassifier' => [ 'shape' => 'UpdateXMLClassifierRequest', ], 'JsonClassifier' => [ 'shape' => 'UpdateJsonClassifierRequest', ], ], ], 'UpdateClassifierResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConnectionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'ConnectionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], 'ConnectionInput' => [ 'shape' => 'ConnectionInput', ], ], ], 'UpdateConnectionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCrawlerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Role' => [ 'shape' => 'Role', ], 'DatabaseName' => [ 'shape' => 'DatabaseName', ], 'Description' => [ 'shape' => 'DescriptionStringRemovable', ], 'Targets' => [ 'shape' => 'CrawlerTargets', ], 'Schedule' => [ 'shape' => 'CronExpression', ], 'Classifiers' => [ 'shape' => 'ClassifierNameList', ], 'TablePrefix' => [ 'shape' => 'TablePrefix', ], 'SchemaChangePolicy' => [ 'shape' => 'SchemaChangePolicy', ], 'Configuration' => [ 'shape' => 'CrawlerConfiguration', ], ], ], 'UpdateCrawlerResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCrawlerScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'CrawlerName', ], 'members' => [ 'CrawlerName' => [ 'shape' => 'NameString', ], 'Schedule' => [ 'shape' => 'CronExpression', ], ], ], 'UpdateCrawlerScheduleResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDatabaseRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'DatabaseInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'Name' => [ 'shape' => 'NameString', ], 'DatabaseInput' => [ 'shape' => 'DatabaseInput', ], ], ], 'UpdateDatabaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDevEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'EndpointName', ], 'members' => [ 'EndpointName' => [ 'shape' => 'GenericString', ], 'PublicKey' => [ 'shape' => 'GenericString', ], 'CustomLibraries' => [ 'shape' => 'DevEndpointCustomLibraries', ], 'UpdateEtlLibraries' => [ 'shape' => 'BooleanValue', ], ], ], 'UpdateDevEndpointResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGrokClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'GrokPattern' => [ 'shape' => 'GrokPattern', ], 'CustomPatterns' => [ 'shape' => 'CustomPatterns', ], ], ], 'UpdateJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'JobUpdate', ], 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], 'JobUpdate' => [ 'shape' => 'JobUpdate', ], ], ], 'UpdateJobResponse' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'NameString', ], ], ], 'UpdateJsonClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'JsonPath' => [ 'shape' => 'JsonPath', ], ], ], 'UpdatePartitionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableName', 'PartitionValueList', 'PartitionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableName' => [ 'shape' => 'NameString', ], 'PartitionValueList' => [ 'shape' => 'BoundedPartitionValueList', ], 'PartitionInput' => [ 'shape' => 'PartitionInput', ], ], ], 'UpdatePartitionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTableRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'TableInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'TableInput' => [ 'shape' => 'TableInput', ], 'SkipArchive' => [ 'shape' => 'BooleanNullable', ], ], ], 'UpdateTableResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTriggerRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'TriggerUpdate', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'TriggerUpdate' => [ 'shape' => 'TriggerUpdate', ], ], ], 'UpdateTriggerResponse' => [ 'type' => 'structure', 'members' => [ 'Trigger' => [ 'shape' => 'Trigger', ], ], ], 'UpdateUserDefinedFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'DatabaseName', 'FunctionName', 'FunctionInput', ], 'members' => [ 'CatalogId' => [ 'shape' => 'CatalogIdString', ], 'DatabaseName' => [ 'shape' => 'NameString', ], 'FunctionName' => [ 'shape' => 'NameString', ], 'FunctionInput' => [ 'shape' => 'UserDefinedFunctionInput', ], ], ], 'UpdateUserDefinedFunctionResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateXMLClassifierRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'RowTag' => [ 'shape' => 'RowTag', ], ], ], 'UriString' => [ 'type' => 'string', ], 'UserDefinedFunction' => [ 'type' => 'structure', 'members' => [ 'FunctionName' => [ 'shape' => 'NameString', ], 'ClassName' => [ 'shape' => 'NameString', ], 'OwnerName' => [ 'shape' => 'NameString', ], 'OwnerType' => [ 'shape' => 'PrincipalType', ], 'CreateTime' => [ 'shape' => 'Timestamp', ], 'ResourceUris' => [ 'shape' => 'ResourceUriList', ], ], ], 'UserDefinedFunctionInput' => [ 'type' => 'structure', 'members' => [ 'FunctionName' => [ 'shape' => 'NameString', ], 'ClassName' => [ 'shape' => 'NameString', ], 'OwnerName' => [ 'shape' => 'NameString', ], 'OwnerType' => [ 'shape' => 'PrincipalType', ], 'ResourceUris' => [ 'shape' => 'ResourceUriList', ], ], ], 'UserDefinedFunctionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserDefinedFunction', ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'ValueString' => [ 'type' => 'string', 'max' => 1024, ], 'ValueStringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValueString', ], ], 'VersionId' => [ 'type' => 'long', ], 'VersionMismatchException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'MessageString', ], ], 'exception' => true, ], 'VersionString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*', ], 'ViewTextString' => [ 'type' => 'string', 'max' => 409600, ], 'XMLClassifier' => [ 'type' => 'structure', 'required' => [ 'Name', 'Classification', ], 'members' => [ 'Name' => [ 'shape' => 'NameString', ], 'Classification' => [ 'shape' => 'Classification', ], 'CreationTime' => [ 'shape' => 'Timestamp', ], 'LastUpdated' => [ 'shape' => 'Timestamp', ], 'Version' => [ 'shape' => 'VersionId', ], 'RowTag' => [ 'shape' => 'RowTag', ], ], ], ],];
