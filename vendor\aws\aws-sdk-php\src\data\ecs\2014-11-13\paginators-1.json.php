<?php
// This file was auto-generated from sdk-root/src/data/ecs/2014-11-13/paginators-1.json
return [ 'pagination' => [ 'ListClusters' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'clusterArns', ], 'ListContainerInstances' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'containerInstanceArns', ], 'ListServices' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'serviceArns', ], 'ListTaskDefinitionFamilies' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'families', ], 'ListTaskDefinitions' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'taskDefinitionArns', ], 'ListTasks' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', 'result_key' => 'taskArns', ], ],];
