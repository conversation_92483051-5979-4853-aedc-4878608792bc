<?php
// This file was auto-generated from sdk-root/src/data/codecommit/2015-04-13/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-04-13', 'endpointPrefix' => 'codecommit', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'CodeCommit', 'serviceFullName' => 'AWS CodeCommit', 'serviceId' => 'CodeCommit', 'signatureVersion' => 'v4', 'targetPrefix' => 'CodeCommit_20150413', 'uid' => 'codecommit-2015-04-13', ], 'operations' => [ 'BatchGetRepositories' => [ 'name' => 'BatchGetRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetRepositoriesInput', ], 'output' => [ 'shape' => 'BatchGetRepositoriesOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNamesRequiredException', ], [ 'shape' => 'MaximumRepositoryNamesExceededException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'CreateBranch' => [ 'name' => 'CreateBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBranchInput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'BranchNameExistsException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'CreatePullRequest' => [ 'name' => 'CreatePullRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePullRequestInput', ], 'output' => [ 'shape' => 'CreatePullRequestOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'ClientRequestTokenRequiredException', ], [ 'shape' => 'InvalidClientRequestTokenException', ], [ 'shape' => 'IdempotencyParameterMismatchException', ], [ 'shape' => 'ReferenceNameRequiredException', ], [ 'shape' => 'InvalidReferenceNameException', ], [ 'shape' => 'ReferenceDoesNotExistException', ], [ 'shape' => 'ReferenceTypeNotSupportedException', ], [ 'shape' => 'TitleRequiredException', ], [ 'shape' => 'InvalidTitleException', ], [ 'shape' => 'InvalidDescriptionException', ], [ 'shape' => 'TargetsRequiredException', ], [ 'shape' => 'InvalidTargetsException', ], [ 'shape' => 'TargetRequiredException', ], [ 'shape' => 'InvalidTargetException', ], [ 'shape' => 'MultipleRepositoriesInPullRequestException', ], [ 'shape' => 'MaximumOpenPullRequestsExceededException', ], [ 'shape' => 'SourceAndDestinationAreSameException', ], ], ], 'CreateRepository' => [ 'name' => 'CreateRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRepositoryInput', ], 'output' => [ 'shape' => 'CreateRepositoryOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameExistsException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'InvalidRepositoryDescriptionException', ], [ 'shape' => 'RepositoryLimitExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DeleteBranch' => [ 'name' => 'DeleteBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBranchInput', ], 'output' => [ 'shape' => 'DeleteBranchOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'DefaultBranchCannotBeDeletedException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DeleteCommentContent' => [ 'name' => 'DeleteCommentContent', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCommentContentInput', ], 'output' => [ 'shape' => 'DeleteCommentContentOutput', ], 'errors' => [ [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], [ 'shape' => 'CommentDeletedException', ], ], ], 'DeleteRepository' => [ 'name' => 'DeleteRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRepositoryInput', ], 'output' => [ 'shape' => 'DeleteRepositoryOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'DescribePullRequestEvents' => [ 'name' => 'DescribePullRequestEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePullRequestEventsInput', ], 'output' => [ 'shape' => 'DescribePullRequestEventsOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidPullRequestEventTypeException', ], [ 'shape' => 'InvalidActorArnException', ], [ 'shape' => 'ActorDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetBlob' => [ 'name' => 'GetBlob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlobInput', ], 'output' => [ 'shape' => 'GetBlobOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'BlobIdRequiredException', ], [ 'shape' => 'InvalidBlobIdException', ], [ 'shape' => 'BlobIdDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'FileTooLargeException', ], ], ], 'GetBranch' => [ 'name' => 'GetBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBranchInput', ], 'output' => [ 'shape' => 'GetBranchOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetComment' => [ 'name' => 'GetComment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommentInput', ], 'output' => [ 'shape' => 'GetCommentOutput', ], 'errors' => [ [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], [ 'shape' => 'CommentDeletedException', ], ], ], 'GetCommentsForComparedCommit' => [ 'name' => 'GetCommentsForComparedCommit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommentsForComparedCommitInput', ], 'output' => [ 'shape' => 'GetCommentsForComparedCommitOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetCommentsForPullRequest' => [ 'name' => 'GetCommentsForPullRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommentsForPullRequestInput', ], 'output' => [ 'shape' => 'GetCommentsForPullRequestOutput', ], 'errors' => [ [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'RepositoryNotAssociatedWithPullRequestException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetCommit' => [ 'name' => 'GetCommit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCommitInput', ], 'output' => [ 'shape' => 'GetCommitOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'CommitIdDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetDifferences' => [ 'name' => 'GetDifferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDifferencesInput', ], 'output' => [ 'shape' => 'GetDifferencesOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'PathDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetMergeConflicts' => [ 'name' => 'GetMergeConflicts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetMergeConflictsInput', ], 'output' => [ 'shape' => 'GetMergeConflictsOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'MergeOptionRequiredException', ], [ 'shape' => 'InvalidMergeOptionException', ], [ 'shape' => 'InvalidDestinationCommitSpecifierException', ], [ 'shape' => 'InvalidSourceCommitSpecifierException', ], [ 'shape' => 'CommitRequiredException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidCommitException', ], [ 'shape' => 'TipsDivergenceExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetPullRequest' => [ 'name' => 'GetPullRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPullRequestInput', ], 'output' => [ 'shape' => 'GetPullRequestOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetRepository' => [ 'name' => 'GetRepository', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRepositoryInput', ], 'output' => [ 'shape' => 'GetRepositoryOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'GetRepositoryTriggers' => [ 'name' => 'GetRepositoryTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRepositoryTriggersInput', ], 'output' => [ 'shape' => 'GetRepositoryTriggersOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'ListBranches' => [ 'name' => 'ListBranches', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBranchesInput', ], 'output' => [ 'shape' => 'ListBranchesOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'InvalidContinuationTokenException', ], ], ], 'ListPullRequests' => [ 'name' => 'ListPullRequests', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPullRequestsInput', ], 'output' => [ 'shape' => 'ListPullRequestsOutput', ], 'errors' => [ [ 'shape' => 'InvalidPullRequestStatusException', ], [ 'shape' => 'InvalidAuthorArnException', ], [ 'shape' => 'AuthorDoesNotExistException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidMaxResultsException', ], [ 'shape' => 'InvalidContinuationTokenException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'ListRepositories' => [ 'name' => 'ListRepositories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListRepositoriesInput', ], 'output' => [ 'shape' => 'ListRepositoriesOutput', ], 'errors' => [ [ 'shape' => 'InvalidSortByException', ], [ 'shape' => 'InvalidOrderException', ], [ 'shape' => 'InvalidContinuationTokenException', ], ], ], 'MergePullRequestByFastForward' => [ 'name' => 'MergePullRequestByFastForward', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'MergePullRequestByFastForwardInput', ], 'output' => [ 'shape' => 'MergePullRequestByFastForwardOutput', ], 'errors' => [ [ 'shape' => 'ManualMergeRequiredException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'TipOfSourceReferenceIsDifferentException', ], [ 'shape' => 'ReferenceDoesNotExistException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'PostCommentForComparedCommit' => [ 'name' => 'PostCommentForComparedCommit', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PostCommentForComparedCommitInput', ], 'output' => [ 'shape' => 'PostCommentForComparedCommitOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'ClientRequestTokenRequiredException', ], [ 'shape' => 'InvalidClientRequestTokenException', ], [ 'shape' => 'IdempotencyParameterMismatchException', ], [ 'shape' => 'CommentContentRequiredException', ], [ 'shape' => 'CommentContentSizeLimitExceededException', ], [ 'shape' => 'InvalidFileLocationException', ], [ 'shape' => 'InvalidRelativeFileVersionEnumException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidFilePositionException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'BeforeCommitIdAndAfterCommitIdAreSameException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'PathDoesNotExistException', ], ], 'idempotent' => true, ], 'PostCommentForPullRequest' => [ 'name' => 'PostCommentForPullRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PostCommentForPullRequestInput', ], 'output' => [ 'shape' => 'PostCommentForPullRequestOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'RepositoryNotAssociatedWithPullRequestException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'ClientRequestTokenRequiredException', ], [ 'shape' => 'InvalidClientRequestTokenException', ], [ 'shape' => 'IdempotencyParameterMismatchException', ], [ 'shape' => 'CommentContentRequiredException', ], [ 'shape' => 'CommentContentSizeLimitExceededException', ], [ 'shape' => 'InvalidFileLocationException', ], [ 'shape' => 'InvalidRelativeFileVersionEnumException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidFilePositionException', ], [ 'shape' => 'CommitIdRequiredException', ], [ 'shape' => 'InvalidCommitIdException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'CommitDoesNotExistException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'PathDoesNotExistException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'BeforeCommitIdAndAfterCommitIdAreSameException', ], ], 'idempotent' => true, ], 'PostCommentReply' => [ 'name' => 'PostCommentReply', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PostCommentReplyInput', ], 'output' => [ 'shape' => 'PostCommentReplyOutput', ], 'errors' => [ [ 'shape' => 'ClientRequestTokenRequiredException', ], [ 'shape' => 'InvalidClientRequestTokenException', ], [ 'shape' => 'IdempotencyParameterMismatchException', ], [ 'shape' => 'CommentContentRequiredException', ], [ 'shape' => 'CommentContentSizeLimitExceededException', ], [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], ], 'idempotent' => true, ], 'PutFile' => [ 'name' => 'PutFile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutFileInput', ], 'output' => [ 'shape' => 'PutFileOutput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'ParentCommitIdRequiredException', ], [ 'shape' => 'InvalidParentCommitIdException', ], [ 'shape' => 'ParentCommitDoesNotExistException', ], [ 'shape' => 'ParentCommitIdOutdatedException', ], [ 'shape' => 'FileContentRequiredException', ], [ 'shape' => 'FileContentSizeLimitExceededException', ], [ 'shape' => 'PathRequiredException', ], [ 'shape' => 'InvalidPathException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'BranchNameIsTagNameException', ], [ 'shape' => 'InvalidFileModeException', ], [ 'shape' => 'NameLengthExceededException', ], [ 'shape' => 'InvalidEmailException', ], [ 'shape' => 'CommitMessageLengthExceededException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], [ 'shape' => 'SameFileContentException', ], [ 'shape' => 'FileNameConflictsWithDirectoryNameException', ], [ 'shape' => 'DirectoryNameConflictsWithFileNameException', ], ], ], 'PutRepositoryTriggers' => [ 'name' => 'PutRepositoryTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRepositoryTriggersInput', ], 'output' => [ 'shape' => 'PutRepositoryTriggersOutput', ], 'errors' => [ [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryTriggersListRequiredException', ], [ 'shape' => 'MaximumRepositoryTriggersExceededException', ], [ 'shape' => 'InvalidRepositoryTriggerNameException', ], [ 'shape' => 'InvalidRepositoryTriggerDestinationArnException', ], [ 'shape' => 'InvalidRepositoryTriggerRegionException', ], [ 'shape' => 'InvalidRepositoryTriggerCustomDataException', ], [ 'shape' => 'MaximumBranchesExceededException', ], [ 'shape' => 'InvalidRepositoryTriggerBranchNameException', ], [ 'shape' => 'InvalidRepositoryTriggerEventsException', ], [ 'shape' => 'RepositoryTriggerNameRequiredException', ], [ 'shape' => 'RepositoryTriggerDestinationArnRequiredException', ], [ 'shape' => 'RepositoryTriggerBranchNameListRequiredException', ], [ 'shape' => 'RepositoryTriggerEventsListRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'TestRepositoryTriggers' => [ 'name' => 'TestRepositoryTriggers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestRepositoryTriggersInput', ], 'output' => [ 'shape' => 'TestRepositoryTriggersOutput', ], 'errors' => [ [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'RepositoryTriggersListRequiredException', ], [ 'shape' => 'MaximumRepositoryTriggersExceededException', ], [ 'shape' => 'InvalidRepositoryTriggerNameException', ], [ 'shape' => 'InvalidRepositoryTriggerDestinationArnException', ], [ 'shape' => 'InvalidRepositoryTriggerRegionException', ], [ 'shape' => 'InvalidRepositoryTriggerCustomDataException', ], [ 'shape' => 'MaximumBranchesExceededException', ], [ 'shape' => 'InvalidRepositoryTriggerBranchNameException', ], [ 'shape' => 'InvalidRepositoryTriggerEventsException', ], [ 'shape' => 'RepositoryTriggerNameRequiredException', ], [ 'shape' => 'RepositoryTriggerDestinationArnRequiredException', ], [ 'shape' => 'RepositoryTriggerBranchNameListRequiredException', ], [ 'shape' => 'RepositoryTriggerEventsListRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdateComment' => [ 'name' => 'UpdateComment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCommentInput', ], 'output' => [ 'shape' => 'UpdateCommentOutput', ], 'errors' => [ [ 'shape' => 'CommentContentRequiredException', ], [ 'shape' => 'CommentContentSizeLimitExceededException', ], [ 'shape' => 'CommentDoesNotExistException', ], [ 'shape' => 'CommentIdRequiredException', ], [ 'shape' => 'InvalidCommentIdException', ], [ 'shape' => 'CommentNotCreatedByCallerException', ], [ 'shape' => 'CommentDeletedException', ], ], ], 'UpdateDefaultBranch' => [ 'name' => 'UpdateDefaultBranch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDefaultBranchInput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'BranchNameRequiredException', ], [ 'shape' => 'InvalidBranchNameException', ], [ 'shape' => 'BranchDoesNotExistException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdatePullRequestDescription' => [ 'name' => 'UpdatePullRequestDescription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePullRequestDescriptionInput', ], 'output' => [ 'shape' => 'UpdatePullRequestDescriptionOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidDescriptionException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], ], ], 'UpdatePullRequestStatus' => [ 'name' => 'UpdatePullRequestStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePullRequestStatusInput', ], 'output' => [ 'shape' => 'UpdatePullRequestStatusOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'InvalidPullRequestStatusUpdateException', ], [ 'shape' => 'InvalidPullRequestStatusException', ], [ 'shape' => 'PullRequestStatusRequiredException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdatePullRequestTitle' => [ 'name' => 'UpdatePullRequestTitle', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePullRequestTitleInput', ], 'output' => [ 'shape' => 'UpdatePullRequestTitleOutput', ], 'errors' => [ [ 'shape' => 'PullRequestDoesNotExistException', ], [ 'shape' => 'InvalidPullRequestIdException', ], [ 'shape' => 'PullRequestIdRequiredException', ], [ 'shape' => 'TitleRequiredException', ], [ 'shape' => 'InvalidTitleException', ], [ 'shape' => 'PullRequestAlreadyClosedException', ], ], ], 'UpdateRepositoryDescription' => [ 'name' => 'UpdateRepositoryDescription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRepositoryDescriptionInput', ], 'errors' => [ [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'InvalidRepositoryNameException', ], [ 'shape' => 'InvalidRepositoryDescriptionException', ], [ 'shape' => 'EncryptionIntegrityChecksFailedException', ], [ 'shape' => 'EncryptionKeyAccessDeniedException', ], [ 'shape' => 'EncryptionKeyDisabledException', ], [ 'shape' => 'EncryptionKeyNotFoundException', ], [ 'shape' => 'EncryptionKeyUnavailableException', ], ], ], 'UpdateRepositoryName' => [ 'name' => 'UpdateRepositoryName', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRepositoryNameInput', ], 'errors' => [ [ 'shape' => 'RepositoryDoesNotExistException', ], [ 'shape' => 'RepositoryNameExistsException', ], [ 'shape' => 'RepositoryNameRequiredException', ], [ 'shape' => 'InvalidRepositoryNameException', ], ], ], ], 'shapes' => [ 'AccountId' => [ 'type' => 'string', ], 'ActorDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AdditionalData' => [ 'type' => 'string', ], 'Arn' => [ 'type' => 'string', ], 'AuthorDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BatchGetRepositoriesInput' => [ 'type' => 'structure', 'required' => [ 'repositoryNames', ], 'members' => [ 'repositoryNames' => [ 'shape' => 'RepositoryNameList', ], ], ], 'BatchGetRepositoriesOutput' => [ 'type' => 'structure', 'members' => [ 'repositories' => [ 'shape' => 'RepositoryMetadataList', ], 'repositoriesNotFound' => [ 'shape' => 'RepositoryNotFoundList', ], ], ], 'BeforeCommitIdAndAfterCommitIdAreSameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BlobIdDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BlobIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BlobMetadata' => [ 'type' => 'structure', 'members' => [ 'blobId' => [ 'shape' => 'ObjectId', ], 'path' => [ 'shape' => 'Path', ], 'mode' => [ 'shape' => 'Mode', ], ], ], 'BranchDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BranchInfo' => [ 'type' => 'structure', 'members' => [ 'branchName' => [ 'shape' => 'BranchName', ], 'commitId' => [ 'shape' => 'CommitId', ], ], ], 'BranchName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'BranchNameExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BranchNameIsTagNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'BranchNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BranchName', ], ], 'BranchNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ChangeTypeEnum' => [ 'type' => 'string', 'enum' => [ 'A', 'M', 'D', ], ], 'ClientRequestToken' => [ 'type' => 'string', ], 'ClientRequestTokenRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CloneUrlHttp' => [ 'type' => 'string', ], 'CloneUrlSsh' => [ 'type' => 'string', ], 'Comment' => [ 'type' => 'structure', 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], 'content' => [ 'shape' => 'Content', ], 'inReplyTo' => [ 'shape' => 'CommentId', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'authorArn' => [ 'shape' => 'Arn', ], 'deleted' => [ 'shape' => 'IsCommentDeleted', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'CommentContentRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentContentSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentDeletedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentId' => [ 'type' => 'string', ], 'CommentIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommentNotCreatedByCallerException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Comments' => [ 'type' => 'list', 'member' => [ 'shape' => 'Comment', ], ], 'CommentsForComparedCommit' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'beforeBlobId' => [ 'shape' => 'ObjectId', ], 'afterBlobId' => [ 'shape' => 'ObjectId', ], 'location' => [ 'shape' => 'Location', ], 'comments' => [ 'shape' => 'Comments', ], ], ], 'CommentsForComparedCommitData' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommentsForComparedCommit', ], ], 'CommentsForPullRequest' => [ 'type' => 'structure', 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'beforeBlobId' => [ 'shape' => 'ObjectId', ], 'afterBlobId' => [ 'shape' => 'ObjectId', ], 'location' => [ 'shape' => 'Location', ], 'comments' => [ 'shape' => 'Comments', ], ], ], 'CommentsForPullRequestData' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommentsForPullRequest', ], ], 'Commit' => [ 'type' => 'structure', 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], 'parents' => [ 'shape' => 'ParentList', ], 'message' => [ 'shape' => 'Message', ], 'author' => [ 'shape' => 'UserInfo', ], 'committer' => [ 'shape' => 'UserInfo', ], 'additionalData' => [ 'shape' => 'AdditionalData', ], ], ], 'CommitDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitId' => [ 'type' => 'string', ], 'CommitIdDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitMessageLengthExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'CommitName' => [ 'type' => 'string', ], 'CommitRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Content' => [ 'type' => 'string', ], 'CreateBranchInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'branchName', 'commitId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], 'commitId' => [ 'shape' => 'CommitId', ], ], ], 'CreatePullRequestInput' => [ 'type' => 'structure', 'required' => [ 'title', 'targets', ], 'members' => [ 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'targets' => [ 'shape' => 'TargetList', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreatePullRequestOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'CreateRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryDescription' => [ 'shape' => 'RepositoryDescription', ], ], ], 'CreateRepositoryOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryMetadata' => [ 'shape' => 'RepositoryMetadata', ], ], ], 'CreationDate' => [ 'type' => 'timestamp', ], 'Date' => [ 'type' => 'string', ], 'DefaultBranchCannotBeDeletedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'DeleteBranchInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'branchName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], ], ], 'DeleteBranchOutput' => [ 'type' => 'structure', 'members' => [ 'deletedBranch' => [ 'shape' => 'BranchInfo', ], ], ], 'DeleteCommentContentInput' => [ 'type' => 'structure', 'required' => [ 'commentId', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], ], ], 'DeleteCommentContentOutput' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'Comment', ], ], ], 'DeleteRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'DeleteRepositoryOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryId' => [ 'shape' => 'RepositoryId', ], ], ], 'DescribePullRequestEventsInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'pullRequestEventType' => [ 'shape' => 'PullRequestEventType', ], 'actorArn' => [ 'shape' => 'Arn', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribePullRequestEventsOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequestEvents', ], 'members' => [ 'pullRequestEvents' => [ 'shape' => 'PullRequestEventList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 10240, ], 'Difference' => [ 'type' => 'structure', 'members' => [ 'beforeBlob' => [ 'shape' => 'BlobMetadata', ], 'afterBlob' => [ 'shape' => 'BlobMetadata', ], 'changeType' => [ 'shape' => 'ChangeTypeEnum', ], ], ], 'DifferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Difference', ], ], 'DirectoryNameConflictsWithFileNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Email' => [ 'type' => 'string', ], 'EncryptionIntegrityChecksFailedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, 'fault' => true, ], 'EncryptionKeyAccessDeniedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EncryptionKeyDisabledException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EncryptionKeyNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EncryptionKeyUnavailableException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'EventDate' => [ 'type' => 'timestamp', ], 'FileContent' => [ 'type' => 'blob', 'max' => 6291456, ], 'FileContentRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileContentSizeLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileModeTypeEnum' => [ 'type' => 'string', 'enum' => [ 'EXECUTABLE', 'NORMAL', 'SYMLINK', ], ], 'FileNameConflictsWithDirectoryNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'FileTooLargeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'GetBlobInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'blobId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'blobId' => [ 'shape' => 'ObjectId', ], ], ], 'GetBlobOutput' => [ 'type' => 'structure', 'required' => [ 'content', ], 'members' => [ 'content' => [ 'shape' => 'blob', ], ], ], 'GetBranchInput' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], ], ], 'GetBranchOutput' => [ 'type' => 'structure', 'members' => [ 'branch' => [ 'shape' => 'BranchInfo', ], ], ], 'GetCommentInput' => [ 'type' => 'structure', 'required' => [ 'commentId', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], ], ], 'GetCommentOutput' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'Comment', ], ], ], 'GetCommentsForComparedCommitInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'afterCommitId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetCommentsForComparedCommitOutput' => [ 'type' => 'structure', 'members' => [ 'commentsForComparedCommitData' => [ 'shape' => 'CommentsForComparedCommitData', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCommentsForPullRequestInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetCommentsForPullRequestOutput' => [ 'type' => 'structure', 'members' => [ 'commentsForPullRequestData' => [ 'shape' => 'CommentsForPullRequestData', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetCommitInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'commitId', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'commitId' => [ 'shape' => 'ObjectId', ], ], ], 'GetCommitOutput' => [ 'type' => 'structure', 'required' => [ 'commit', ], 'members' => [ 'commit' => [ 'shape' => 'Commit', ], ], ], 'GetDifferencesInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'afterCommitSpecifier', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitSpecifier' => [ 'shape' => 'CommitName', ], 'afterCommitSpecifier' => [ 'shape' => 'CommitName', ], 'beforePath' => [ 'shape' => 'Path', ], 'afterPath' => [ 'shape' => 'Path', ], 'MaxResults' => [ 'shape' => 'Limit', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetDifferencesOutput' => [ 'type' => 'structure', 'members' => [ 'differences' => [ 'shape' => 'DifferenceList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetMergeConflictsInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'destinationCommitSpecifier', 'sourceCommitSpecifier', 'mergeOption', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'destinationCommitSpecifier' => [ 'shape' => 'CommitName', ], 'sourceCommitSpecifier' => [ 'shape' => 'CommitName', ], 'mergeOption' => [ 'shape' => 'MergeOptionTypeEnum', ], ], ], 'GetMergeConflictsOutput' => [ 'type' => 'structure', 'required' => [ 'mergeable', 'destinationCommitId', 'sourceCommitId', ], 'members' => [ 'mergeable' => [ 'shape' => 'IsMergeable', ], 'destinationCommitId' => [ 'shape' => 'CommitId', ], 'sourceCommitId' => [ 'shape' => 'CommitId', ], ], ], 'GetPullRequestInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], ], ], 'GetPullRequestOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'GetRepositoryInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'GetRepositoryOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryMetadata' => [ 'shape' => 'RepositoryMetadata', ], ], ], 'GetRepositoryTriggersInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], ], ], 'GetRepositoryTriggersOutput' => [ 'type' => 'structure', 'members' => [ 'configurationId' => [ 'shape' => 'RepositoryTriggersConfigurationId', ], 'triggers' => [ 'shape' => 'RepositoryTriggersList', ], ], ], 'IdempotencyParameterMismatchException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidActorArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidAuthorArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidBlobIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidBranchNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidClientRequestTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidCommentIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidCommitException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidCommitIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidContinuationTokenException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDescriptionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidDestinationCommitSpecifierException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidEmailException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidFileLocationException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidFileModeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidFilePositionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidMaxResultsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidMergeOptionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidOrderException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidParentCommitIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPathException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPullRequestEventTypeException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPullRequestIdException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPullRequestStatusException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidPullRequestStatusUpdateException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidReferenceNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRelativeFileVersionEnumException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryDescriptionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerBranchNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerCustomDataException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerDestinationArnException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerEventsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerNameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidRepositoryTriggerRegionException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSortByException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidSourceCommitSpecifierException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTargetException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTargetsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidTitleException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'IsCommentDeleted' => [ 'type' => 'boolean', ], 'IsMergeable' => [ 'type' => 'boolean', ], 'IsMerged' => [ 'type' => 'boolean', ], 'LastModifiedDate' => [ 'type' => 'timestamp', ], 'Limit' => [ 'type' => 'integer', 'box' => true, ], 'ListBranchesInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListBranchesOutput' => [ 'type' => 'structure', 'members' => [ 'branches' => [ 'shape' => 'BranchNameList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListPullRequestsInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'authorArn' => [ 'shape' => 'Arn', ], 'pullRequestStatus' => [ 'shape' => 'PullRequestStatusEnum', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPullRequestsOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequestIds', ], 'members' => [ 'pullRequestIds' => [ 'shape' => 'PullRequestIdList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListRepositoriesInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'sortBy' => [ 'shape' => 'SortByEnum', ], 'order' => [ 'shape' => 'OrderEnum', ], ], ], 'ListRepositoriesOutput' => [ 'type' => 'structure', 'members' => [ 'repositories' => [ 'shape' => 'RepositoryNameIdPairList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'Location' => [ 'type' => 'structure', 'members' => [ 'filePath' => [ 'shape' => 'Path', ], 'filePosition' => [ 'shape' => 'Position', ], 'relativeFileVersion' => [ 'shape' => 'RelativeFileVersionEnum', ], ], ], 'ManualMergeRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaxResults' => [ 'type' => 'integer', ], 'MaximumBranchesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumOpenPullRequestsExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumRepositoryNamesExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MaximumRepositoryTriggersExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MergeMetadata' => [ 'type' => 'structure', 'members' => [ 'isMerged' => [ 'shape' => 'IsMerged', ], 'mergedBy' => [ 'shape' => 'Arn', ], ], ], 'MergeOptionRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'MergeOptionTypeEnum' => [ 'type' => 'string', 'enum' => [ 'FAST_FORWARD_MERGE', ], ], 'MergePullRequestByFastForwardInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'repositoryName', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceCommitId' => [ 'shape' => 'CommitId', ], ], ], 'MergePullRequestByFastForwardOutput' => [ 'type' => 'structure', 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'Message' => [ 'type' => 'string', ], 'Mode' => [ 'type' => 'string', ], 'MultipleRepositoriesInPullRequestException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Name' => [ 'type' => 'string', ], 'NameLengthExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', ], 'ObjectId' => [ 'type' => 'string', ], 'OrderEnum' => [ 'type' => 'string', 'enum' => [ 'ascending', 'descending', ], ], 'ParentCommitDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParentCommitIdOutdatedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParentCommitIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ParentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ObjectId', ], ], 'Path' => [ 'type' => 'string', ], 'PathDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PathRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Position' => [ 'type' => 'long', ], 'PostCommentForComparedCommitInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'afterCommitId', 'content', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'location' => [ 'shape' => 'Location', ], 'content' => [ 'shape' => 'Content', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'PostCommentForComparedCommitOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'beforeBlobId' => [ 'shape' => 'ObjectId', ], 'afterBlobId' => [ 'shape' => 'ObjectId', ], 'location' => [ 'shape' => 'Location', ], 'comment' => [ 'shape' => 'Comment', ], ], ], 'PostCommentForPullRequestInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'repositoryName', 'beforeCommitId', 'afterCommitId', 'content', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'location' => [ 'shape' => 'Location', ], 'content' => [ 'shape' => 'Content', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'PostCommentForPullRequestOutput' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], 'beforeBlobId' => [ 'shape' => 'ObjectId', ], 'afterBlobId' => [ 'shape' => 'ObjectId', ], 'location' => [ 'shape' => 'Location', ], 'comment' => [ 'shape' => 'Comment', ], ], ], 'PostCommentReplyInput' => [ 'type' => 'structure', 'required' => [ 'inReplyTo', 'content', ], 'members' => [ 'inReplyTo' => [ 'shape' => 'CommentId', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'content' => [ 'shape' => 'Content', ], ], ], 'PostCommentReplyOutput' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'Comment', ], ], ], 'PullRequest' => [ 'type' => 'structure', 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'title' => [ 'shape' => 'Title', ], 'description' => [ 'shape' => 'Description', ], 'lastActivityDate' => [ 'shape' => 'LastModifiedDate', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'pullRequestStatus' => [ 'shape' => 'PullRequestStatusEnum', ], 'authorArn' => [ 'shape' => 'Arn', ], 'pullRequestTargets' => [ 'shape' => 'PullRequestTargetList', ], 'clientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'PullRequestAlreadyClosedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestEvent' => [ 'type' => 'structure', 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'eventDate' => [ 'shape' => 'EventDate', ], 'pullRequestEventType' => [ 'shape' => 'PullRequestEventType', ], 'actorArn' => [ 'shape' => 'Arn', ], 'pullRequestStatusChangedEventMetadata' => [ 'shape' => 'PullRequestStatusChangedEventMetadata', ], 'pullRequestSourceReferenceUpdatedEventMetadata' => [ 'shape' => 'PullRequestSourceReferenceUpdatedEventMetadata', ], 'pullRequestMergedStateChangedEventMetadata' => [ 'shape' => 'PullRequestMergedStateChangedEventMetadata', ], ], ], 'PullRequestEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PullRequestEvent', ], ], 'PullRequestEventType' => [ 'type' => 'string', 'enum' => [ 'PULL_REQUEST_CREATED', 'PULL_REQUEST_STATUS_CHANGED', 'PULL_REQUEST_SOURCE_REFERENCE_UPDATED', 'PULL_REQUEST_MERGE_STATE_CHANGED', ], ], 'PullRequestId' => [ 'type' => 'string', ], 'PullRequestIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PullRequestId', ], ], 'PullRequestIdRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestMergedStateChangedEventMetadata' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'destinationReference' => [ 'shape' => 'ReferenceName', ], 'mergeMetadata' => [ 'shape' => 'MergeMetadata', ], ], ], 'PullRequestSourceReferenceUpdatedEventMetadata' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'beforeCommitId' => [ 'shape' => 'CommitId', ], 'afterCommitId' => [ 'shape' => 'CommitId', ], ], ], 'PullRequestStatusChangedEventMetadata' => [ 'type' => 'structure', 'members' => [ 'pullRequestStatus' => [ 'shape' => 'PullRequestStatusEnum', ], ], ], 'PullRequestStatusEnum' => [ 'type' => 'string', 'enum' => [ 'OPEN', 'CLOSED', ], ], 'PullRequestStatusRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PullRequestTarget' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceReference' => [ 'shape' => 'ReferenceName', ], 'destinationReference' => [ 'shape' => 'ReferenceName', ], 'destinationCommit' => [ 'shape' => 'CommitId', ], 'sourceCommit' => [ 'shape' => 'CommitId', ], 'mergeMetadata' => [ 'shape' => 'MergeMetadata', ], ], ], 'PullRequestTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PullRequestTarget', ], ], 'PutFileInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'branchName', 'fileContent', 'filePath', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'branchName' => [ 'shape' => 'BranchName', ], 'fileContent' => [ 'shape' => 'FileContent', ], 'filePath' => [ 'shape' => 'Path', ], 'fileMode' => [ 'shape' => 'FileModeTypeEnum', ], 'parentCommitId' => [ 'shape' => 'CommitId', ], 'commitMessage' => [ 'shape' => 'Message', ], 'name' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], ], ], 'PutFileOutput' => [ 'type' => 'structure', 'required' => [ 'commitId', 'blobId', 'treeId', ], 'members' => [ 'commitId' => [ 'shape' => 'ObjectId', ], 'blobId' => [ 'shape' => 'ObjectId', ], 'treeId' => [ 'shape' => 'ObjectId', ], ], ], 'PutRepositoryTriggersInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'triggers', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'triggers' => [ 'shape' => 'RepositoryTriggersList', ], ], ], 'PutRepositoryTriggersOutput' => [ 'type' => 'structure', 'members' => [ 'configurationId' => [ 'shape' => 'RepositoryTriggersConfigurationId', ], ], ], 'ReferenceDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReferenceName' => [ 'type' => 'string', ], 'ReferenceNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ReferenceTypeNotSupportedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RelativeFileVersionEnum' => [ 'type' => 'string', 'enum' => [ 'BEFORE', 'AFTER', ], ], 'RepositoryDescription' => [ 'type' => 'string', 'max' => 1000, ], 'RepositoryDoesNotExistException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryId' => [ 'type' => 'string', ], 'RepositoryLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryMetadata' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'repositoryId' => [ 'shape' => 'RepositoryId', ], 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryDescription' => [ 'shape' => 'RepositoryDescription', ], 'defaultBranch' => [ 'shape' => 'BranchName', ], 'lastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'creationDate' => [ 'shape' => 'CreationDate', ], 'cloneUrlHttp' => [ 'shape' => 'CloneUrlHttp', ], 'cloneUrlSsh' => [ 'shape' => 'CloneUrlSsh', ], 'Arn' => [ 'shape' => 'Arn', ], ], ], 'RepositoryMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryMetadata', ], ], 'RepositoryName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\w\\.-]+', ], 'RepositoryNameExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryNameIdPair' => [ 'type' => 'structure', 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryId' => [ 'shape' => 'RepositoryId', ], ], ], 'RepositoryNameIdPairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryNameIdPair', ], ], 'RepositoryNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryName', ], ], 'RepositoryNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryNamesRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryNotAssociatedWithPullRequestException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryNotFoundList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryName', ], ], 'RepositoryTrigger' => [ 'type' => 'structure', 'required' => [ 'name', 'destinationArn', 'events', ], 'members' => [ 'name' => [ 'shape' => 'RepositoryTriggerName', ], 'destinationArn' => [ 'shape' => 'Arn', ], 'customData' => [ 'shape' => 'RepositoryTriggerCustomData', ], 'branches' => [ 'shape' => 'BranchNameList', ], 'events' => [ 'shape' => 'RepositoryTriggerEventList', ], ], ], 'RepositoryTriggerBranchNameListRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryTriggerCustomData' => [ 'type' => 'string', ], 'RepositoryTriggerDestinationArnRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryTriggerEventEnum' => [ 'type' => 'string', 'enum' => [ 'all', 'updateReference', 'createReference', 'deleteReference', ], ], 'RepositoryTriggerEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryTriggerEventEnum', ], ], 'RepositoryTriggerEventsListRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryTriggerExecutionFailure' => [ 'type' => 'structure', 'members' => [ 'trigger' => [ 'shape' => 'RepositoryTriggerName', ], 'failureMessage' => [ 'shape' => 'RepositoryTriggerExecutionFailureMessage', ], ], ], 'RepositoryTriggerExecutionFailureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryTriggerExecutionFailure', ], ], 'RepositoryTriggerExecutionFailureMessage' => [ 'type' => 'string', ], 'RepositoryTriggerName' => [ 'type' => 'string', ], 'RepositoryTriggerNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryTriggerName', ], ], 'RepositoryTriggerNameRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RepositoryTriggersConfigurationId' => [ 'type' => 'string', ], 'RepositoryTriggersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RepositoryTrigger', ], ], 'RepositoryTriggersListRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SameFileContentException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'SortByEnum' => [ 'type' => 'string', 'enum' => [ 'repositoryName', 'lastModifiedDate', ], ], 'SourceAndDestinationAreSameException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Target' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'sourceReference', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'sourceReference' => [ 'shape' => 'ReferenceName', ], 'destinationReference' => [ 'shape' => 'ReferenceName', ], ], ], 'TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Target', ], ], 'TargetRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TargetsRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TestRepositoryTriggersInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'triggers', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'triggers' => [ 'shape' => 'RepositoryTriggersList', ], ], ], 'TestRepositoryTriggersOutput' => [ 'type' => 'structure', 'members' => [ 'successfulExecutions' => [ 'shape' => 'RepositoryTriggerNameList', ], 'failedExecutions' => [ 'shape' => 'RepositoryTriggerExecutionFailureList', ], ], ], 'TipOfSourceReferenceIsDifferentException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'TipsDivergenceExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'Title' => [ 'type' => 'string', 'max' => 150, ], 'TitleRequiredException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'UpdateCommentInput' => [ 'type' => 'structure', 'required' => [ 'commentId', 'content', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], 'content' => [ 'shape' => 'Content', ], ], ], 'UpdateCommentOutput' => [ 'type' => 'structure', 'members' => [ 'comment' => [ 'shape' => 'Comment', ], ], ], 'UpdateDefaultBranchInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', 'defaultBranchName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'defaultBranchName' => [ 'shape' => 'BranchName', ], ], ], 'UpdatePullRequestDescriptionInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'description', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'description' => [ 'shape' => 'Description', ], ], ], 'UpdatePullRequestDescriptionOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'UpdatePullRequestStatusInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'pullRequestStatus', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'pullRequestStatus' => [ 'shape' => 'PullRequestStatusEnum', ], ], ], 'UpdatePullRequestStatusOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'UpdatePullRequestTitleInput' => [ 'type' => 'structure', 'required' => [ 'pullRequestId', 'title', ], 'members' => [ 'pullRequestId' => [ 'shape' => 'PullRequestId', ], 'title' => [ 'shape' => 'Title', ], ], ], 'UpdatePullRequestTitleOutput' => [ 'type' => 'structure', 'required' => [ 'pullRequest', ], 'members' => [ 'pullRequest' => [ 'shape' => 'PullRequest', ], ], ], 'UpdateRepositoryDescriptionInput' => [ 'type' => 'structure', 'required' => [ 'repositoryName', ], 'members' => [ 'repositoryName' => [ 'shape' => 'RepositoryName', ], 'repositoryDescription' => [ 'shape' => 'RepositoryDescription', ], ], ], 'UpdateRepositoryNameInput' => [ 'type' => 'structure', 'required' => [ 'oldName', 'newName', ], 'members' => [ 'oldName' => [ 'shape' => 'RepositoryName', ], 'newName' => [ 'shape' => 'RepositoryName', ], ], ], 'UserInfo' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Name', ], 'email' => [ 'shape' => 'Email', ], 'date' => [ 'shape' => 'Date', ], ], ], 'blob' => [ 'type' => 'blob', ], ],];
