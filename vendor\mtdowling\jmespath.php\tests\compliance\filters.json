[{"given": {"foo": [{"name": "a"}, {"name": "b"}]}, "cases": [{"comment": "Matching a literal", "expression": "foo[?name == 'a']", "result": [{"name": "a"}]}]}, {"given": {"foo": [0, 1], "bar": [2, 3]}, "cases": [{"comment": "Matching a literal", "expression": "*[?[0] == `0`]", "result": [[], []]}]}, {"given": {"foo": [{"first": "foo", "last": "bar"}, {"first": "foo", "last": "foo"}, {"first": "foo", "last": "baz"}]}, "cases": [{"comment": "Matching an expression", "expression": "foo[?first == last]", "result": [{"first": "foo", "last": "foo"}]}, {"comment": "Verify projection created from filter", "expression": "foo[?first == last].first", "result": ["foo"]}]}, {"given": {"foo": [{"age": 20}, {"age": 25}, {"age": 30}]}, "cases": [{"comment": "Greater than with a number", "expression": "foo[?age > `25`]", "result": [{"age": 30}]}, {"expression": "foo[?age >= `25`]", "result": [{"age": 25}, {"age": 30}]}, {"comment": "Greater than with a number", "expression": "foo[?age > `30`]", "result": []}, {"comment": "Greater than with a number", "expression": "foo[?age < `25`]", "result": [{"age": 20}]}, {"comment": "Greater than with a number", "expression": "foo[?age <= `25`]", "result": [{"age": 20}, {"age": 25}]}, {"comment": "Greater than with a number", "expression": "foo[?age < `20`]", "result": []}, {"expression": "foo[?age == `20`]", "result": [{"age": 20}]}, {"expression": "foo[?age != `20`]", "result": [{"age": 25}, {"age": 30}]}]}, {"given": {"foo": [{"weight": 33.3}, {"weight": 44.4}, {"weight": 55.5}]}, "cases": [{"comment": "Greater than with a number", "expression": "foo[?weight > `44.4`]", "result": [{"weight": 55.5}]}, {"expression": "foo[?weight >= `44.4`]", "result": [{"weight": 44.4}, {"weight": 55.5}]}, {"comment": "Greater than with a number", "expression": "foo[?weight > `55.5`]", "result": []}, {"comment": "Greater than with a number", "expression": "foo[?weight < `44.4`]", "result": [{"weight": 33.3}]}, {"comment": "Greater than with a number", "expression": "foo[?weight <= `44.4`]", "result": [{"weight": 33.3}, {"weight": 44.4}]}, {"comment": "Greater than with a number", "expression": "foo[?weight < `33.3`]", "result": []}, {"expression": "foo[?weight == `33.3`]", "result": [{"weight": 33.3}]}, {"expression": "foo[?weight != `33.3`]", "result": [{"weight": 44.4}, {"weight": 55.5}]}]}, {"given": {"foo": [{"top": {"name": "a"}}, {"top": {"name": "b"}}]}, "cases": [{"comment": "Filter with subexpression", "expression": "foo[?top.name == 'a']", "result": [{"top": {"name": "a"}}]}]}, {"given": {"foo": [{"top": {"first": "foo", "last": "bar"}}, {"top": {"first": "foo", "last": "foo"}}, {"top": {"first": "foo", "last": "baz"}}]}, "cases": [{"comment": "Matching an expression", "expression": "foo[?top.first == top.last]", "result": [{"top": {"first": "foo", "last": "foo"}}]}, {"comment": "Matching a JSON array", "expression": "foo[?top == `{\"first\": \"foo\", \"last\": \"bar\"}`]", "result": [{"top": {"first": "foo", "last": "bar"}}]}]}, {"given": {"foo": [{"key": true}, {"key": false}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}, {"key": {"a": 2}}]}, "cases": [{"expression": "foo[?key == `true`]", "result": [{"key": true}]}, {"expression": "foo[?key == `false`]", "result": [{"key": false}]}, {"expression": "foo[?key == `0`]", "result": [{"key": 0}]}, {"expression": "foo[?key == `1`]", "result": [{"key": 1}]}, {"expression": "foo[?key == `[0]`]", "result": [{"key": [0]}]}, {"expression": "foo[?key == `{\"bar\": [0]}`]", "result": [{"key": {"bar": [0]}}]}, {"expression": "foo[?key == `null`]", "result": [{"key": null}]}, {"expression": "foo[?key == `[1]`]", "result": [{"key": [1]}]}, {"expression": "foo[?key == `{\"a\":2}`]", "result": [{"key": {"a": 2}}]}, {"expression": "foo[?`true` == key]", "result": [{"key": true}]}, {"expression": "foo[?`false` == key]", "result": [{"key": false}]}, {"expression": "foo[?`0` == key]", "result": [{"key": 0}]}, {"expression": "foo[?`1` == key]", "result": [{"key": 1}]}, {"expression": "foo[?`[0]` == key]", "result": [{"key": [0]}]}, {"expression": "foo[?`{\"bar\": [0]}` == key]", "result": [{"key": {"bar": [0]}}]}, {"expression": "foo[?`null` == key]", "result": [{"key": null}]}, {"expression": "foo[?`[1]` == key]", "result": [{"key": [1]}]}, {"expression": "foo[?`{\"a\":2}` == key]", "result": [{"key": {"a": 2}}]}, {"expression": "foo[?key != `true`]", "result": [{"key": false}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?key != `false`]", "result": [{"key": true}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?key != `0`]", "result": [{"key": true}, {"key": false}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?key != `1`]", "result": [{"key": true}, {"key": false}, {"key": 0}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?key != `null`]", "result": [{"key": true}, {"key": false}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?key != `[1]`]", "result": [{"key": true}, {"key": false}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": {"a": 2}}]}, {"expression": "foo[?key != `{\"a\":2}`]", "result": [{"key": true}, {"key": false}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}]}, {"expression": "foo[?`true` != key]", "result": [{"key": false}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?`false` != key]", "result": [{"key": true}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?`0` != key]", "result": [{"key": true}, {"key": false}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?`1` != key]", "result": [{"key": true}, {"key": false}, {"key": 0}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?`null` != key]", "result": [{"key": true}, {"key": false}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": [1]}, {"key": {"a": 2}}]}, {"expression": "foo[?`[1]` != key]", "result": [{"key": true}, {"key": false}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": {"a": 2}}]}, {"expression": "foo[?`{\"a\":2}` != key]", "result": [{"key": true}, {"key": false}, {"key": 0}, {"key": 1}, {"key": [0]}, {"key": {"bar": [0]}}, {"key": null}, {"key": [1]}]}]}, {"given": {"reservations": [{"instances": [{"foo": 1, "bar": 2}, {"foo": 1, "bar": 3}, {"foo": 1, "bar": 2}, {"foo": 2, "bar": 1}]}]}, "cases": [{"expression": "reservations[].instances[?bar==`1`]", "result": [[{"foo": 2, "bar": 1}]]}, {"expression": "reservations[*].instances[?bar==`1`]", "result": [[{"foo": 2, "bar": 1}]]}, {"expression": "reservations[].instances[?bar==`1`][]", "result": [{"foo": 2, "bar": 1}]}]}, {"given": {"baz": "other", "foo": [{"bar": 1}, {"bar": 2}, {"bar": 3}, {"bar": 4}, {"bar": 1, "baz": 2}]}, "cases": [{"expression": "foo[?bar==`1`].bar[0]", "result": []}]}, {"given": {"foo": [{"a": 1, "b": {"c": "x"}}, {"a": 1, "b": {"c": "y"}}, {"a": 1, "b": {"c": "z"}}, {"a": 2, "b": {"c": "z"}}, {"a": 1, "baz": 2}]}, "cases": [{"expression": "foo[?a==`1`].b.c", "result": ["x", "y", "z"]}]}, {"given": {"foo": [{"name": "a"}, {"name": "b"}, {"name": "c"}]}, "cases": [{"comment": "Filter with or expression", "expression": "foo[?name == 'a' || name == 'b']", "result": [{"name": "a"}, {"name": "b"}]}, {"expression": "foo[?name == 'a' || name == 'e']", "result": [{"name": "a"}]}, {"expression": "foo[?name == 'a' || name == 'b' || name == 'c']", "result": [{"name": "a"}, {"name": "b"}, {"name": "c"}]}]}, {"given": {"foo": [{"a": 1, "b": 2}, {"a": 1, "b": 3}]}, "cases": [{"comment": "Filter with and expression", "expression": "foo[?a == `1` && b == `2`]", "result": [{"a": 1, "b": 2}]}, {"expression": "foo[?a == `1` && b == `4`]", "result": []}]}, {"given": {"foo": [{"a": 1, "b": 2, "c": 3}, {"a": 3, "b": 4}]}, "cases": [{"comment": "Filter with Or and And expressions", "expression": "foo[?c == `3` || a == `1` && b == `4`]", "result": [{"a": 1, "b": 2, "c": 3}]}, {"expression": "foo[?b == `2` || a == `3` && b == `4`]", "result": [{"a": 1, "b": 2, "c": 3}, {"a": 3, "b": 4}]}, {"expression": "foo[?a == `3` && b == `4` || b == `2`]", "result": [{"a": 1, "b": 2, "c": 3}, {"a": 3, "b": 4}]}, {"expression": "foo[?(a == `3` && b == `4`) || b == `2`]", "result": [{"a": 1, "b": 2, "c": 3}, {"a": 3, "b": 4}]}, {"expression": "foo[?((a == `3` && b == `4`)) || b == `2`]", "result": [{"a": 1, "b": 2, "c": 3}, {"a": 3, "b": 4}]}, {"expression": "foo[?a == `3` && (b == `4` || b == `2`)]", "result": [{"a": 3, "b": 4}]}, {"expression": "foo[?a == `3` && ((b == `4` || b == `2`))]", "result": [{"a": 3, "b": 4}]}]}, {"given": {"foo": [{"a": 1, "b": 2, "c": 3}, {"a": 3, "b": 4}]}, "cases": [{"comment": "Verify precedence of or/and expressions", "expression": "foo[?a == `1` || b ==`2` && c == `5`]", "result": [{"a": 1, "b": 2, "c": 3}]}, {"comment": "Parentheses can alter precedence", "expression": "foo[?(a == `1` || b ==`2`) && c == `5`]", "result": []}, {"comment": "Not expressions combined with and/or", "expression": "foo[?!(a == `1` || b ==`2`)]", "result": [{"a": 3, "b": 4}]}]}, {"given": {"foo": [{"key": true}, {"key": false}, {"key": []}, {"key": {}}, {"key": [0]}, {"key": {"a": "b"}}, {"key": 0}, {"key": 1}, {"key": null}, {"notkey": true}]}, "cases": [{"comment": "Unary filter expression", "expression": "foo[?key]", "result": [{"key": true}, {"key": [0]}, {"key": {"a": "b"}}, {"key": 0}, {"key": 1}]}, {"comment": "Unary not filter expression", "expression": "foo[?!key]", "result": [{"key": false}, {"key": []}, {"key": {}}, {"key": null}, {"notkey": true}]}, {"comment": "Equality with null RHS", "expression": "foo[?key == `null`]", "result": [{"key": null}, {"notkey": true}]}]}, {"given": {"foo": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}, "cases": [{"comment": "Using @ in a filter expression", "expression": "foo[?@ < `5`]", "result": [0, 1, 2, 3, 4]}, {"comment": "Using @ in a filter expression", "expression": "foo[?`5` > @]", "result": [0, 1, 2, 3, 4]}, {"comment": "Using @ in a filter expression", "expression": "foo[?@ == @]", "result": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}]}]