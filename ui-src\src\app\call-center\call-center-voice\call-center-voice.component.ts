import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Output,
  EventEmitter,
  Input,
  HostBinding,
} from '@angular/core';

export type Devices = MediaDeviceInfo[];

import { select, Store } from '@ngrx/store';
import { Device as TwilioDevice } from '@twilio/voice-sdk';
import * as fromRoot from 'app/store';
import * as fromSession from 'app/store/reducers/session.reducer';
import * as fromVoIPExtensions from 'app/store/reducers/voip-extensions.reducer';
import * as fromConfigurationAction from '../../store/actions/configuration.action';
import * as fromCallCenterVoice from '../../store/reducers/call-center-voice.reducer';
import * as fromCallCenterVoiceAction from 'app/store/actions/call-center-voice.action';
import * as fromCallQueues from 'app/store/reducers/call-queues.reducer';
import * as fromDeviceClient from 'app/store/reducers/device-client.reducer';
import * as fromSyncClientAction from 'app/store/actions/sync-client.action';
import * as fromSystemConfiguration from 'app/store/reducers/system-configuration.reducer';
import * as fromSystemConfigurationActions from 'app/store/actions/system-configuration.action';
import * as fromTrackingNumbers from 'app/store/reducers/tracking-numbers.reducer';
import * as fromWorkerChannels from 'app/store/reducers/worker-channels.reducer';
import * as fromWorkerClient from 'app/store/reducers/worker-client.reducer';
import * as fromWorkspaceActivities from 'app/store/reducers/workspace-activities.reducer';
import * as fromWorkspaceTasks from 'app/store/reducers/workspace-tasks.reducer';
import * as fromWorkspaceWorkers from 'app/store/reducers/workspace-workers.reducer';
import * as fromConfigureCallCenterActions from 'app/store/actions/configure-call-center.action';
import * as fromConfigureCallCenter from 'app/store/reducers/configure-call-center.reducer';
import * as fromCollectionEventsActions from 'app/store/actions/collection-events.action';
import * as fromCallCenterChat from 'app/store/reducers/call-center-chat.reducer';
import * as fromLocationConfig from 'app/store/reducers/location-configuration.reducer';
import * as fromAllLocations from 'app/store/reducers/locations-all.reducer';
import * as fromAgentLeadsAction from 'app/store/actions/agent-leads.action';
import { VideoCommunicationsService } from '../../store/services/video-communications.service';

import { DeviceService } from 'app/store/services/device.service';
import {
  Observable,
  Subscription,
  Subject,
  map,
  distinctUntilChanged,
  takeUntil,
  fromEvent,
  take,
  tap,
  filter,
  forkJoin,
  timer,
  interval,
} from 'rxjs';
import { SessionData } from 'common/swagger-providers/core-api.provider';
import {
  Location,
  TrackingnumberListItem,
} from 'common/swagger-providers/account-api.provider';
import { SysConfig } from 'common/swagger-providers/core-api.provider';
import {
  TwilioWorkerChannel,
  CallCenter,
} from 'common/swagger-providers/mcc-api.provider';

import { UtilsHelper } from 'common/core/utils.helper';

import {
  COOLDOWN_ACTIVITY_FRIENDLY_NAME,
  OFFLINE_ACTIVITY_FRIENDLY_NAME,
  ONCALL_ACTIVITY_FRIENDLY_NAME,
  READY_ACTIVITY_FRIENDLY_NAME,
  REJECTED_ACTIVITY_FRIENDLY_NAME,
  NOANSWER_ACTIVITY_FRIENDLY_NAME,
  BUSY_ACTIVITY_FRIENDLY_NAME,
  NOMIC_ACTIVITY_FRIENDLY_NAME
} from './common';

import { TWILIO_URLS } from 'common/services/twilio-urls.provider';

import { TopToastComponent } from 'common/presentational/top-toast';
import { AWSLambdaProvider } from 'common/services/aws-lambda.provider';
import { TwilioWorkerChannelsProvider } from 'common/twilio/twilio-worker-channels.provider';
import { IFollowupCall } from 'app/models/followup-call.model';
import { SharedService } from '../../app-v2/system-configuration/call-routes/models';
import { TwilioEventHandlerService } from '../twilio-event-handlers.service';
import { ChromeNotifications } from 'common/chrome-notifications';
import { TrackingNumberProvider } from 'common/tracking-number';
import { SystemVariablesProvider } from 'common/system-variables';
import { TwilioProvider } from 'common/twilio';
import { TelephoneFormatPipe } from 'common/pipes/telephone-format.pipe';
import { PHONE_FORMATS } from 'common/core/constants';

import { NotificationsActions as fromNotificationAlertsAction } from '@callpotential/ng-common-display';
import { CallQueuesWorkerProvider } from 'common/callqueues-worker';
import { InternetConnectivityService } from 'common/check-internet-connectivity/internet-connectivity.service';
import { selectLocationsMap } from 'app/store/selectors/locations.selector';
import { Router } from '@angular/router';
import { SessionStorageHelper } from 'common/core/session.helper';
import { datadogLogs } from '@datadog/browser-logs';
import { isCallCommunicationChannel } from 'app/common/communication-channels';
import { environment } from 'environments/environment';
import { CpLoggingService } from '@callpotential/cp-logging-service';
import { CallProvider } from '../../../common/call-data';
import { BugsnagService } from 'app/bugsnag/bugsnag.service';
import { TwilioDeviceService } from '../../services/twilio-device.service';
import { Actions, ofType } from '@ngrx/effects';

const HANG_UP_CALL_REASON = 'hangup';
const TTL_EXCEEDED = 'Task TTL Exceeded';
const SCHEDULED_CALLBACK = 'Scheduled Callback';
const callInProgressKey = 'cpTwilioVideoCallInProgressAgent';
const APP_CONFIG = environment.APP_CONFIG;

@Component({
  selector: 'call-center-voice',
  templateUrl: './call-center-voice.component.html',
  styleUrls: ['./call-center-voice.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [TwilioProvider, CallQueuesWorkerProvider, CallProvider, DeviceService],
})
export class CallCenterVoiceComponent implements OnInit, OnDestroy {
  public agentStates: any = fromCallCenterVoice.AgentStates;

  private ngUnsubscribe: Subject<any> = new Subject();
  public session: SessionData;
  public worker: any;
  public workerClient: any;
  public workerChannels: Array<TwilioWorkerChannel>;
  public workspaceActivities: Array<any>;
  private workspaceWorkers: Array<any>;
  public currentReservation: any;
  private currentConnection: any;
  private currentAgentStatus: any;
  private timeoutHandler: any;
  public hideQueueNameInbound: boolean = false;
  public hideCallerTypeInbound: boolean = false;
  public currentCallState: fromCallCenterVoice.CallStates;

  public callCenterVoice$: Observable<
    fromCallCenterVoice.CallCenterVoiceState
  >;
  private callQueues$: Observable<fromCallQueues.CallQueuesState>;
  private deviceClient$: Observable<fromDeviceClient.DeviceClientState>;
  private session$: Observable<fromSession.SessionState>;
  private systemConfiguration$: Observable<
    fromSystemConfiguration.SystemConfigurationState
  >;
  private trackingNumbers$: Observable<
    fromTrackingNumbers.TrackingNumbersState
  >;
  public voIPExtensions$: Observable<fromVoIPExtensions.VoIPExtensionsState>;
  private workerChannels$: Observable<fromWorkerChannels.WorkerChannelsState>;
  private workerClient$: Observable<fromWorkerClient.WorkerClientState>;
  public workspaceActivities$: Observable<
    fromWorkspaceActivities.WorkspaceActivitiesState
  >;
  public workspaceTasks$: Observable<fromWorkspaceTasks.WorkspaceTasksState>;
  public workspaceWorkers$: Observable<
    fromWorkspaceWorkers.WorkspaceWorkersState
  >;
  public callCenterConfiguration$: Observable<
    fromConfigureCallCenter.CallCenterConfigurationState
  >;
  private callCenterChat$: Observable<fromCallCenterChat.CallCenterChatState>;
  private locationConfig$: Observable<
    fromLocationConfig.LocationConfigurationState
  >;
  public showTopToast: boolean = false;
  public showManualCloseToast: boolean = false;
  public showErrorConnectionToast: boolean = false;
  public topToastMessages: Array<{
    message: string;
    type: string;
  }> = [];
  public manualCloseToastMessages: Array<{
    message: string;
    type: string;
  }> = [];
  public topErrorConnectionToastMessages: Array<{
    message: string;
    type: string;
  }> = [];
  public callTransferMessage: Array<{
    message: string;
    type: string;
  }> = [];
  public callWhileInactiveMessages: Array<{
    message: string;
    type: string;
  }> = [];
  public callQueues: Array<any>;
  public inboundRingStop: any = null;
  private cancelOutboundCall: boolean = false;
  private envInfo: string =
    (APP_CONFIG.APP_ENV &&
      !APP_CONFIG.APP_ENV.includes('cpt') &&
      !APP_CONFIG.APP_ENV.includes('qa')) ||
    APP_CONFIG.qatest
      ? APP_CONFIG.APP_ENV
      : APP_CONFIG.APP_STACK;
  private url: string = `https://${APP_CONFIG.CALL_API_GW}/${this.envInfo}-outbound-callback/handler`;
  private callUrl: any = `${this.url}`;
  public connectedVia: any;
  public dialerNumber: string;
  private locationByPass: any;
  public leadId: number | undefined;
  public ledgerId: any;
  public customerId: number | undefined;
  public leadLocationId: number | undefined;
  public ledgerLocationId: number | undefined;
  public leadESId: string | undefined;
  public customerESId: string | undefined;
  public contactUri: string;
  public reservationUrl: string = '';
  public isCallPlaced: boolean = false;
  public isLocationLogin: boolean = false;
  public currentWorkerOnCallStatus: string = '';
  private callTo: string = '';
  public trackingNumbers: Array<TrackingnumberListItem>;
  private callCenterConfiguration: CallCenter;
  private loseTwilioConnection: boolean = false;
  private reconnectWithReservation: boolean = false;
  public onlineEvent: Observable<Event>;
  public offlineEvent: Observable<Event>;

  public subscriptions: Subscription[] = [];
  private statusWhenOffline: any;
  public internetConnectionLost: boolean = false;
  public internetConnectionStatusIsOnline: boolean = this.internetConnectivityService.getNetworkStatus;
  public retryingForCheckNetworkConnection:boolean = false;
  public isCheckingNetworkConnection:boolean = false;
  private collectionReportId: any;
  private collectionCallType: any;
  public showConnectionWizard: boolean = false;
  @Output() onTransferStarted: EventEmitter<any> = new EventEmitter<any>();
  private callToWhenChange: any;
  public inactiveStatusUpdated: boolean = false;
  private parentId: any;
  public coolDownTime: any;
  public couldNotReconnect: boolean = false;
  private initDone: boolean = false;
  private twilioDevice: any;
  private wrappingTasks: Array<any> = [];
  private wrappingTasksAlreadyClean: boolean = false;
  public noCallCenterConfig: boolean = false;
  public isThereActiveConversation: boolean = false;
  public showActiveModal: boolean = false;
  public showCallBackPopup: boolean = false;
  public showInboundVideoCallPopup: boolean = false;
  private audio: any;
  private isCallBack: boolean = true;
  public customerHangUp: boolean = false;
  public customerHangUpMessage: Array<{
    message: string;
    type: string;
  }> = [];
  private hangupWorker: any;
  public showTwilioInitializationError: boolean = false;
  private voiceIdentity: string;
  public fullLocationInfo: any;
  private trackingNumberFromEndpoint: any;
  private holdStatus: boolean = false;
  @HostBinding('style.height.px') componentHeight: number;
  public refreshObject: any;
  private endCallButtonPress: boolean = false;
  private moveToCoolDown: boolean = false;
  public disableHold = false;
  public onInternetLostPhoneNumber: any;
  public showInactiveNotAvailableTopToast: boolean = false;
  public agentStateInactive: any;
  public inactiveActivitySid: any;
  private callIsMuted: boolean;
  private isCallSidUsedAsTask: any;
  private isNewIC: boolean = false;
  private newFromCall: boolean = false;
  private callBackRescindedOnAccept: boolean = false;
  private coolDownTimeoutPointer: any;
  private oBcallNumber: any;
  public allLocations$: Observable<fromAllLocations.AllLocationsState>;
  public errorWithWorker: boolean = false;
  public errorWithWorkerMessage: string = '';
  public showRefeshForWorker: any;
  public showAttemptToReconnect: boolean = false;
  private wasAgentOnACall: boolean = false;
  private agentNoAnswer: boolean = false;
  public loadingImage: string = 'assets/svg/loading-spin-orange.svg';
  private audioContext: boolean = false;
  private callBackTaskSid: string = '';
  private callerHasHungUp: boolean = false;
  public callBeingAccepted: boolean = false;
  locationsMap$?: Subscription;
  locationsMap: any;
  devices: MediaDeviceInfo[] = [];
  showInactivityWarningModal: boolean = false;
  private modalTimeoutSubscription: Subscription | null = null;
  private countdownSubscription: Subscription | null = null;
  private inactivityTimerMilliseconds: number = 0;
  private warningTimerMilliseconds: number = 0;
  inactivityMinutes: number = 0;
  countdownSeconds: number = 0;

  // Phase 4: Periodic State Validation and Monitoring
  private stateValidationInterval: any;
  private performanceBaselineData: Map<string, number[]> = new Map();
  private lastStateValidationTime: number = 0;
  private consecutiveInconsistencies: number = 0;

  constructor(
    private store: Store<fromRoot.AppState>,
    private cd: ChangeDetectorRef,
    private awsLambdaProvider: AWSLambdaProvider,
    private twilioWorkerChannelsProvider: TwilioWorkerChannelsProvider,
    private sharedService: SharedService,
    private twilioEventHandlerService: TwilioEventHandlerService,
    private systemVariablesProvider: SystemVariablesProvider,
    private trackingNumberProvider: TrackingNumberProvider,
    private twilioProvider: TwilioProvider,
    private telephoneNumberPipe: TelephoneFormatPipe,
    private callQueuesWorkerProvider: CallQueuesWorkerProvider,
    private internetConnectivityService: InternetConnectivityService,
    private router: Router,
    private videoCommunicationsService: VideoCommunicationsService,
    private deviceService: DeviceService,
    private cpLoggingService: CpLoggingService,
    private callProvider: CallProvider,
    private bugsnagService: BugsnagService,
    private twilioDeviceService: TwilioDeviceService,
    private actions$: Actions
  ) {
    this.callCenterVoice$ = this.store.select('callCenterVoice');
    this.deviceClient$ = this.store.select('deviceClient');
    this.session$ = this.store.select('session');
    this.systemConfiguration$ = this.store.select('systemConfiguration');
    this.trackingNumbers$ = this.store.select('trackingNumbers');
    this.voIPExtensions$ = this.store.select('voIPExtensions');
    this.workerClient$ = this.store.select('workerClient');
    this.workspaceActivities$ = this.store.select('workspaceActivities');
    this.workspaceTasks$ = this.store.select('workspaceTasks');
    this.workspaceWorkers$ = this.store.select('workspaceWorkers');
    this.callQueues$ = this.store.select('callQueues');
    this.workerChannels$ = this.store.select('workerChannels');
    this.callCenterChat$ = this.store.select('callCenterChat');
    this.callCenterConfiguration$ = this.store.select(
      'callCenterConfiguration'
    );

    this.locationConfig$ = this.store.select('locationConfiguration');
    this.allLocations$ = this.store.select('allLocations');
  }

  ngOnInit() {
    console.log('[Call Center Voice][ngOnInit] - CallCenterVoiceComponent');
    this.getSession();
    this.twilioHandlers();
    this.getWorkerClient();
    this.updateWorkerToLatestState();
    this.getWorkerChannels();
    this.getWorkspaceActivities();
    this.getWorkspaceWorkers();
    this.getTrackingNumbers();
    this.registerDeviceClient();

    // Subscribe to the TwilioDeviceService initialization status
    this.twilioDeviceService.initializeStatus$.pipe(
      takeUntil(this.ngUnsubscribe)
    ).subscribe(initialized => {
      if (initialized) {
        this.twilioDevice = this.twilioDeviceService.getDevice();
        // Update the audioContext flag
        this.audioContext = true;
      }
    });

    this.checkForLoadSysConfig();
    this.attachWorkspaceClientHandlers();
    this.subscribeWorkerClient();
    this.attachWorkerClientHandlers();
    this.attachToReConnectAgent();
    this.attachToDisconnectAgent();
    this.attachToCallMute();
    this.attachToCurrentStatus();
    this.attachSendDigitsHandler();
    this.attachToCallQueues();
    this.attachToOutBoundCallFromPopCard();
    this.ensureTwilioDeviceInitialization();
    this.attatchToCallCenterConfigurationUpdate();
    this.attatchToCallCenterConfigurationError();
    this.listenTwilioWorkerUpdates();
    this.attachToWrappingTaskToClean();
    this.subscribeHangup();
    this.listenToMakeReady();
    this.getOpenedChats();
    this.listenToTwilioInitializationError();
    this.attachToLocations();
    this.subscribeToInactiveNotAvailable();
    this.subscribeToCallFromInactiveActivity();
    this.subscribeToNoMicError();
    this.store.dispatch(new fromCallCenterVoiceAction.ToggleCallHold(false));
    this.listenToCallBackRescinded();
    this.listenToInitializationErrors();
    this.listenToAttemptToRecoveInitializationError();
    this.watchLocationsMap();
    this.subscribeToCallBackStarted();

    this.componentHeight = 30;
    this.isLocationLogin = UtilsHelper.isLocationLogin();
    if (this.isLocationLogin) {
      this.connectedVia = fromCallCenterVoice.ConnectionMethods.COMPUTER;
      this.componentHeight = 0;
    }

    this.callCenterVoice$
      .pipe(map((state: fromCallCenterVoice.CallCenterVoiceState) => {
        return state.connectionMethod;
      })
      ,distinctUntilChanged()
      ,takeUntil(this.ngUnsubscribe))
      .subscribe(connectionMethod => {
        console.log(
          '[Call Center Voice][ngOnInit] - Connection method: ',
          fromCallCenterVoice.ConnectionMethods[connectionMethod]
        );
      });

    this.onlineEvent = fromEvent(window, 'online');
    this.offlineEvent = fromEvent(window, 'offline');

    this.internetConnectivityService.networkStatusChanged.pipe(takeUntil(this.ngUnsubscribe)).subscribe(internetConnectionStatusIsOnline => {
      this.isCheckingNetworkConnection = false;
      if (this.internetConnectionStatusIsOnline !== internetConnectionStatusIsOnline) {
        this.internetConnectionStatusIsOnline = internetConnectionStatusIsOnline;
        if (this.internetConnectionStatusIsOnline) { // online
          if (this.loseTwilioConnection) {
            let offlineActivity = this.workspaceActivities.find(
              (workspaceActivity: any) => {
                return (
                  workspaceActivity.friendlyName ===
                  OFFLINE_ACTIVITY_FRIENDLY_NAME
                );
              }
            );
            //update worker with inactive status
            UtilsHelper.workerUpdateSequential(
              this.worker,
              {
                ActivitySid: offlineActivity.sid,
              },
              (error, worker) => {
                if (error) {
                  return console.error(
                    'There was an error updating the worker: ',
                    error
                  );
                }
                console.log(
                  '[Call Center Voice][onlineEvent.subscribe] - Worker Activity has been updated to: ',
                  offlineActivity.friendlyName
                );
              }
            );
            this.updateWorker(
              offlineActivity,
              fromCallCenterVoice.AgentStates.OFFLINE
            );
          } else {
            this.showTopToast = true;
            this.topToastMessages = [
              {
                message: 'Internet connection re-established',
                type: TopToastComponent.SUCCESS,
              },
            ];
            switch (this.statusWhenOffline) {
              case fromCallCenterVoice.AgentStates.READY:
                this.removeHangupWorkerAttribute();
                this.setAgentState({
                  agentState: fromCallCenterVoice.AgentStates.READY,
                }, 'subscriptions.push()');
                break;
              case fromCallCenterVoice.AgentStates.OFFLINE:
                this.setAgentState({
                  agentState: fromCallCenterVoice.AgentStates.OFFLINE,
                }, 'subscriptions.push()');
                break;
              case fromCallCenterVoice.AgentStates.ON_INBOUND_CALL:
                this.setAgentState({
                  agentState: fromCallCenterVoice.AgentStates.ON_INBOUND_CALL,
                }, 'subscriptions.push()');
                break;
              case fromCallCenterVoice.AgentStates.COOLDOWN:
                this.setAgentState({
                  agentState: fromCallCenterVoice.AgentStates.COOLDOWN,
                }, 'subscriptions.push()');
                break;
              case fromCallCenterVoice.AgentStates.ON_OUTBOUND_CALL:
                this.setAgentState({
                  agentState: fromCallCenterVoice.AgentStates.ON_OUTBOUND_CALL,
                }, 'subscriptions.push()');
                break;
              default:
                if (this.workspaceActivities && this.worker) {
                  let connectingInactiveActivity: any = this.workspaceActivities.find(
                    (workspaceActivity: any) => {
                      return (
                        workspaceActivity.friendlyName ===
                        this.worker.activityName
                      );
                    }
                  );
                  this.setAgentState({
                    agentState:
                      fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE,
                    agentStateData: connectingInactiveActivity,
                  }, 'subscriptions.push()');
                }
                break;
            }
          }
        } else { // offline
          this.statusWhenOffline = this.currentAgentStatus;
          this.setAgentState({
            agentState: fromCallCenterVoice.AgentStates.OFFLINE,
          }, 'subscriptions.push()');
          this.retryToCheckNetworkConnection();
        }
      }

    });

    // TODO: Once we define if this message is going to be added to the webapp we can either remove it or complete the flow.
    // this.callTransferMessage = [
    //   {
    //     message: 'Customer is not longer on the call',
    //     type: TopToastComponent.NORMAL,
    //   },
    // ];
    this.sharedService.showConnection$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((res: any) => {
      if (res) {
        this.showConnectionWizard = true;
      }
    });

    // Initialize Phase 4 periodic monitoring after all other initializations
    setTimeout(() => {
      this.initializePeriodicMonitoring();
    }, 5000); // Wait 5 seconds for other components to initialize

  }

  ngOnDestroy() {
    console.log('[Call Center Voice][ngOnDestroy] - CallCenterVoiceComponent');
    
    // Cleanup Phase 4 periodic monitoring
    this.cleanupPeriodicMonitoring();
    
    this.ngUnsubscribe.next(null);
    this.ngUnsubscribe.complete();
    this.cd.detach();

    // Ensure the Twilio device is destroyed
    this.twilioDeviceService.destroyDevice();

    this.resetAgentTimer();
    if (this.modalTimeoutSubscription) {
      this.modalTimeoutSubscription.unsubscribe();
    }
  }

  /**
   * check if we already have sys config loaded. if not, load it
   */
  private checkForLoadSysConfig() {
    this.systemConfiguration$
      .pipe(take(1))
      .subscribe(
        (sysState: fromSystemConfiguration.SystemConfigurationState) => {
          if (!sysState.loaded && !sysState.loading) {
            this.store.dispatch(
              new fromSystemConfigurationActions.LoadSystemConfiguration()
            );
          }
        }
      );
  }

  /**
   * @memberof CallCenterVoiceComponent
   * Checks if the user has an inbound and outbound audio device and if not changes them to invactive state, sets their activity, and displays an error message.
   * Called during fromCallCenterVoice.AgentStates.CONNECT and on twilioDevice.audio.on('deviceChange')
   */
  audioDeviceStatus = async (devices?: any, triggerFail?: boolean) => {
    // Get the device from the service
    const device = this.getTwilioDevice();

    // Only check audio devices if the device is initialized
    if (!device) return true;

    let inboundAudioDevices = device.audio?.availableInputDevices.size;
    let outboundAudioDevices = device.audio?.availableOutputDevices.size;
    if ((!inboundAudioDevices || !outboundAudioDevices) && (this.connectedVia ===  fromCallCenterVoice.ConnectionMethods.COMPUTER) && !UtilsHelper.isLocationLogin()) {
      if (this.currentReservation?.task?.attributes?.taskchannel === 'voice') {
        this.inboundCallRejected();
      }
      let inactiveActivity: any = this.workspaceActivities.find(
        (workspaceActivity: any) => {
          return workspaceActivity.friendlyName === NOMIC_ACTIVITY_FRIENDLY_NAME;
        }
      );
      this.setAgentState({
        agentState: fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE,
        agentStateData: inactiveActivity,
      }, 'audioDeviceStatus()');
      this.inactiveStatusUpdated = true;
      this.cd.detectChanges();

      await this.disconnectCalls();
      let audioErrorMessage;
      if (!inboundAudioDevices) {
        audioErrorMessage = 'No microphone detected. Please check your microphone and try again.';
      } else {
        audioErrorMessage = 'No speakers or audio output detected. Check your audio and try again';
      }
      this.manualCloseToastMessages = [
        {
          message: audioErrorMessage,
          type: TopToastComponent.ERROR,
        },
      ];
      this.store.dispatch(new fromCallCenterVoiceAction.ToggleDialPad(false));
      this.showManualCloseToast = true;
      this.setCallState({
        callState: fromCallCenterVoice.CallStates.IDLE,
      });

      return false;
    }
    return true;
  }

  /**
   *
   * Method that changes the agent's state
   * @param {fromCallCenterVoice.AgentStateActionPayload} {
   *     agentState,
   *     agentStateData,
   *   }
   * @param {string} [origin] optional, only for debugging purposes
   */
  public setAgentState({
      agentState,
      agentStateData,
    // tslint:disable-next-line: align
  }: fromCallCenterVoice.AgentStateActionPayload, origin?: string): void {
    this.bugsnagService.leaveBreadcrumb('AgentStateChanged', {
      agentState: fromCallCenterVoice.AgentStates[agentState],
      agentStateData,
    });
    this.cpLoggingService.registerEvent(
      'agent.state.changed',
      'success',
      {
        agentState: fromCallCenterVoice.AgentStates[agentState],
        agentStateData,
        origin
      }
    );

    console.log('[Call Center Voice][setAgentState] - setAgentState to: ' + fromCallCenterVoice.AgentStates[agentState] +
      (origin ? ', Method from where it is called: ' + origin : ''));
    // Not the best place or logic but will work right now
    // LocationAgents must always be in Ready status
    if (
      this.worker &&
      this.worker.attributes &&
      this.worker.attributes.hidden
    ) {
      if (
        agentState !== fromCallCenterVoice.AgentStates.READY &&
        agentState !== fromCallCenterVoice.AgentStates.CONNECTING_READY
      ) {
        return;
      }
    }

    switch (agentState) {
      // NONE
      case fromCallCenterVoice.AgentStates.NONE: {
        break;
      }

      // DISCONNECTING
      case fromCallCenterVoice.AgentStates.DISCONNECTING: {
        let workspaceActivity: any = agentStateData;
        this.onDisconnectingAgent(workspaceActivity);
        break;
      }

      // OFFLINE
      case fromCallCenterVoice.AgentStates.OFFLINE: {
        this.resetAgentTimer();
        this.disconnectCalls();
        this.store.dispatch(
          new fromCallCenterVoiceAction.SetAgentState({
            agentState: fromCallCenterVoice.AgentStates.OFFLINE,
          })
        );
        this.sharedService.makeChatOffline$.next(null);
        break;
      }

      // CONNECT
      case fromCallCenterVoice.AgentStates.CONNECT: {
        this.twilioDeviceService.handleUserInteraction();
        this.store.dispatch(
          new fromCallCenterVoiceAction.SetAgentState({
            agentState: fromCallCenterVoice.AgentStates.CONNECT,
          })
        );
        this.showConnectionWizard = true;
        break;
      }

      // CONNECTING_READY
      case fromCallCenterVoice.AgentStates.CONNECTING_READY: {
        if (!this.audioDeviceStatus()) {
          return;
        }
        let workspaceActivity: any = agentStateData;
        this.onConnectingAgentReady(workspaceActivity);
        break;
      }

      // CONNECTING_INACTIVE
      case fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE: {
        let workspaceActivity: any = agentStateData;
        this.onConnectingAgentInactive(workspaceActivity);
        break;
      }

      // READY
      case fromCallCenterVoice.AgentStates.READY: {
        this.twilioDeviceService.handleUserInteraction();
        this.store.dispatch(
          new fromCallCenterVoiceAction.SetAgentState({
            agentState: fromCallCenterVoice.AgentStates.READY,
          })
        );
        this.activateAgentTimer();
        if (!this.audioDeviceStatus()) {
          return;
        }
        break;
      }

      // INACTIVE
      case fromCallCenterVoice.AgentStates.INACTIVE: {
        this.disconnectCalls();
        this.store.dispatch(
          new fromCallCenterVoiceAction.SetAgentState({
            agentState: fromCallCenterVoice.AgentStates.INACTIVE,
            agentStateData: agentStateData,
          })
        );
        this.inactiveStatusUpdated = true;
        if (this.cancelOutboundCall) {
          let agentWorkerActivity = this.workspaceActivities.find(activity => activity.sid === agentStateData);
        }
        this.cd.markForCheck();
        this.activateAgentTimer();
        break;
      }

      // ON_OUTBOUND_CALL
      case fromCallCenterVoice.AgentStates.ON_OUTBOUND_CALL: {
        if (agentStateData) {
          /* agentStateData is modified and "cleaned" constatly so for
           * better to store the toNumber on a different variable to
           * handle the connection lost scenario
           */
          this.onInternetLostPhoneNumber = agentStateData.toNumber;
          agentStateData.toNumber = agentStateData.toNumber;
          this.cd.markForCheck();
          this.oBcallNumber = agentStateData.toNumber;
        }
        this.resetAgentTimer();
        this.store.dispatch(
          new fromCallCenterVoiceAction.SetAgentState({
            agentState: fromCallCenterVoice.AgentStates.ON_OUTBOUND_CALL,
            agentStateData: agentStateData,
          })
        );
        this.cd.markForCheck();
        break;
      }

      // ON_INBOUND_CALL
      case fromCallCenterVoice.AgentStates.ON_INBOUND_CALL: {
        // this.currentConnection.accept();
        this.onInternetLostPhoneNumber = '';
        this.resetAgentTimer();
        this.store.dispatch(
          new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false)
        );
        this.setCallState({
          callState: fromCallCenterVoice.CallStates.ON_CALL,
        });
        this.store.dispatch(
          new fromCallCenterVoiceAction.SetAgentState({
            agentState: fromCallCenterVoice.AgentStates.ON_INBOUND_CALL,
            agentStateData: {
              toNumber: '',
            }
          })
        );
        this.cd.markForCheck();
        break;
      }

      // ON_INBOUND_REJECTED
      case fromCallCenterVoice.AgentStates.ON_INBOUND_REJECTED: {
        break;
      }

      // COOLDOWN
      case fromCallCenterVoice.AgentStates.COOLDOWN: {
        this.store.dispatch(new fromCallCenterVoiceAction.ToggleDialPad(false));
        //this.store.dispatch(new fromCallsCountActions.LoadCallsCount()); //commented to dont lose this line

        this.store.dispatch(
          new fromCallCenterVoiceAction.SetAgentState({
            agentState: fromCallCenterVoice.AgentStates.COOLDOWN,
          })
        );

        // This code makes sure the cooldown status doesn't stay stuck, It forces to move the agent's state
        // to ready if the cooldown status remains longer than the time defined in the coolDownTime variable.
        if (!this.coolDownTimeoutPointer) {
          this.coolDownTimeoutPointer = setTimeout(() => {
            if (this.currentAgentStatus === fromCallCenterVoice.AgentStates.COOLDOWN) {
              let readyActivity = this.workspaceActivities.find(activity => activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME);
              this.setAgentState({
                agentState: fromCallCenterVoice.AgentStates.COMPLETING_CALL,
                agentStateData: readyActivity,
              }, 'setAgentState()');
            }
            this.coolDownTimeoutPointer = null;
          }, ((this.coolDownTime ? this.coolDownTime : 30) + 5) * 1000);
        }
        break;
      }

      // COMPLETING_CALL
      case fromCallCenterVoice.AgentStates.COMPLETING_CALL: {
        this.onCompletingCall(agentStateData);
        break;
      }
      default: {
      }
    }

    // Validate state consistency after agent state change
    this.validateStateConsistency();
  }

  /**
   *
   * Function that Add a timeout in order to change the agent status to offline
   * based on call center config.
   * This only applies to CC agents
   * @private
   * @memberof CallCenterVoiceComponent
   */
  private activateAgentTimer() {
    if (this.timeoutHandler) {
      this.resetAgentTimer();
    }

    if (!this.timeoutHandler && !UtilsHelper.isLocationLogin()) {
      const hourArray = this.callCenterConfiguration.inactivity_time.split(':');
      this.inactivityTimerMilliseconds =
        parseInt(hourArray[0], 10) * 3600000 +
        parseInt(hourArray[1], 10) * 60000;

      this.inactivityMinutes =
        parseInt(hourArray[0], 10) * 60 +
        parseInt(hourArray[1], 10);

      //warningMinutes determines how long the modal stays open before automatically going offline
      const warningMinutes = 2;
      this.warningTimerMilliseconds = warningMinutes * 60000;

      this.timeoutHandler = setTimeout(() => {
        if (
          this.currentAgentStatus !== fromCallCenterVoice.AgentStates.OFFLINE
        ) {
          this.showInactivityWarningModal = true;
          this.setupModalTimeout();
        }

      }, this.inactivityTimerMilliseconds);
    }
  }

  private setupModalTimeout() {
    this.countdownSeconds = Math.ceil(this.warningTimerMilliseconds / 1000);

    if (this.modalTimeoutSubscription) {
      this.modalTimeoutSubscription.unsubscribe();
    }
    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
    }

    this.countdownSubscription = interval(1000)
      .pipe(
        take(this.countdownSeconds),
        tap(() => {
          this.countdownSeconds--;
          if (this.countdownSeconds < 0) {
            this.countdownSeconds = 0;
          }
          this.cd.detectChanges();
        })
      )
      .subscribe({
        complete: () => {
          console.log('Countdown finished visually.');
        },
      });

    // Start a new timer for the modal's display duration
    this.modalTimeoutSubscription = timer(this.warningTimerMilliseconds)
      .pipe(take(1))
      .subscribe(() => {
        if (this.showInactivityWarningModal) {
          this.handleInactivityModalResponse();
        }
      });
  }

  public handleInactivityModalResponse(stayOnline: boolean = false) {
    this.showInactivityWarningModal = false; // Hide the modal immediately
    if (this.modalTimeoutSubscription) {
      this.modalTimeoutSubscription.unsubscribe();
    }

    if (stayOnline) {
      this.resetAgentTimer();
      this.activateAgentTimer();
    } else {
      if (this.currentAgentStatus !== fromCallCenterVoice.AgentStates.OFFLINE) {
        const activity = this.workspaceActivities.find(
          (workspaceActivity: any) => {
            return (
              workspaceActivity.friendlyName === OFFLINE_ACTIVITY_FRIENDLY_NAME
            );
          }
        );
        const state = fromCallCenterVoice.AgentStates.OFFLINE;
        this.updateWorker(activity, state);
        ChromeNotifications.notify(
          'Agent Inactivity',
          'You have been put in an offline status due to inactivity. Click to return to Call Potential to reconnect.',
          false,
          true
        );
      }
    }
  }

  private resetAgentTimer() {
    // reset the timer pointer for the agent timeout
    if (this.timeoutHandler) {
      clearTimeout(this.timeoutHandler);
      this.timeoutHandler = null;
    }

    if (this.modalTimeoutSubscription) {
      this.modalTimeoutSubscription.unsubscribe();
      this.modalTimeoutSubscription = null;
    }

    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
      this.countdownSubscription = null;
    }
  }

  public catchConnectLater(event) {
    this.showConnectionWizard = event;
  }
  private attachToDisconnectAgent() {
    this.callCenterVoice$
      .pipe(filter(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.loseConnection
      )
      ,map(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.loseConnection
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe(loseConnection => {
        this.checkNetworkConnection();
        this.internetConnectionLost = false;
        this.showErrorConnectionToast = true;
        this.cd.detectChanges();
        this.loseTwilioConnection = true;
      });
  }

  private attachToReConnectAgent() {
    this.callCenterVoice$
      .pipe(filter(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.connectionReEstablished
      )
      ,map(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.connectionReEstablished
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe(connectionReEstablished => {
        this.showErrorConnectionToast = false;
        this.couldNotReconnect = false;
        this.showTopToast = true;
        this.showAttemptToReconnect = false;
        this.showTwilioInitializationError = false;
        this.topToastMessages = [
          {
            message: 'Connection has been re-established',
            type: TopToastComponent.SUCCESS,
          },
        ];
        this.cd.detectChanges();
        this.loseTwilioConnection = false;
        this.internetConnectivityService.updateNetworkStatus(true);
      });
  }

  /**
   * Agent's state listener
   */
  private attachToCurrentStatus() {
    this.callCenterVoice$
      .pipe(filter(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.agentState !== null
      )
      ,map(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.agentState
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe(state => {
        this.currentAgentStatus = state;
      });
  }

  public setCallState({
    callState,
    callStateData,
  // tslint:disable-next-line: align
  }: fromCallCenterVoice.CallStateActionPayload, origin?: string) {
    console.log('[Call Center Voice][setCallState] - setting call state to: ' + fromCallCenterVoice.CallStates[callState] +
      (origin ? ', Method from where it is called: ' + origin : ''));

    this.currentCallState = callState;
    switch (callState) {
      case fromCallCenterVoice.CallStates.DIALING: {
        if (!this.isLocationLogin) {
          this.changeWorkerAvailability(false);
        }
        this.isCallBack = false;
        if (this.showCallBackPopup) {
          this.showCallBackPopup = false;
          this.store.dispatch(
            new fromCallCenterVoiceAction.ToggleInboundCallbackPopUp(false)
          );
          this.isCallBack = true;
          if (this.audio) {
            this.audio.pause();
            this.audio = null;
          }
        }
        this.showTopToast = false;
        let isValidNumber: boolean = true;
        let isSipValid: boolean = true;
        if (!callStateData.fromLocation) {
          this.topToastMessages = [
            {
              message: 'No calling as location selected to perform the call',
              type: TopToastComponent.BIG_NORMAL,
            },
          ];
          this.showTopToast = true;
          return;
        }

        if (!callStateData.toNumber) {
          this.topToastMessages = [
            {
              message: 'No number selected to perform the call',
              type: TopToastComponent.BIG_NORMAL,
            },
          ];
          this.showTopToast = true;
          return;
        }

        let toPhoneNumber = callStateData.toNumber;
        if (toPhoneNumber.includes('@') && !toPhoneNumber.includes('sip:')) {
          isSipValid = UtilsHelper.validSipPhone(toPhoneNumber);
          isValidNumber = false;
          toPhoneNumber = 'sip:' + toPhoneNumber;
        } else {
          callStateData.toNumber = callStateData.toNumber.replace(/\D/g, '');
          this.dialerNumber = callStateData.toNumber;
          isValidNumber = UtilsHelper.validPhone(callStateData.toNumber);
          isSipValid = false;
        }
        if (isValidNumber || isSipValid) {
          // set agent to be out bound call
          this.setAgentState({
            agentState: fromCallCenterVoice.AgentStates.ON_OUTBOUND_CALL,
            agentStateData: {
              fromLocation: callStateData.fromLocation,
              toNumber: callStateData.toNumber,
              isDialPad: callStateData.isDialPad,
              workflowStep: callStateData.workflowStep,
              outboundCardLinkId: callStateData.outboundCardLinkId,
            },
          }, 'setCallState()');
          // dispatch call state change
          this.store.dispatch(
            new fromCallCenterVoiceAction.SetCallState(
              fromCallCenterVoice.CallStates.DIALING
            )
          );
          this.isCallPlaced = true;
          console.log('[Call Center Voice][setCallState] - Calling create call function');
          this.createCall();
          this.changeCallToNumber({
            callToNumber: '',
            lastNumberPressed: null,
          });
          this.changeDialPadView(fromCallCenterVoice.DialPadViews.KEYPAD);
        } else {
          this.topToastMessages = [
            {
              message: 'The number is not valid',
              type: TopToastComponent.ERROR,
            },
          ];
          this.showTopToast = true;
        }
        break;
      }

      case fromCallCenterVoice.CallStates.INCOMING: {
        const taskAttributes = this.currentReservation.task.attributes || null;
        if (taskAttributes && taskAttributes.taskchannel === 'voice') {
          const displayName = 'Call';
          let callerNumber = this.telephoneNumberPipe.transform(
            taskAttributes.caller,
            PHONE_FORMATS.DIGITS_AND_DASHES_WITH_PARENTHESIS
          );
          let notificationBody;
          if (!this.hideCallerTypeInbound) {
            notificationBody = `${taskAttributes.caller_name ||
              taskAttributes.customer_name ||
              callerNumber} - ${taskAttributes.disposition} - ${
              taskAttributes.ad_name
            }`;
          } else {
            notificationBody = `${taskAttributes.caller_name ||
              taskAttributes.customer_name ||
              callerNumber} - ${taskAttributes.ad_name}`;
          }
          ChromeNotifications.notify(displayName, notificationBody);
        }
        if (this.currentReservation.reservationStatus !== 'canceled') {
          // this trigger is executed whether the browser is hidden or not
          this.store.dispatch(
            new fromCallCenterVoiceAction.ToggleInboundCallPopUp(true)
          );
        }
        break;
      }

      case fromCallCenterVoice.CallStates.ON_CALL: {
        this.store.dispatch(
          new fromCallCenterVoiceAction.SetCallState(
            fromCallCenterVoice.CallStates.ON_CALL
          )
        );
        break;
      }

      case fromCallCenterVoice.CallStates.END_CALL: {
        // this.store.dispatch(new fromCallsCountActions.LoadCallsCount()); //commented to dont lose this line
        this.endCall();
        break;
      }

      case fromCallCenterVoice.CallStates.IDLE: {
        this.store.dispatch(
          new fromCallCenterVoiceAction.SetCallState(
            fromCallCenterVoice.CallStates.IDLE
          )
        );
        break;
      }

      default: {
        console.error('Unhandled set of new call state', callState);
      }
    }

    // Validate state consistency after call state change
    this.validateStateConsistency();
  }

  /**
   * Start the call transfer process.
   */
  public startCallTransfer({
    callState,
    callStateData,
  }: fromCallCenterVoice.CallStateActionPayload, origin?: string) {
    this.bugsnagService.leaveBreadcrumb('StartCallTransfer', {
      callSid: callStateData?.callSid,
      callNumber: callStateData?.toNumber,
      destinationNumber: callStateData?.destinationNumber,
      transferDetails: callStateData,
    });

    console.log(`[Call Center Voice][setCallTransferState] - setting call state to: ${fromCallCenterVoice.CallStates[callState]}${(origin ? `, Method from where it is called: ${origin}` : '')}`);

    this.setAgentState({
      agentState: fromCallCenterVoice.AgentStates.ON_OUTBOUND_CALL,
      agentStateData: {
        fromLocation: callStateData.fromLocation,
        toNumber: callStateData.toNumber,
      },
    }, 'setCallState()');

    // dispatch call state change
    this.store.dispatch(
      new fromCallCenterVoiceAction.SetCallState(
        fromCallCenterVoice.CallStates.DIALING
      )
    );
    this.isCallPlaced = true;

    /**
     * Emit this event to trigger the call transfer process to start.
     */
    this.onTransferStarted.emit(true);
  }

  public setConnectionMethod(
    connectionMethod: fromCallCenterVoice.ConnectionMethods
  ): void {
    this.store.dispatch(
      new fromCallCenterVoiceAction.SetConnectionMethod(connectionMethod)
    );
    this.isLocationLogin = UtilsHelper.isLocationLogin();
    this.connectedVia = this.isLocationLogin
      ? fromCallCenterVoice.ConnectionMethods.COMPUTER
      : connectionMethod;
  }

  public toggleDialPad(event?: MouseEvent): void {
    // This is a user gesture, perfect time to handle AudioContext
    if (event) {
      this.twilioDeviceService.handleUserInteraction();
    }

    // Check if it's an incoming call or callback - if so, don't toggle dialpad
    const incomingCallBack = this.currentReservation?.task?.attributes?.taskchannel === 'Callback' && this.currentReservation?.reservationStatus === 'pending';
    if (this.currentCallState === fromCallCenterVoice.CallStates.INCOMING || incomingCallBack) {
      console.log('[Call Center Voice][toggleDialPad] - Incoming call, cannot toggle dial pad');
      return;
    }

    this.isLocationLogin = UtilsHelper.isLocationLogin();
    if (!this.isLocationLogin && this.connectedVia === fromCallCenterVoice.ConnectionMethods.COMPUTER) {
      if (this.deviceService.getAudioPermissionStatus()) {
        // Permission already granted, proceed with actions
        this.store.dispatch(new fromCallCenterVoiceAction.ToggleDialPad());
      } else {
        // Handle case where permission was previously denied
        let inactiveActivity = this.workspaceActivities.find(
          (workspaceActivity: any) => {
            return workspaceActivity.friendlyName === NOMIC_ACTIVITY_FRIENDLY_NAME;
          }
        );
        this.setAgentState({
          agentState: fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE,
          agentStateData: inactiveActivity,
        }, 'toggleDialPad()');
      }
    } else {
      this.store.dispatch(new fromCallCenterVoiceAction.ToggleDialPad());
    }
  }

  public changeDialPadView(
    newDialPadView: fromCallCenterVoice.DialPadViews
  ): void {
    return this.store.dispatch(
      new fromCallCenterVoiceAction.SetDialPadView(newDialPadView)
    );
  }

  public changePhonebookView(
    newPhonebookView: fromCallCenterVoice.PhonebookViews
  ) {
    return this.store.dispatch(
      new fromCallCenterVoiceAction.SetDialPadPhonebookView(newPhonebookView)
    );
  }

  public changeCallToNumber({ callToNumber, lastNumberPressed }): void {
    this.callToWhenChange = callToNumber;
    return this.store.dispatch(
      new fromCallCenterVoiceAction.SetDialPadCallToNumber({
        callToNumber,
        lastNumberPressed,
      })
    );
  }

  public toggleCallMute(event: MouseEvent): void {
    // This is a user gesture, handle AudioContext
    this.twilioDeviceService.handleUserInteraction();

    // Toggle the mute state
    const newMuteState = !this.callIsMuted;

    // Apply mute immediately for better user experience
    this.toggleMuteForCalls(newMuteState);

    // Then update the Redux store to keep UI in sync
    return this.store.dispatch(new fromCallCenterVoiceAction.ToggleCallMute());
  }

  /**
   * Listen for fromCallCenterVoice.CallCenterVoiceState.isCallMuted changes
   *
   *
   * @memberOf CallCenterVoiceComponent
   */
  public attachToCallMute() {
    this.callCenterVoice$
      .pipe(
        distinctUntilChanged(
          (a, b) => a.isCallMuted === b.isCallMuted
        )
      ,map(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.isCallMuted
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe((isCallMuted: boolean) => {
        const device = this.getTwilioDevice();
        this.callIsMuted = isCallMuted;

        // Try multiple approaches to get the active connection
        let connection = null;

        // Try currentConnection first (works for outbound calls)
        if (this.currentConnection && typeof this.currentConnection.mute === 'function') {
          console.log('[Call Center Voice][attachToCallMute] - Using currentConnection');
          this.currentConnection.mute(isCallMuted);
          return;
        }

        // If no currentConnection, try device.activeConnection() (works for inbound calls)
        if (device) {
          try {
            connection = device._activeCall;
            if (connection) {
              console.log('[Call Center Voice][attachToCallMute] - Using device.activeConnection()');
              connection.mute(isCallMuted);
              return;
            }
          } catch (error) {
            console.log('[Call Center Voice][attachToCallMute] - Error accessing activeConnection', error.message);
          }
        }

        // Fallback to toggleMuteForCalls which handles it in a different way
        this.toggleMuteForCalls(isCallMuted);
      });
  }

  public toggleCallHold(event?: MouseEvent, fromEndCall?, fromCoolDown?): void {
    // If this is from a user gesture, ensure AudioContext is resumed
    if (event) {
      this.twilioDeviceService.handleUserInteraction();
    }

    let statusForRequest = !this.holdStatus;
    if (fromEndCall) {
      this.holdStatus = false;
      statusForRequest = false;
    }

    this.cd.markForCheck();
    // Update the condition to check for both inbound and outbound calls
    if (this.currentReservation?.task?.attributes?.taskchannel === 'voice' ||
        this.currentReservation?.task?.attributes?.direction === 'outbound') {
      let payload = {
        accountSid: this.session.user.sid,
        taskSid: this.currentReservation.task.sid,
        holdStatus: statusForRequest,
      };

      if (!fromEndCall) {
        this.disableHold = true;
      }

      this.twilioProvider.holdCall(payload).then(
        res => {
          if (res && res.error) {
            if (res.error.status === 404 && !fromEndCall && !fromCoolDown) {
              this.topToastMessages = [
                {
                  message: 'Cannot place call on hold. Retry after call is connected.',
                  type: TopToastComponent.ERROR,
                },
              ];
              this.showTopToast = true;
              this.cd.detectChanges();
            } else if (res.error !== 404 && !fromEndCall && !fromCoolDown) {
              this.topToastMessages = [
                {
                  message: res.error,
                  type: TopToastComponent.ERROR,
                },
              ];
              this.showTopToast = true;
              this.cd.detectChanges();
            }
            if (fromEndCall || fromCoolDown) {
              this.store.dispatch(new fromCallCenterVoiceAction.ToggleCallHold(false));
              this.disableHold = false;
              this.holdStatus = false;
            }
            datadogLogs.logger.debug(
              'Error occurred on call hold request ',
              {
                component: 'Call Center voice',
                method: 'toggleCallHold',
                data: {
                  payload,
                  error: res.error,
                }
              }
            );
          } else {
            this.holdStatus = !this.holdStatus;
            this.disableHold = false;

            // If we're taking the call off hold (holdStatus is now false), ensure AudioContext is resumed
            if (!this.holdStatus && this.connectedVia === fromCallCenterVoice.ConnectionMethods.COMPUTER) {
              // Resume AudioContext after taking off hold
              this.twilioDeviceService.handleUserInteraction();
            }

            if (fromEndCall) {
              this.holdStatus = false;
              this.store.dispatch(new fromCallCenterVoiceAction.ToggleCallHold(false));
            } else {
              this.store.dispatch(new fromCallCenterVoiceAction.ToggleCallHold(this.holdStatus));
            }
          }
        },
        err => {
          this.topToastMessages = [
            {
              message: 'Cannot place call on hold. Retry after call is connected.',
              type: TopToastComponent.ERROR,
            },
          ];
          this.disableHold = true;
          this.showTopToast = true;
          this.cd.detectChanges();
        }
      );
    }

    datadogLogs.logger.debug(
      'Action: Toggle On Hold',
      {
        component: 'Call center voice',
        method: 'toggleCallHold',
        data: {
          holdStatus: this.holdStatus,
        }
      }
    );
  }

  public inboundVideoCallAccepted(event: MouseEvent): void {
    // This is a user gesture, handle AudioContext
    this.twilioDeviceService.handleUserInteraction();

    this.bugsnagService.leaveBreadcrumb('inboundVideoCallAccepted', {
      callSid: this.currentReservation?.task?.attributes?.call_sid,
      callNumber: this.currentReservation?.task?.attributes?.caller,
      destinationNumber: this.currentReservation?.task?.attributes?.to,
      callStatus: this.currentReservation?.reservationStatus,
      callChannel: this.currentReservation?.task?.attributes?.taskchannel,
      callType: this.currentReservation?.task?.attributes?.call_type,
    });

    this.endCallButtonPress = false;

    if (this.audio) {
      this.audio.pause();
      this.audio = null;
      this.inboundRingStop = new Date();
    }
    this.showInboundVideoCallPopup = false;

    this.inboundRingStop = new Date();
    this.reconnectWithReservation = false;
    console.log('[Call Center Voice][inboundVideoCallAccepted] - Incoming video call accepted');

    const atts = this.currentReservation.task.attributes;

    // Accept task
    this.currentReservation.accept((error, reservation) => {
      if (reservation) {

        // Call the Video Service to index the call information (Room name + Reservation.task.sid + workerClient)
        this.videoCommunicationsService.setCallInformation(atts.room_name, reservation.task.sid, this.workerClient);

        // Launch the video call for the agent
        this.launchVideoCallForAgent(atts.room_name, atts.agent_token, atts.location_id, atts.recording);

        // Set flag for agent on call
        this.wasAgentOnACall = true;

        this.callBeingAccepted = true;

        // Set agent state to "on inbound call"
        this.setAgentState({
          agentState: fromCallCenterVoice.AgentStates.ON_INBOUND_CALL,
        }, 'inboundVideoCallAccepted()');

      } else if (error) {

        /* According to the Twilio docs, any attempts to accept a rescinded reservation,
         * the user will receive a 410 series response, indicating that the reservation
         * can no longer accept the reservation of the task
         */
        if (error.code === 410 || error.message.includes('rescinded')) {
          this.twilioEventHandlerService.workerClientCallbackReservationRescinded$.next(true);
        }
        this.bugsnagService.notify(error);
        console.log(error);
      }
    });

    //Resetting the agentStatePriorCall values since this information is only required for OB calls from Inactive status
    this.sharedService.agentStatePriorCall$.next({ status: '', activity: '' });

  }

  private launchVideoCallForAgent(roomName: string, agentToken: string, locationId: string, recording: Number): void {

    // Remove the call in progress flag from local storage.
    localStorage.removeItem(callInProgressKey);

    const loc = this.locationsMap[locationId] || {};
    const videoCallUrl = loc.video_call_link || '';

    /**
     * NOTE: We are communicating the video call short URL to the agent video room.
     * The video room uses this information to determine if the feature is enabled.
     */
    const queryParams = {
      r: roomName,
      t: agentToken,
      u: videoCallUrl,
      recording: recording === 1 ? true : false
    };

    /**
     * DEVELOPMENT NOTE:
     * This section of code deals with constructing the URL to the video call app which
     * is a separate application.  Since we must always debug the CP app locally on port
     * 3000, to accommodate local testing of this behavior, we will check to see if we
     * are running on port 3000 and if so, we will:
     * - construct the URL for the video call app using http://localhost:4200/CP-Video-Calling-UI/app/view
     *   - the showcase app must be running and locally accessible via http://localhost:4200/CP-Video-Calling-UI/
     *     - this is the default behavior when you run that app locally
     *     - the /app/view path will load the standalone video call app
     */

    let videoCallAppOrigin;
    if (window.location.port === '3000') {
      videoCallAppOrigin = 'http://localhost:4200/CP-Video-Calling-UI/app/view';
    }else {
      videoCallAppOrigin = window.location.origin + '/ui/video-call';
    }

    const url = videoCallAppOrigin + this.router.serializeUrl(
      this.router.createUrlTree(['/connect/client'], { queryParams })
    );
    console.log('inboundVideoCallAccepted() -- url: ', url);
    window.open(url, '_blank');

  }

  /**
   * Get the account ID and location ID from the task reservation.
   * If the task reservation is not available, return NA for both values.
   * @returns {Object} An object containing the account ID and location ID.
   */
  public getAccountIdAndLocationIdFromReservation(): { accountId: string, locationId: string } {
    let locationId = 'NA';
    let accountId = 'NA';
    if (this.currentReservation?.task?.attributes) {
      const atts = this.currentReservation.task.attributes;
      if (atts.location_id) {
        locationId = atts.location_id;
      }
      if (atts.user_id) {
        accountId = atts.user_id;
      }
    }
    return { accountId, locationId };
  }

  public inboundCallAccepted(event: MouseEvent): void {
    // This is a user gesture, handle AudioContext
    this.twilioDeviceService.handleUserInteraction();

    // Hide the inbound call popup immediately to give user feedback
    this.store.dispatch(
      new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false)
    );

    this.bugsnagService.leaveBreadcrumb('inboundCallAccepted', {
      callSid: this.currentReservation?.task?.attributes?.call_sid,
      callNumber: this.currentReservation?.task?.attributes?.caller,
      destinationNumber: this.currentReservation?.task?.attributes?.to,
      callStatus: this.currentReservation?.reservationStatus,
      callChannel: this.currentReservation?.task?.attributes?.taskchannel,
      callType: this.currentReservation?.task?.attributes?.call_type,
    });

    // Check if Twilio device is initialized before proceeding
    if (
      this.getConnectionMethod() === fromCallCenterVoice.ConnectionMethods.COMPUTER &&
      !this.twilioDeviceService.isInitialized()
    ) {
      console.log('[Call Center Voice][inboundCallAccepted] - Twilio device not initialized, attempting initialization');

      // Try to initialize the device before continuing
      this.twilioDeviceService.initializeDevice(true).then(() => {
        console.log('[Call Center Voice][inboundCallAccepted] - Device initialized successfully, proceeding with call acceptance');
        // Continue with call acceptance after initialization
        this.processInboundCallAccept();
      }).catch(error => {
        console.error('[Call Center Voice][inboundCallAccepted] - Failed to initialize Twilio device', error);
        this.bugsnagService.notify(error);
        this.showAudioContextError();
      });
    } else {
      // Device is already initialized or not needed (phone/VOIP), proceed with call acceptance
      this.processInboundCallAccept();
    }
  }

  /**
   * Process the actual call acceptance logic after ensuring the device is ready
   * Extracted from inboundCallAccepted for better error handling
   */
  private processInboundCallAccept(): void {
    // Reset any previous transfer state when accepting a new call
    this.twilioDeviceService.resetOnNewCall();

    this.bugsnagService.leaveBreadcrumb('processInboundCallAccept', {
      callSid: this.currentReservation?.task?.attributes?.call_sid,
      callNumber: this.currentReservation?.task?.attributes?.caller,
    });

    this.setCallState({
      callState: fromCallCenterVoice.CallStates.ON_CALL,
    }, 'processInboundCallAccept()');

    this.setAgentState({
      agentState: fromCallCenterVoice.AgentStates.ON_INBOUND_CALL,
    }, 'processInboundCallAccept()');

    this.callBeingAccepted = true;

    this.endCallButtonPress = false;
    const currentTrackingNumber = this.trackingNumbers.find(
      x =>
        `+${x.trackingnumber.call_number}` ===
        this.currentReservation.task.attributes.tracking_no
    );
    const ConferenceRecord =
      currentTrackingNumber &&
      currentTrackingNumber.trackingnumber &&
      currentTrackingNumber.trackingnumber.record + '' === '1'
        ? 'true'
        : 'false';
    this.inboundRingStop = new Date();
    this.reconnectWithReservation = false;

    console.log('[Call Center Voice][processInboundCallAccept] - Processing incoming call acceptance');
    // if connected by computer
    if (
      this.getConnectionMethod() ===
      fromCallCenterVoice.ConnectionMethods.COMPUTER
    ) {

      const accountIdAndLocationId = this.getAccountIdAndLocationIdFromReservation();

      let options = {
        EndConferenceOnExit: false,
        Beep: false,
        ConferenceStatusCallback: `https://${APP_CONFIG.CALL_API_GW}/${this.envInfo}-conference-event/handler?WorkerSid=${this.worker.sid}`,
        ConferenceStatusCallbackEvent: 'start,join,leave,end',
        ConferenceRecord,
        ConferenceRecordingStatusCallback: `https://${APP_CONFIG.CALL_API_GW}/${this.envInfo}/v1${APP_CONFIG.TWILIO.STATUS_URL}?log_id=${this.currentReservation.task.attributes.log_id}`,
      };
      if (
        this.connectedVia === fromCallCenterVoice.ConnectionMethods.PHONE ||
        this.connectedVia === fromCallCenterVoice.ConnectionMethods.VOIP
      ) {
        this.dialerNumber = this.dialerNumber.toString();
        if (
          this.dialerNumber.includes('@') &&
          !this.dialerNumber.includes('sip:')
        ) {
          this.dialerNumber = 'sip:' + this.dialerNumber;
        }
        options['To'] = this.dialerNumber;
      }
      this.cpLoggingService.startRecordingPerformance(
        'inbound.call.reservation.accept.duration',
        {
          ...accountIdAndLocationId
        }
      );
      this.currentReservation.conference(
        null,
        null,
        null,
        null,
        (error, reservation) => {
          this.cpLoggingService.stopRecordingPerformance('inbound.call.reservation.accept.duration');
          this.cpLoggingService.startRecordingPerformance(
            'inbound.call.agent.status.change.duration',
            {
              ...accountIdAndLocationId
            }
          );
          if (error) {
            this.bugsnagService.notify(error);
            //don't change to on-call status if the conference fails
            this.showTopToast = true;
            this.topToastMessages = [
              {
                message: 'Call cascaded due to end of ring period',
                type: TopToastComponent.NORMAL,
              },
            ];
            this.cpLoggingService.registerEvent(
              'inbound.call.reservation.accept',
              'failure',
              {
                ...accountIdAndLocationId
              }
            );
            return;
          }
          this.cpLoggingService.registerEvent(
            'inbound.call.reservation.accept',
            'success',
            {
              ...accountIdAndLocationId
            }
          );
          this.callBeingAccepted = true;
          //if the conference succeeded proceed to change the status
          this.setAgentState({
            agentState: fromCallCenterVoice.AgentStates.ON_INBOUND_CALL,
          }, 'inboundCallAccepted()');
        }, // from, postWorkActivitySid, timeout, to, resultCallback
        options, // Similar parameters as Participants creation, for the Worker Conference Leg
      );
    }
    //Resetting the agentStatePriorCall values since this information is only required for OB calls from Inactive status
    this.sharedService.agentStatePriorCall$.next({ status: '', activity: '' });
  }

  /**
   * Show an error message if AudioContext fails to initialize
   */
  private showAudioContextError(): void {
    this.topToastMessages = [
      {
        message: 'Unable to initialize audio. Please try refreshing the page or check your browser permissions.',
        type: TopToastComponent.ERROR,
      },
    ];
    this.showTopToast = true;
    this.cd.detectChanges();
  }

  public inboundCallRejected(event?: MouseEvent, isCallback?): void {
    this.store.dispatch(
      new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false)
    );

    this.bugsnagService.leaveBreadcrumb('inboundCallRejected', {
      callSid: this.currentReservation?.task?.attributes?.call_sid,
      callNumber: this.currentReservation?.task?.attributes?.caller,
      destinationNumber: this.currentReservation?.task?.attributes?.to,
      callStatus: this.currentReservation?.reservationStatus,
      callChannel: this.currentReservation?.task?.attributes?.taskchannel,
      callType: this.currentReservation?.task?.attributes?.call_type,
    });

    console.log('[Call Center Voice][inboundCallRejected] - Incoming call rejected');
    let workerVoiceChannel = this.workerChannels.find(
      (channel: TwilioWorkerChannel) => {
        return channel.taskChannelUniqueName === 'voice';
      }
    );

    let rejectedActivity: any = this.getWorkerActivityByFriendlyName(
      REJECTED_ACTIVITY_FRIENDLY_NAME
    );
    let readyActivity: any = this.getWorkerActivityByFriendlyName(
      READY_ACTIVITY_FRIENDLY_NAME
    );
   /* Since admis can decide the status after reject of a call we store that into a variable since, the activity
    sid needs to be passed through the reject function */
    let activityForReject = this.callCenterConfiguration.on_reject_inactive === 1 ? rejectedActivity : readyActivity;

    if (!workerVoiceChannel) {
      this.bugsnagService.notify(
        new Error(
          "Missing worker's voice channel."
        )
      );
      return console.error(
        "[Call Center Voice]: Missing worker's voice channel. Please contact support."
      );
    }
    let workerVoiceChannelSid: string = workerVoiceChannel.sid;
    let workerSid: string = this.worker.sid;
    this.inboundRingStop = new Date();
    if (isCallback) {
      this.showCallBackPopup = false;
      this.store.dispatch(
        new fromCallCenterVoiceAction.ToggleInboundCallbackPopUp(false)
      );
      if (this.audio) {
        this.audio.pause();
        this.audio = null;
      }
      workerVoiceChannel = this.workerChannels.find(
        (channel: TwilioWorkerChannel) => {
          return channel.taskChannelUniqueName === 'callback';
        }
      );
      workerVoiceChannelSid = workerVoiceChannel?.sid || '';
    }
    this.twilioWorkerChannelsProvider
      .update({
        workerSid,
        workerchannelSid: workerVoiceChannelSid,
        body: {
          available: false,
        },
      })
      .subscribe(
        (updatedWorkerChannel: TwilioWorkerChannel) => {
          this.workerChannels = this.workerChannels.map(workerChannel => {
            if (workerChannel.sid === updatedWorkerChannel.sid) {
              return updatedWorkerChannel;
            }
            return workerChannel;
          });
          if (this.currentReservation) {
            const accountIdAndLocationId = this.getAccountIdAndLocationIdFromReservation();

            // The documentation says the first parameter (activitySid)is optional but we discovered it is not
            this.currentReservation.reject(activityForReject.sid,
              (error, reservation) => {
                this.cpLoggingService.registerEvent(
                  'inbound.call.reservation.reject',
                  'success',
                  {
                    ...accountIdAndLocationId
                  }
                );
                if (error) {
                  this.bugsnagService.notify(error);
                  console.error('[Call Center Voice]: Current reservation is already rejected: ', error);
                } else {
                  console.log('[Call Center Voice]: Current reservation rejected', reservation);
                }
              }
            );
            this.currentReservation = null;
          }
        },
        error => {
          this.bugsnagService.notify(error);
          console.error(error);
          const accountIdAndLocationId = this.getAccountIdAndLocationIdFromReservation();
          this.cpLoggingService.registerEvent(
            'inbound.call.reservation.reject',
            'failure',
            {
              ...accountIdAndLocationId
            }
          );
        }
      );
  }

  public inboundVideoCallRejected(event?: MouseEvent, isCallback?): void {
    this.bugsnagService.leaveBreadcrumb('inboundVideoCallRejected', {
      callSid: this.currentReservation?.task?.attributes?.call_sid,
      callNumber: this.currentReservation?.task?.attributes?.caller,
      destinationNumber: this.currentReservation?.task?.attributes?.to,
      callStatus: this.currentReservation?.reservationStatus,
      callChannel: this.currentReservation?.task?.attributes?.taskchannel,
      callType: this.currentReservation?.task?.attributes?.call_type,
    })
    console.log('[Call Center Video Call][inboundCallRejected] - Incoming video call rejected');
    this.store.dispatch(
      new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false) // Create a ToggleInboundVideoCallPopUp
    );
    let workerVoiceChannel = this.workerChannels.find(
      (channel: TwilioWorkerChannel) => {
        return channel.taskChannelUniqueName === 'video';
      }
    );

    this.showInboundVideoCallPopup = false;
    if (this.audio) {
      this.audio.pause();
      this.audio = null;
      this.inboundRingStop = new Date();
    }

    if (!workerVoiceChannel) {
      this.bugsnagService.notify(
        new Error(
          "Missing worker's voice channel."
        )
      );
      return console.error(
        "[Call Center Video Call]: Missing worker's voice channel. Please contact support."
      );
    }

    /**
     * Determine what the worker activity should be AFTER the reservation is rejected.
     * If the call center configuration says to set the worker to inactive, then use the
     * rejected activity. Otherwise, use the ready activity.
     */
    const rejectedActivity: any = this.getWorkerActivityByFriendlyName(REJECTED_ACTIVITY_FRIENDLY_NAME);
    const readyActivity: any = this.getWorkerActivityByFriendlyName(READY_ACTIVITY_FRIENDLY_NAME);
    const onInactiveAfterReject = Number(this.callCenterConfiguration.on_reject_inactive);
    const activityForReject = onInactiveAfterReject === 1 ? rejectedActivity : readyActivity;

    let workerVoiceChannelSid: string = workerVoiceChannel.sid;
    let workerSid: string = this.worker.sid;
    this.twilioWorkerChannelsProvider
      .update({
        workerSid,
        workerchannelSid: workerVoiceChannelSid,
        body: {
          /**
           * In call center voice, this is set to false to avoid a race condition
           * scenario.  However, in video calls, we want to keep the channel
           * available so that an agent can accept the next reservation if they
           * are the only agent available.
           */
          available: true,
        },
      })
      .subscribe(
        (updatedWorkerChannel: TwilioWorkerChannel) => {
          this.workerChannels = this.workerChannels.map(workerChannel => {
            if (workerChannel.sid === updatedWorkerChannel.sid) {
              return updatedWorkerChannel;
            }
            return workerChannel;
          });
          if (this.currentReservation) {

            this.currentReservation.reject(activityForReject.sid, (error, reservation) => {
              if (error) {
                this.bugsnagService.notify(error);
                console.error('[Call Center Voice]: Current reservation is already rejected: ', error);
              }
              this.currentReservation = null;
              this.cd.markForCheck();
            });

          }
        },
        error => {
          this.bugsnagService.notify(error);
          console.error(error);
        }
      );
  }

  /**
   * Unmute a call
   *
   * @private
   * @returns {void}
   *
   * @memberOf DashboardComponent
   */
  private unmute() {
    this.store.dispatch(new fromCallCenterVoiceAction.CallUnmute());
  }

  private getSession(): void {
    this.session$
      .pipe(filter((sessionState: fromSession.SessionState) => sessionState.loaded)
      ,map((sessionState: fromSession.SessionState) => sessionState.session)
      ,takeUntil(this.ngUnsubscribe))
      .subscribe((session: SessionData) => {
        console.log('[Call Center Voice][getSession] - session updated => ', session);
        this.session = session;
        this.parentId = session.user.parent_id;
        this.getCoolDownTime();
      });
  }

  private getCoolDownTime() {
    this.store.dispatch(
      new fromConfigureCallCenterActions.GetCallCenterConfiguration(
        this.parentId
      )
    );
  }

  private attachToOutBoundCallFromPopCard() {
    this.bugsnagService.leaveBreadcrumb('attachToOutBoundCallFromPopCard',{
      callSid: this.currentReservation,
      callNumber: this.callToWhenChange,
      destinationNumber: this.callToWhenChange,
      callStatus: this.currentCallState,
    });
    this.callCenterVoice$
      .pipe(filter(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.outboundCallFromCard
      )
      ,map(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.agentStateData
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe((agentStateData: IFollowupCall) => {
        this.leadId = agentStateData.leadId;
        this.ledgerId = agentStateData.ledgerId;
        this.customerId = agentStateData.customerId;
        this.leadLocationId = agentStateData.leadLocationId;
        this.ledgerLocationId = agentStateData.ledgerLocationId;
        this.leadESId = agentStateData.leadESId;
        this.customerESId = agentStateData.customerESId;
        this.collectionReportId = agentStateData.reportId;
        this.collectionCallType = agentStateData.callType;
        this.isCallSidUsedAsTask = agentStateData.isCallSidUsedAsTask;
        this.isNewIC = agentStateData.isNewIC;
        this.newFromCall = agentStateData.newFromCall;
        if (!this.isLocationLogin && this.connectedVia === fromCallCenterVoice.ConnectionMethods.COMPUTER) {
          // Use cached permission status from DeviceService instead of directly checking permissions
          if (this.deviceService.getAudioPermissionStatus()) {
            this.setCallState({
              callState: fromCallCenterVoice.CallStates.DIALING,
              callStateData: agentStateData,
            });
          } else {
            let inactiveActivity: any = this.workspaceActivities.find(
              (workspaceActivity: any) => {
                return workspaceActivity.friendlyName === NOMIC_ACTIVITY_FRIENDLY_NAME;
              }
            );
            this.setAgentState({
              agentState: fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE,
              agentStateData: inactiveActivity,
            }, 'attachToOutBoundCallFromPopCard()');
          }
        } else {
          console.log('[Call Center Voice][attachToOutBoundCallFromPopCard] - Dispatching Dialing');
          this.setCallState({
            callState: fromCallCenterVoice.CallStates.DIALING,
            callStateData: agentStateData,
          });
        }
      });
  }

  public getWorkspaceActivities(): void {
    this.workspaceActivities$
      .pipe(filter(
        (
          workspaceActivitiesState: fromWorkspaceActivities.WorkspaceActivitiesState
        ) => workspaceActivitiesState.loaded
      )
      ,map(
        (
          workspaceActivitiesState: fromWorkspaceActivities.WorkspaceActivitiesState
        ) => workspaceActivitiesState.activities
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe((workspaceActivities: Array<any>) => {
        console.log('[Call Center Voice][getWorkspaceActivities] - workspaceActivities updated => ', workspaceActivities);
        this.workspaceActivities = workspaceActivities;
      });
  }

  public getWorkerClient(): void {
    this.workerClient$
      .pipe(filter(
        (workerClientState: fromWorkerClient.WorkerClientState) =>
          (workerClientState.loaded || workerClientState.tokenUpdated) && !workerClientState.workerUpdatedFromTwilio
      )
      ,map(
        (workerClientState: fromWorkerClient.WorkerClientState) =>
          workerClientState.worker
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe((workerClient: any) => {
        console.log(
          '[Call Center Voice][getWorkerClient] - workerClient updated => ',
          workerClient
        );
        this.workerClient = workerClient;
        this.worker = workerClient;
        this.cleanWrapping();
      });
  }
  private updateWorkerToLatestState(): void {
    this.workerClient$
      .pipe(filter(
        (workerClientState: fromWorkerClient.WorkerClientState) =>
          workerClientState.loaded && workerClientState.workerUpdatedFromTwilio
      )
      ,map(
        (workerClientState: fromWorkerClient.WorkerClientState) =>
          workerClientState.currentWorker
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe((currentWorker: any) => {
        this.worker = currentWorker;

      });
  }
  private getWorkspaceWorkers(): void {
    this.workspaceWorkers$
      .pipe(filter(
        (workspacWorkersState: fromWorkspaceWorkers.WorkspaceWorkersState) =>
          workspacWorkersState.loaded
      )
      ,map(
        (workspaceWorkersState: fromWorkspaceWorkers.WorkspaceWorkersState) =>
          workspaceWorkersState.workers
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe(workspaceWorkers => {
        this.workspaceWorkers = workspaceWorkers;
        if (
          this.connectedVia === fromCallCenterVoice.ConnectionMethods.PHONE ||
          this.connectedVia === fromCallCenterVoice.ConnectionMethods.VOIP
        ) {
          for (let index in this.workspaceWorkers) {
            if (
              this.workspaceWorkers[index].attributes.email ===
              this.session.user.email
            ) {
            }
          }
        }
      });
  }

  private getTrackingNumbers() {
    this.trackingNumbers$
      .pipe(filter((state: fromTrackingNumbers.TrackingNumbersState) => state.loaded)
      ,map(
        (state: fromTrackingNumbers.TrackingNumbersState) =>
          state.trackingNumbers
      )
      ,take(1))
      .subscribe((trackingNumbers: Array<TrackingnumberListItem>) => {
        this.trackingNumbers = trackingNumbers;
      });
  }

  /**
   * We attach to workspaceTasks just 1 at the init of the call center voice to clean up the
   * wrapping tasks if there are some.
   * @private
   * @memberof CallCenterVoiceComponent
   */
  private attachToWrappingTaskToClean(): void {
    this.workspaceTasks$
      .pipe(filter(
        (workspaceTasksState: fromWorkspaceTasks.WorkspaceTasksState) =>
          workspaceTasksState.loaded
      )
      ,map(
        (workspaceTasksState: fromWorkspaceTasks.WorkspaceTasksState) =>
          workspaceTasksState.entities
      )
      ,take(1))
      .subscribe((workspaceTasks: any) => {
        this.cleanWrapping();
      });
  }

  /**
   * This function is going to be executed once the call voice center is initialized
   * and is going to search wrapping tasks for the current agent and complete them.
   * @private
   * @memberof CallCenterVoiceComponent
   */
  private cleanWrapping() {
    if (this.workerClient && !this.wrappingTasksAlreadyClean) {
      this.workerClient.fetchReservations((error, reservations) => {
        if (error) {
          console.log(
            '[Call Center Voice][cleanWrapping] - Worker fetchReservations had an error: ' +
              error.response +
              ' with message: ' +
              error.message
          );
        }
        reservations.data.filter(res => {
          if (
            res.reservationStatus === 'accepted' &&
            res.task.assignmentStatus === 'wrapping'
          ) {
            this.wrappingTasks.push(res);
          }
        });
        if (this.wrappingTasks.length > 0) {
          this.wrappingTasks.forEach(wrappingTask => {
            if (wrappingTask) {
              this.workerClient.completeTask(
                wrappingTask.task.sid,
                (error, wrappingTask) => {
                  if (error) {
                    console.log(
                      '[Call Center Voice][cleanWrapping] - There was an error completing the current task => ',
                      error
                    );
                  } else {
                    this.wrappingTasksAlreadyClean = true;
                    console.log(
                      '[Call Center Voice][cleanWrapping] - Current task set to complete successfully'
                    );
                  }
                }
              );
            }
          });
        }
      });
    }
  }
  public getWorkerChannels(): void {
    this.workerChannels$
      .pipe(filter(
        (workerChannelsState: fromWorkerChannels.WorkerChannelsState) =>
          workerChannelsState.loaded
      )
      ,map(
        (workerChannelsState: fromWorkerChannels.WorkerChannelsState) =>
          workerChannelsState.channels
      )
      ,distinctUntilChanged()
      ,takeUntil(this.ngUnsubscribe))
      .subscribe((workerChannels: Array<TwilioWorkerChannel>) => {
        console.log(
          '[Call Center Voice][getWorkerChannels] - workerChannels updated => ',
          workerChannels
        );
        this.workerChannels = workerChannels;
      });
  }
  private attachWorkspaceClientHandlers(): void {
    this.twilioEventHandlerService.workSpaceClientDisconnected$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      () => {
        // Workspace Disconnected
        this.checkConnection();
      }
    );
  }

  private attachWorkerClientHandlers() {
    this.twilioEventHandlerService.workerClientReady$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      (worker: any) => {
        this.bugsnagService.leaveBreadcrumb('WorkerClientReady', {
          workerSid: worker?.sid,
          workerName: worker?.attributes.name,
          workerActivity: worker?.activityName,
        });
        this.worker = worker;
        this.checkWorkerConnectionMethod(this.worker);
        if (worker.attributes && worker.attributes.hidden) {
          this.store.dispatch(new fromConfigurationAction.SetLocationClient());
          this.setLocationClientStatus();
        }
      }
    );
    this.twilioEventHandlerService.workerClientDisconnected$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(() => {
      this.bugsnagService.leaveBreadcrumb('WorkerClientDisconnected', {
        workerSid: this.worker?.sid,
        workerName: this.worker?.attributes.name,
        workerActivity: this.worker?.activityName,
      });
      // Worker disconnected handler
      this.checkConnection();
    });

    this.twilioEventHandlerService.workerClientReservationCreated$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      async (reservation: any) => {
        this.bugsnagService.leaveBreadcrumb('WorkerClientReservationCreated', {
          reservationId: reservation?.task?.sid,
          workerId: reservation?.workerSid,
          reservationStatus: reservation?.reservationStatus,
        });

        // Log reservation state transition and check for race conditions
        this.logReservationStateTransition('Created', reservation);
        this.logReservationTiming(reservation, 'Created');

        // Start call pop presentation timing
        this.trackCallPopPresentationTiming(reservation, 'start');

        this.removeHangupWorkerAttribute();
        // Worker reservation.created handler
        switch (reservation.task.attributes.taskchannel) {
          case 'voice': {
            console.log(
              '[Call Center Voice][workerClientReservationCreated$.subscribe] - Worker has been assigned a voice reservation => ',
              reservation
            );
            this.bugsnagService.leaveBreadcrumb('ReservationCreatedAssigned', {
              taskSid: reservation?.task?.sid,
              worker: reservation?.workerName,
              task: reservation?.task,
            });

            // Call pop presentation tracking
            this.bugsnagService.leaveBreadcrumb('CallPopPresentationAttempt', {
              reservationId: reservation?.task?.sid,
              taskChannel: reservation?.task?.attributes?.taskchannel,
              shouldShowPopup: true,
              currentAgentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
              workerAvailable: this.workerChannels?.find(ch => ch.taskChannelUniqueName === 'voice')?.available,
              hasCurrentReservation: !!this.currentReservation,
              currentReservationId: this.currentReservation?.task?.sid,
              timestamp: new Date().toISOString()
            });

            this.currentReservation = reservation;
            this.store.dispatch(new fromCallCenterVoiceAction.ToggleDialPad(false));

            //Resetting the agentStatePriorCall values since this information is only required for OB calls from Inactive status
            this.sharedService.agentStatePriorCall$.next({ status: '', activity: '' });
            this.isLocationLogin = UtilsHelper.isLocationLogin();
            if (!this.isLocationLogin && this.connectedVia === fromCallCenterVoice.ConnectionMethods.COMPUTER) {
              // Use cached permission status instead of directly checking permissions
              const micPermissionGranted = this.deviceService.getAudioPermissionStatus();
              if (micPermissionGranted) {
                console.log(
                  '[Call Center Voice][workerClientReservationCreated$.subscribe] - Worker has been assigned a voice reservation => ',
                  reservation
                );

                this.bugsnagService.leaveBreadcrumb('ReservationCreatedAssigned', {
                  taskSid: reservation?.task?.sid,
                  worker: reservation?.workerName,
                  task: reservation?.task,
                });
                // this variable assignation impacts in the inbound-call-pop-up component
                // in the ngOnChanges method

                this.refreshObject = Date.now();
                  if (this.showErrorConnectionToast) {
                    this.reconnectWithReservation = true;
                    this.showErrorConnectionToast = false;
                    this.couldNotReconnect = false;
                  }
                  this.setCallState({
                    callState: fromCallCenterVoice.CallStates.INCOMING,
                  });

                  // Track call pop presentation timing
                  this.trackCallPopPresentationTiming(reservation, 'popup_requested');

                  this.bugsnagService.leaveBreadcrumb('CallPopPresentationRequested', {
                    reservationId: reservation?.task?.sid,
                    callStateSet: true,
                    timestamp: new Date().toISOString()
                  });
                  
                  this.cpLoggingService.registerEvent(
                    'call.popup.presentation.requested',
                    'info',
                    {
                      reservationId: reservation?.task?.sid,
                      taskChannel: reservation?.task?.attributes?.taskchannel
                    }
                  );

              } else {
                // Handle permission denial
                let noMicActivity: any = this.workspaceActivities.find(
                  (workspaceActivity: any) => {
                    return workspaceActivity.friendlyName === NOMIC_ACTIVITY_FRIENDLY_NAME;
                  }
                );
                this.currentReservation.reject(
                  noMicActivity.sid,
                  (error, reservation) => {
                    this.setAgentState({
                      agentState: fromCallCenterVoice.AgentStates.INACTIVE,
                      agentStateData: noMicActivity.sid,
                    }, 'workerClientReservationCreated$.subscribe - voice');
                    this.setCallState({
                      callState: fromCallCenterVoice.CallStates.IDLE,
                    });
                    this.currentReservation = null;
                  });
              }
            } else {
              this.refreshObject = Date.now();
              if (this.showErrorConnectionToast) {
                this.reconnectWithReservation = true;
                this.showErrorConnectionToast = false;
                this.couldNotReconnect = false;
              }
              this.setCallState({
                callState: fromCallCenterVoice.CallStates.INCOMING,
              });

              // Track call pop presentation timing
              this.trackCallPopPresentationTiming(reservation, 'popup_requested');

              this.bugsnagService.leaveBreadcrumb('CallPopPresentationRequested', {
                reservationId: reservation?.task?.sid,
                callStateSet: true,
                timestamp: new Date().toISOString()
              });
              
              this.cpLoggingService.registerEvent(
                'call.popup.presentation.requested',
                'info',
                {
                  reservationId: reservation?.task?.sid,
                  taskChannel: reservation?.task?.attributes?.taskchannel
                }
              );
            }
            this.cd.markForCheck();
            break;
          }
          case 'video': {
            const micPermissionEnabled = await this.deviceService.checkMicPermission();
            if (!micPermissionEnabled) {
              // Disable Inbound Video popup
              this.showInboundVideoCallPopup = false;
              this.currentReservation = reservation;
              let noMicActivity: any = this.workspaceActivities.find(
                (workspaceActivity: any) => {
                  return workspaceActivity.friendlyName === NOMIC_ACTIVITY_FRIENDLY_NAME;
                }
              );
              this.currentReservation.reject(
                noMicActivity.sid,
                (error, reservation) => {
                  if (error) {
                    this.bugsnagService.notify(error);
                  }
                  this.setAgentState({
                    agentState: fromCallCenterVoice.AgentStates.INACTIVE,
                    agentStateData: noMicActivity.sid,
                  }, 'workerClientReservationCreated$.subscribe');
                  this.setCallState({
                    callState: fromCallCenterVoice.CallStates.IDLE,
                  });
                  this.currentReservation = null;
                });

              this.cd.markForCheck();
              break;
            } else {
              console.log(
                '[Call Center Video][workerClientReservationCreated$.subscribe] - Worker has been assigned a video reservation => ',
                reservation
              );
              this.bugsnagService.leaveBreadcrumb('ReservationCreatedAssigned', {
                taskSid: reservation?.task?.sid,
                worker: reservation?.workerName,
                task: reservation?.task,
              });
              this.refreshObject = Date.now();
              this.currentReservation = reservation;
              let location = this.fullLocationInfo.find(location => location.location_id === reservation.task.attributes.location_id);
              this.currentReservation.task.attributes.location_name = location.location_name;
              this.showInboundVideoCallPopup = true;
              this.playIncomingAudio(reservation.task.attributes.taskchannel);
              this.cd.markForCheck();
              this.displayChromeAlertForVideo(this.currentReservation.task.attributes);
            }
            break;
          }
          case 'custom1': {
            this.currentReservation = reservation;
            console.log(
              '[Call Center Voice][workerClientReservationCreated$.subscribe] - Worker has been assigned an outbound reservation => ',
              reservation
            );
            this.bugsnagService.leaveBreadcrumb('ReservationCreatedAssigned', {
              taskSid: reservation?.task?.sid,
              worker: reservation?.workerName,
              task: reservation?.task,
            });

            if (this.cancelOutboundCall) {
              console.log(
                '[Call Center Voice][workerClientReservationCreated$.subscribe] - Cancelling outbound reservation'
              );

              if (this.moveToCoolDown) {
                let workspaceActivity = this.workspaceActivities.find(
                  (workspaceActivity: any) => {
                    return (
                      workspaceActivity.friendlyName === COOLDOWN_ACTIVITY_FRIENDLY_NAME
                    );
                  });
                this.updateWorkerStatus(workspaceActivity);
                this.setCallState({
                  callState: fromCallCenterVoice.CallStates.IDLE,
                });
                this.setAgentState({
                  agentState: fromCallCenterVoice.AgentStates.COOLDOWN,
                }, 'workerClientReservationCreated$.subscribe');
                this.moveToCoolDown = false;
                return;
              }
            }
            let sid: string = reservation.task.sid;
            let to: string = reservation.task.attributes.to;
            let from: string = reservation.task.attributes.from;
            let authToken: string = this.session.session_id;
            let statusCallbackUrl: string = `https://${APP_CONFIG.CALL_API_GW}/${APP_CONFIG.APP_STACK}-conference-event/handler`;
            let agentId: string = `${this.session.user.user_id}`;
            let agentFirstName: string = encodeURI(
              `${this.session.user.firstname}`
            );
            let agentLastName: string = encodeURI(
              `${this.session.user.lastname}`
            );

            let leadParams = {
              leadId: this.leadId,
              ledgerId: this.ledgerId,
              leadLocationId: this.leadLocationId,
              ledgerLocationId: this.ledgerLocationId,
              customerId: this.customerId,
              leadESId: this.leadESId,
              customerESId: this.customerESId,
            };

            const stringParams: any = {
              agentId,
              agentFirstName,
              agentLastName,
              ToPhone: to,
              Sid: sid,
              authToken,
              statusUrl: statusCallbackUrl,
            };
            if (this.collectionReportId) {
              stringParams.reportId = this.collectionReportId;
            }
            if (this.collectionCallType) {
              stringParams.callType = this.collectionCallType;
            }
            if (this.isCallBack) {
              stringParams.callType = 'outbound_callback';
              this.reservationUrl += `&callback_task_sid=${this.callBackTaskSid}`;
            }
            this.callUrl = `${this.url}`;
            this.callUrl += this.reservationUrl;
            Object.keys(leadParams).map(key => {
              if (!!leadParams[key]) {
                this.callUrl += '&' + key + `=${leadParams[key]}`;
              }
            });
            if (to.includes('@') && !to.includes('sip:')) {
              to = 'sip:' + to;
            }
            to = encodeURIComponent(to);
            if (this.callTo.includes('@') && !this.callTo.includes('sip:')) {
              this.callTo = 'sip:' + this.callTo;
            }
            this.callUrl += `&${UtilsHelper.objectToQueryString(
              stringParams
            ).slice(1)}`;
            if (this.isCallPlaced) {
              this.isCallPlaced = false;
              reservation.call(
                from, // callfrom
                this.callUrl, // callUrl
                this.callUrl, // callStatusCallbackUrl
                'true', // callAccept
                '', // callRecord
                this.callTo,
                (error, reservation) => {
                  if (error) {
                    this.endCall();
                    this.bugsnagService.notify(error);
                    return console.error(
                      '[Call Center Voice][workerClientReservationCreated$.subscribe] - There was an error in while calling the Outbound reservation => ',
                      error
                    );
                  }
                  console.log(
                    '[Call Center Voice][workerClientReservationCreated$.subscribe] - Outbound reservation call process was successful',
                    reservation
                  );
                }
              );
            }
            delete this.collectionCallType;
            delete this.collectionReportId;
            break;
          }
          // Everytime we have a callback task this will show the callback popup
          case 'Callback': {
            this.currentReservation = reservation;
            this.callBackTaskSid = this.currentReservation.task.sid;
            let taskAttributes = this.currentReservation.task.attributes;
            if (!this.isLocationLogin && this.connectedVia === fromCallCenterVoice.ConnectionMethods.COMPUTER) {
              // Use cached permission status instead of directly checking permissions
              const micPermissionGranted = this.deviceService.getAudioPermissionStatus();
              if (micPermissionGranted) {
                this.showCallBackChromeAlert(taskAttributes);
                this.playIncomingAudio(reservation.task.attributes.taskchannel);
                this.showCallBackPopup = true;
                this.store.dispatch(
                  new fromCallCenterVoiceAction.ToggleInboundCallbackPopUp(true)
                );
                //Resetting the agentStatePriorCall values since this information is only required for OB calls from Inactive status
                this.sharedService.agentStatePriorCall$.next({ status: '', activity: '' });
                this.cd.markForCheck();
              } else {
                // Handle permission denial
                let noMicActivity: any = this.workspaceActivities.find(
                  (workspaceActivity: any) => {
                    return workspaceActivity.friendlyName === NOMIC_ACTIVITY_FRIENDLY_NAME;
                  }
                );
                this.currentReservation.reject(
                  noMicActivity.sid,
                  (error, reservation) => {
                    this.setAgentState({
                      agentState: fromCallCenterVoice.AgentStates.INACTIVE,
                      agentStateData: noMicActivity.sid,
                    }, 'workerClientReservationCreated$.subscribe - callback');
                    this.setCallState({
                      callState: fromCallCenterVoice.CallStates.IDLE,
                    });
                    this.currentReservation = null;
                  });
              }
            } else {
              this.playIncomingAudio(reservation.task.attributes.taskchannel);
              this.showCallBackPopup = true;
              this.store.dispatch(
                new fromCallCenterVoiceAction.ToggleInboundCallbackPopUp(true)
              );
              this.showCallBackChromeAlert(taskAttributes);
              this.cd.markForCheck();
            }
            break;
          }
          case 'custom3': {
            let newReservation = { ...reservation };
            this.currentReservation = newReservation;
            // this variable assignation impacts in the inbound-call-pop-up component
            // in the ngOnChanges method
            this.refreshObject = Date.now();
            this.cd.markForCheck();
            break;
          }
          default: {
            console.log(
              '[Call Center Voice][workerClientReservationCreated$.subscribe] - Unsupported channel reservation created'
            );
            console.log(reservation);
          }
        }
      }
    );

    this.twilioEventHandlerService.workerClientReservationCanceled$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      (reservation: any) => {
        this.bugsnagService.leaveBreadcrumb('WorkerClientReservationCanceled', {
          reservationId: reservation?.task?.sid,
          workerId: reservation?.workerSid,
          reservationStatus: reservation?.reservationStatus,
        });
        
        // Enhanced state transition logging
        this.logReservationStateTransition('Canceled', reservation, {
          reason: reservation?.task?.reason,
          assignmentStatus: reservation?.task?.assignmentStatus,
          callCenterConfig: {
            on_reject_inactive: this.callCenterConfiguration.on_reject_inactive
          }
        });
        
        // Race condition detection
        this.logReservationTiming(reservation, 'Canceled');
        
        console.log(
          '[Call Center Voice][workerClientReservationCanceled$.subscribe] - Worker pending voice reservation has been canceled => ',
          reservation
        );
        console.log(
          '[Call Center Voice][workerClientReservationCanceled$.subscribe] - Metadata => ',
          {
            taskChannelUniqueName: reservation?.task?.attributes?.taskchannel,
            reason: reservation?.task?.reason
          });

        // Worker reservation.canceled handler
        /*
          CP-10378
          This function is executed when a task reservation is canceled. For
          call center voice calls, there is no situation where we want the call
          pop to remain visible after a task reservation is canceled.

          Therefore, no matter what work happens in this function, one thing that
          must be done is to close the inbound call pop.
        */

        let task = reservation.task;
        let taskChannelUniqueName = task.attributes.taskchannel;
        let reason = task.reason;

        switch (taskChannelUniqueName) {
          case 'voice': {
            this.store.dispatch(
              new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false)
            );
            this.callBeingAccepted = false;
            this.currentReservation = reservation;
            if (this.currentReservation && this.currentReservation.sid === reservation.sid) {
              this.setCallState({
                callState: fromCallCenterVoice.CallStates.END_CALL,
              });
            }

            this.inboundRingStop = new Date();
            if (reason === TTL_EXCEEDED) {

              /**
               * https://sparefoot.atlassian.net/browse/CAL-6656
               *
               * As any agent fielding inbound calls, if I miss a call because it rolls over before
               * the cascade timeout is reached, I want my status to remain in Ready so that I can
               * be Ready to accept another call without needing to take any manual actions.
               *
               * REMARKS: If this reservation is canceled because of the TTL_EXCEEDED reason, we need
               * to set the agent state to READY.  This is in contrast to the reservation reaching its
               * timeout.  In that case, we want to set the agent state to INACTIVE.
               */
              let readyActivity: any = this.workspaceActivities.find(
                (activity: any) => {
                  return (
                    activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME
                  );
                }
              );
              this.setAgentState({
                agentState: fromCallCenterVoice.AgentStates.CONNECTING_READY,
                agentStateData: readyActivity,
              }, 'workerClientReservationCanceled$.subscribe, reason: TTL_EXCEEDED');

              this.store.dispatch(
                new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false)
              );
            } else if (reason === HANG_UP_CALL_REASON || reason.indexOf('Timeout') > -1) {
              this.currentConnection && this.currentConnection.reject();
              this.currentConnection = null;
              this.currentReservation = null;
              this.onCancellingCall();
              let workspaceActivity = this.workspaceActivities.find(
                (workspaceActivity: any) => {
                  return (
                    workspaceActivity.friendlyName === COOLDOWN_ACTIVITY_FRIENDLY_NAME
                  );
                });
              this.updateWorkerStatus(workspaceActivity);
              this.callerHasHungUp = true;
              this.sharedService.callHungUp$.next(null);
            } else if (reason === SCHEDULED_CALLBACK) {
              this.currentConnection = null;
              this.currentReservation = null;
              this.setCallState({
                callState: fromCallCenterVoice.CallStates.IDLE,
              });
            } else if (this.connectedVia === fromCallCenterVoice.ConnectionMethods.PHONE ||
              this.connectedVia === fromCallCenterVoice.ConnectionMethods.VOIP) {
              this.currentConnection = null;
              this.currentReservation = null;
              if (this.callCenterConfiguration.on_reject_inactive) {
                let rejectedActivity: any = this.getWorkerActivityByFriendlyName(
                  REJECTED_ACTIVITY_FRIENDLY_NAME
                );

                // CCC-1533. This validation is due to when the caller press 9 for leaving the queue
                // and the status should stay as Ready
                if (reason !== 'leave') {
                  this.setAgentState({
                    agentState: fromCallCenterVoice.AgentStates.INACTIVE,
                    agentStateData: rejectedActivity.sid,
                  }, 'workerClientReservationCanceled$.subscribe');

                  this.voIpCallWasRejected();
                }

                this.setCallState({
                  callState: fromCallCenterVoice.CallStates.IDLE,
                });
              } else {
                let readyActivity: any = this.workspaceActivities.find(
                  (activity: any) => {
                    return (
                      activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME
                    );
                  }
                );
                // Enhanced logging before and after worker activity update
                this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateAttempt', {
                  reservationId: reservation?.task?.sid,
                  workerId: this.worker?.sid,
                  fromActivity: this.worker?.activitySid,
                  toActivity: readyActivity.sid,
                  toActivityName: readyActivity.friendlyName,
                  reservationStatus: reservation?.reservationStatus,
                  currentAgentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
                  context: 'workerClientReservationCanceled',
                  timestamp: new Date().toISOString()
                });

                this.workerClient.update(
                  'ActivitySid',
                  readyActivity.sid,
                  (error, worker) => {
                    if (error) {
                      this.bugsnagService.notify(error, {
                        severity: 'error',
                        metadata: {
                          workerActivityUpdate: {
                            reservationId: reservation?.task?.sid,
                            workerId: this.worker?.sid,
                            targetActivitySid: readyActivity.sid,
                            targetActivityName: readyActivity.friendlyName,
                            errorCode: error.code,
                            errorMessage: error.message,
                            context: 'workerClientReservationCanceled',
                            currentWorkerState: this.worker
                          }
                        }
                      });
                      
                      this.cpLoggingService.registerEvent(
                        'worker.activity.update.failed',
                        'error',
                        {
                          reservationId: reservation?.task?.sid,
                          workerId: this.worker?.sid,
                          targetActivity: readyActivity.friendlyName,
                          error: error.message,
                          errorCode: error.code,
                          context: 'workerClientReservationCanceled'
                        }
                      );
                      
                      return console.error(
                        '[Call Center Voice][workerClientReservationCanceled$.subscribe] - There was an error updating the worker\'s activity to READY => ',
                        error
                      );
                    }
                    
                    // Success logging
                    this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateSuccess', {
                      reservationId: reservation?.task?.sid,
                      workerId: worker?.sid,
                      newActivity: worker?.activitySid,
                      newActivityName: worker?.activityName,
                      context: 'workerClientReservationCanceled',
                      timestamp: new Date().toISOString()
                    });
                    
                    this.cpLoggingService.registerEvent(
                      'worker.activity.update.success',
                      'success',
                      {
                        reservationId: reservation?.task?.sid,
                        workerId: worker?.sid,
                        newActivity: worker?.activityName,
                        context: 'workerClientReservationCanceled'
                      }
                    );
                    
                    console.log(
                      '[Call Center Voice][workerClientReservationCanceled$.subscribe] - Worker\'s activity was successfully updated to READY'
                    );
                    this.worker = worker;
                  }
                );
                this.setAgentState({
                  agentState: fromCallCenterVoice.AgentStates.CONNECTING_READY,
                  agentStateData: readyActivity,
                }, 'workerClientReservationCanceled$.subscribe');
              }
            } else {
              /*
                CP-10378
                It is important that have an else branch here to ensure that
                we always close the inbound call pop.
              */
              this.currentConnection = null;
              this.currentReservation = null;
              this.setCallState({
                callState: fromCallCenterVoice.CallStates.IDLE,
              });
            }
            break;
          }
          case 'custom1':
          case 'Callback': {
            this.currentReservation = reservation;
            this.showCallBackPopup = false;
            this.store.dispatch(
              new fromCallCenterVoiceAction.ToggleInboundCallbackPopUp(false)
            );
            this.cd.markForCheck();
            if (this.audio) {
              this.audio.pause();
              this.audio = null;
            }
            break;
          }
          case 'custom3': {
            this.currentReservation = reservation;
            break;
          }
          case 'video': {
            this.currentReservation = reservation;
            this.setCallState({
              callState: fromCallCenterVoice.CallStates.END_CALL,
            });
            if (this.audio) {
              this.audio.pause();
              this.audio = null;
              this.inboundRingStop = new Date();
            }
            this.showInboundVideoCallPopup = false;
            break;
          }
          default: {
            console.log(
              '[Call Center Voice][workerClientReservationCanceled$.subscribe] - Unsupported channel reservation was canceled'
            );
            console.log(reservation);
          }
        }
      }
    );

    this.twilioEventHandlerService.workerClientReservationTimeOut$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      (reservation: any) => {
        this.bugsnagService.leaveBreadcrumb('WorkerClientReservationTimeOut', {
          reservationId: reservation?.task?.sid,
          workerId: reservation?.workerSid,
          reservationStatus: reservation?.reservationStatus,
        });
        
        // Enhanced state transition logging
        this.logReservationStateTransition('TimeOut', reservation, {
          reason: reservation?.task?.reason,
          callCenterConfig: {
            on_cascade_inactive: this.callCenterConfiguration.on_cascade_inactive
          }
        });
        
        // Race condition detection
        this.logReservationTiming(reservation, 'TimeOut');
        
        // Worker reservation.timeout handler
        let taskChannelUniqueName = reservation.task.attributes.taskchannel;
        switch (taskChannelUniqueName) {
          case 'voice': {
            this.currentReservation = reservation;
            this.inboundRingStop = new Date();
            console.log(
              '[Call Center Voice][workerClientReservationTimeOut$.subscribe] - Worker pending inbound reservation timed out => ',
              reservation
            );
            this.store.dispatch(
              new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false)
            );
            this.setCallState({
              callState: fromCallCenterVoice.CallStates.IDLE,
            });
            if (this.callCenterConfiguration.on_cascade_inactive) {
              this.setAgentState({
                agentState: fromCallCenterVoice.AgentStates.DISCONNECTING,
              }, 'workerClientReservationTimeOut$.subscribe');
            } else {
              let readyActivity: any = this.workspaceActivities.find(
                (activity: any) => {
                  return activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME;
                }
              );
              this.setAgentState({
                agentState: fromCallCenterVoice.AgentStates.CONNECTING_READY,
                agentStateData: readyActivity,
              }, 'workerClientReservationTimeOut$.subscribe');
            }
            this.currentReservation = null;
            break;
          }
          case 'video': {
            this.showInboundVideoCallPopup = false;
            this.currentReservation = reservation;
            if (this.audio) {
              this.audio.pause();
              this.audio = null;
              this.inboundRingStop = new Date();
            }
            this.cd.detectChanges();
            break;
          }
          case 'custom1': {
            console.log(
              '[Call Center Voice][workerClientReservationTimeOut$.subscribe] - Worker pending outobund reservation timed out => ',
              reservation
            );
            this.currentReservation = reservation;
            break;
          }
          case 'Callback': {
            this.currentReservation = reservation;
            this.showCallBackPopup = false;
            this.store.dispatch(
              new fromCallCenterVoiceAction.ToggleInboundCallbackPopUp(false)
            );
            this.cd.markForCheck();
            if (this.audio) {
              this.audio.pause();
              this.audio = null;
            }
            break;
          }
          default: {
            console.log(
              '[Call Center Voice][workerClientReservationTimeOut$.subscribe] - Unsupported channel reservation timeout'
            );
            console.log(reservation);
          }
        }
      }
    );

    this.twilioEventHandlerService.workerClientReservationRascinded$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      (reservation: any) => {
        this.bugsnagService.leaveBreadcrumb('WorkerClientReservationRascinded', {
          reservationId: reservation?.task?.sid,
          workerId: reservation?.workerSid,
          reservationStatus: reservation?.reservationStatus,
        });
        
        // Enhanced state transition logging
        this.logReservationStateTransition('Rescinded', reservation, {
          reason: reservation?.task?.reason,
          rescindedBy: 'anotherAgent',
          inboundRingStop: this.inboundRingStop
        });
        
        // Race condition detection
        this.logReservationTiming(reservation, 'Rescinded');
        
        // Worker reservation.rescinded handler
        switch (reservation.task.attributes.taskchannel) {
          case 'voice': {
            this.currentReservation = reservation;
            this.inboundRingStop = new Date();
            console.log(
              '[Call Center Voice][workerClientReservationRascinded$.subscribe] - voice - Worker pending reservation was rescinded => ',
              reservation
            );
            this.store.dispatch(
              new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false)
            );
            this.setCallState({
              callState: fromCallCenterVoice.CallStates.IDLE,
            });
            let readyActivity: any = this.workspaceActivities.find(
              (activity: any) => {
                return activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME;
              }
            );
            this.setAgentState({
              agentState: fromCallCenterVoice.AgentStates.CONNECTING_READY,
              agentStateData: readyActivity,
            }, 'workerClientReservationRascinded$.subscribe');
            this.currentReservation = null;
            this.topToastMessages = [
              {
                message: 'The call has been accepted by another agent.',
                type: TopToastComponent.BIG_NORMAL,
              },
            ];
            this.showTopToast = true;
            break;
          }
          case 'Callback': {
            this.currentReservation = reservation;
            if (!this.callBackRescindedOnAccept) {
              this.showCallBackPopup = false;
              this.store.dispatch(
                new fromCallCenterVoiceAction.ToggleInboundCallbackPopUp(false)
              );
              if (this.audio) {
                this.audio.pause();
                this.audio = null;
              }
              this.topToastMessages = [
                {
                  message: 'The call has been accepted by another agent.',
                  type: TopToastComponent.BIG_NORMAL,
                },
              ];
              this.showTopToast = true;
              console.log(
                '[Call Center Voice][workerClientReservationRascinded$.subscribe] - callback - Worker pending reservation was rescinded => ',
              );
            }
            break;
          }
          case 'custom1': {
            console.log(
              '[[Call Center Voice][workerClientReservationRascinded$.subscribe] - custom1 - Worker pending reservation was rescinded => ',
              reservation
            );
            break;
          }
          default: {
            console.log(
              '[Call Center Voice][workerClientReservationRascinded$.subscribe] - default - Unsupported channel reservation was rescinded'
            );
            console.log(reservation);

            this.store.dispatch(fromNotificationAlertsAction.doRemoveAlert({ taskSid: reservation.taskSid }));

          }
        }
      }
    );

    this.twilioEventHandlerService.workerClientReservationAccepted$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      (reservation: any) => {
        this.bugsnagService.leaveBreadcrumb('WorkerClientReservationAccepted', {
          reservationId: reservation?.task?.sid,
          workerId: reservation?.workerSid,
          reservationStatus: reservation?.reservationStatus,
        });
        // Worker reservation.accepted handler
        if (isCallCommunicationChannel(reservation.task.attributes.taskchannel)){

          this.currentWorkerOnCallStatus = reservation.task.attributes.taskchannel;
          this.currentReservation = reservation;
          console.log(
            '[Call Center Voice][workerClientReservationAccepted$.subscribe] - Worker has accepted a reservation => ',
            reservation
          );
          if (this.currentReservation && this.currentReservation.task) {
            let onCallActivity: any = this.workspaceActivities.find(
              (activity: any) => {
                return activity.friendlyName === ONCALL_ACTIVITY_FRIENDLY_NAME;
              }
            );
            // Enhanced logging before and after worker activity update
            this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateAttempt', {
              reservationId: reservation?.task?.sid,
              workerId: this.worker?.sid,
              fromActivity: this.worker?.activitySid,
              toActivity: onCallActivity.sid,
              toActivityName: onCallActivity.friendlyName,
              reservationStatus: reservation?.reservationStatus,
              currentAgentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
              timestamp: new Date().toISOString()
            });

            // Start performance timing for worker activity update
            this.trackWorkerActivityUpdateTiming(
              'start', 
              onCallActivity.sid, 
              onCallActivity.friendlyName, 
              reservation?.task?.sid
            );

            this.workerClient.update(
              'ActivitySid',
              onCallActivity.sid,
              (error, worker) => {
                // End performance timing
                this.trackWorkerActivityUpdateTiming(
                  'end', 
                  onCallActivity.sid, 
                  onCallActivity.friendlyName, 
                  reservation?.task?.sid,
                  error
                );

                if (error) {
                  this.bugsnagService.notify(error, {
                    severity: 'error',
                    metadata: {
                      workerActivityUpdate: {
                        reservationId: reservation?.task?.sid,
                        workerId: this.worker?.sid,
                        targetActivitySid: onCallActivity.sid,
                        targetActivityName: onCallActivity.friendlyName,
                        errorCode: error.code,
                        errorMessage: error.message,
                        currentWorkerState: this.worker
                      }
                    }
                  });
                  
                  this.cpLoggingService.registerEvent(
                    'worker.activity.update.failed',
                    'error',
                    {
                      reservationId: reservation?.task?.sid,
                      workerId: this.worker?.sid,
                      targetActivity: onCallActivity.friendlyName,
                      error: error.message,
                      errorCode: error.code
                    }
                  );
                  
                  return console.error(
                    '[Call Center Voice][workerClientReservationAccepted$.subscribe] - There was an error updating the worksers activity to OnCall => ',
                    error
                  );
                }
                
                // Success logging
                this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateSuccess', {
                  reservationId: reservation?.task?.sid,
                  workerId: worker?.sid,
                  newActivity: worker?.activitySid,
                  newActivityName: worker?.activityName,
                  timestamp: new Date().toISOString()
                });
                
                this.cpLoggingService.registerEvent(
                  'worker.activity.update.success',
                  'success',
                  {
                    reservationId: reservation?.task?.sid,
                    workerId: worker?.sid,
                    newActivity: worker?.activityName
                  }
                );
                
                console.log(
                  '[Call Center Voice][workerClientReservationAccepted$.subscribe] - Workers activity was successfully updated to On-call'
                );
                this.worker = worker;
              }
            );
          }
          if (this.endCallButtonPress) {
            this.endCall();
          }

        } else {

          console.log(
            '[Call Center Voice][workerClientReservationAccepted$.subscribe] - Unsupported channel reservation accepted'
          );
          console.log(reservation);

        }

      }
    );

    this.twilioEventHandlerService.workerClientReservationRejected$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      (reservation: any) => {
        this.bugsnagService.leaveBreadcrumb('WorkerClientReservationRejected', {
          reservationId: reservation?.task?.sid,
          workerId: reservation?.workerSid,
          reservationStatus: reservation?.reservationStatus,
        });
        
        // Enhanced state transition logging
        this.logReservationStateTransition('Rejected', reservation, {
          reason: reservation?.task?.reason,
          rejectedBy: reservation?.task?.attributes?.rejectedBy || 'agent',
          callCenterConfig: {
            on_reject_inactive: this.callCenterConfiguration.on_reject_inactive
          }
        });
        
        // Race condition detection
        this.logReservationTiming(reservation, 'Rejected');
        
        switch (reservation.task.attributes.taskchannel) {
          case 'voice':
            this.store.dispatch(
              new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false)
            );
            this.currentReservation = reservation;
            // Ticket related: CCC-915
            // When the admin changes the agent status from the dashboard, the backend rejects the inbound call and deactivates
            // the voice channel in the worker's channel lists, so in the UI it is only necessary to update the workerChannels local variable
            // and hide the call popup. For this, it is necessary to receive in the reservation task attributes a flag that means that
            // the reservation rejection is made in the dashboard and not by hanging up action in the computer, physical phone or in the VoIP extension.
            if (reservation.task.attributes.rejectedBy && reservation.task.attributes.rejectedBy === 'dashboard') {
              console.log('[Call Center Voice][workerClientReservationRejected$.subscribe] - Incoming call rejected through the admin dashboard');
              this.workerChannels = this.workerChannels.map(workerChannel => {
                if (workerChannel.taskChannelUniqueName === 'voice') {
                  workerChannel.available = false;
                }
                return workerChannel;
              });

              break;
            }
            if (this.callCenterConfiguration.on_reject_inactive !== 1) {
              this.changeWorkerAvailability(true);
            }

            if (this.connectedVia === fromCallCenterVoice.ConnectionMethods.PHONE ||
              this.connectedVia === fromCallCenterVoice.ConnectionMethods.VOIP) {

              console.log('[Call Center Voice][workerClientReservationRejected$.subscribe] - Incoming call rejected by hanging up');
              const workerVoiceChannel = this.workerChannels.find(
                (channel: TwilioWorkerChannel) => {
                  return channel.taskChannelUniqueName === 'voice';
                }
              );

              let workerVoiceChannelSid: string = workerVoiceChannel?.sid || '';
              let workerSid: string = this.worker.sid;
              this.currentConnection = null;
              this.currentReservation = null;

              this.twilioWorkerChannelsProvider
              .update({
                workerSid,
                workerchannelSid: workerVoiceChannelSid,
                body: {
                  available: false,
                },
              })
              .subscribe(
                (updatedWorkerChannel: TwilioWorkerChannel) => {
                  this.workerChannels = this.workerChannels.map(workerChannel => {
                    if (workerChannel.sid === updatedWorkerChannel.sid) {
                      return updatedWorkerChannel;
                    }
                    return workerChannel;
                  });

                  let activity: any;

                  if (this.callCenterConfiguration.on_reject_inactive) {
                    // validate if the agent rejected the call or he/she doesn't answer the call
                    if (!this.agentNoAnswer) {
                      activity = this.getWorkerActivityByFriendlyName(
                        REJECTED_ACTIVITY_FRIENDLY_NAME
                      );
                      this.setAgentState({
                        agentState: fromCallCenterVoice.AgentStates.INACTIVE,
                        agentStateData: activity.sid,
                      }, 'workerClientReservationRejected$.subscribe');
                    }

                    this.setCallState({
                      callState: fromCallCenterVoice.CallStates.IDLE,
                    });
                  } else {
                    activity = this.workspaceActivities.find(
                      (activity: any) => {
                        return (
                          activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME
                        );
                      }
                    );

                    this.setAgentState({
                      agentState: fromCallCenterVoice.AgentStates.CONNECTING_READY,
                      agentStateData: activity,
                    }, 'workerClientReservationRejected$.subscribe');
                  }

                  // if there is no activity object because the agent wasn't send to rejected status, then
                  // it is not necessary to change its status due to this was already changed in the backend.
                  if (activity) {
                    // Enhanced logging before and after worker activity update
                    this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateAttempt', {
                      reservationId: reservation?.task?.sid,
                      workerId: this.worker?.sid,
                      fromActivity: this.worker?.activitySid,
                      toActivity: activity.sid,
                      toActivityName: activity.friendlyName,
                      reservationStatus: reservation?.reservationStatus,
                      currentAgentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
                      context: 'workerClientReservationRejected',
                      timestamp: new Date().toISOString()
                    });

                    this.workerClient.update(
                      'ActivitySid',
                      activity.sid,
                      (error, worker) => {
                        if (error) {
                          this.bugsnagService.notify(error, {
                            severity: 'error',
                            metadata: {
                              workerActivityUpdate: {
                                reservationId: reservation?.task?.sid,
                                workerId: this.worker?.sid,
                                targetActivitySid: activity.sid,
                                targetActivityName: activity.friendlyName,
                                errorCode: error.code,
                                errorMessage: error.message,
                                context: 'workerClientReservationRejected',
                                currentWorkerState: this.worker
                              }
                            }
                          });
                          
                          this.cpLoggingService.registerEvent(
                            'worker.activity.update.failed',
                            'error',
                            {
                              reservationId: reservation?.task?.sid,
                              workerId: this.worker?.sid,
                              targetActivity: activity.friendlyName,
                              error: error.message,
                              errorCode: error.code,
                              context: 'workerClientReservationRejected'
                            }
                          );
                          
                          return console.error(
                            '[Call Center Voice][workerClientReservationRejected$.subscribe] - There was an error updating the workers activity => ',
                            error
                          );
                        }
                        
                        // Success logging
                        this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateSuccess', {
                          reservationId: reservation?.task?.sid,
                          workerId: worker?.sid,
                          newActivity: worker?.activitySid,
                          newActivityName: worker?.activityName,
                          context: 'workerClientReservationRejected',
                          timestamp: new Date().toISOString()
                        });
                        
                        this.cpLoggingService.registerEvent(
                          'worker.activity.update.success',
                          'success',
                          {
                            reservationId: reservation?.task?.sid,
                            workerId: worker?.sid,
                            newActivity: worker?.activityName,
                            context: 'workerClientReservationRejected'
                          }
                        );
                        
                        console.log(
                          '[Call Center Voice][workerClientReservationRejected$.subscribe] - Workers activity was successfully updated'
                        );
                        this.worker = worker;
                        this.cd.markForCheck();
                      }
                    );
                  }
                },
                error => {
                  this.bugsnagService.notify(error);
                  console.error(error);
                }
              );
            }
            break;
          case 'custom1':
            console.log(
              '[Call Center Voice][workerClientReservationRejected$.subscribe] - Worker has rejected a reservation => ',
              reservation
            );
            break;
          case 'Callback':
            if (reservation.task.attributes.rejectedBy && reservation.task.attributes.rejectedBy === 'dashboard') {
              console.log('[Call Center Voice][workerClientReservationRejected$.subscribe] - Callback call rejected through the admin dashboard');

              this.workerChannels = this.workerChannels.map((workerChannel: TwilioWorkerChannel) => {
                if (workerChannel.taskChannelUniqueName === 'voice' || workerChannel.taskChannelUniqueName === 'callback') {
                  workerChannel.available = false;
                }
                return workerChannel;
              });

              this.showCallBackPopup = false;
              this.store.dispatch(
                new fromCallCenterVoiceAction.ToggleInboundCallbackPopUp(false)
              );
              this.cd.markForCheck();
              if (this.audio) {
                this.audio.pause();
                this.audio = null;
              }

              this.store.dispatch(
                new fromCallCenterVoiceAction.ToggleInboundCallPopUp(false)
              );
            }
            if (this.callCenterConfiguration.on_reject_inactive !== 1) {
              this.changeWorkerAvailability(true);
            }
            break;
          default:
            console.log(
              '[Call Center Voice][workerClientReservationRejected$.subscribe] - Unsupported channel reservation rejected'
            );
            console.log(reservation);
            this.store.dispatch(fromNotificationAlertsAction.doRemoveAlert({ taskSid: reservation.taskSid }));
        }
      }
    );

    // this observable will update the local reservation variable from 'reservation.completed'
    // event set in the workerClient handler in call-center.module
    this.twilioEventHandlerService.workerClientTaskReservationCompleted$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      (reservation: any) => {
        this.bugsnagService.leaveBreadcrumb('WorkerClientTaskReservationCompleted', {
          reservationId: reservation?.task?.sid,
          workerId: reservation?.workerSid,
          reservationStatus: reservation?.reservationStatus,
        });
        if (isCallCommunicationChannel(reservation.task.attributes.taskchannel)) {
          let isCallCompleted = false;
          this.currentReservation = reservation;

          if (this.currentReservation?.task.assignmentStatus === 'completed') {
            const taskAttributes = this.currentReservation.task.attributes || null;

            // Handle completed Twilio video call.
            if (taskAttributes.taskchannel === 'video' && this.wasAgentOnACall) {
              this.endCall();
              return;
            }

            if (taskAttributes && taskAttributes.callCancelReason && taskAttributes.callCancelReason === 'invalidNumber') {
              this.topToastMessages = [
                {
                  message: 'The number is not valid',
                  type: TopToastComponent.ERROR,
                },
              ];
              this.showTopToast = true;
              this.cd.markForCheck();
              console.log('[Call Center Voice][workerClientTaskReservationCompleted$.subscribe] - The number is not valid');
            }

            if(reservation?.task?.reason && reservation.task.reason === HANG_UP_CALL_REASON) {
              this.onCancellingCall();
            }

            this.changeWorkerAvailability(true);
            // Since the lambda doesn't handle the cooldown status when the connection is through PC, it is necessary to handle it here
            // and once the call is completed, move it to cooldown.
            // When it is an inbound call, there is a 'reason' property with 'call completed' value, but when it is an outbound call this
            // property doesn't exist so we have to 'guess' that the call ended, in both cases the this.wasAgentOnACall variable will help us
            // to know that the agent was in a call. this piece of code seems redundant but it helps to identify both cases. (CCC-1409)
            isCallCompleted = true;
            if (taskAttributes.direction === 'outbound' && this.currentReservation?.reservationStatus === 'accepted') {
              this.endCall();
            }

            if (this.currentReservation?.task.attributes.direction === 'outbound') {
              isCallCompleted = this.currentReservation.task.reason === null ? true : false;
            }

          }

          if (isCallCompleted && this.wasAgentOnACall) {
            this.endCall();
          }
          this.sharedService.checkIfObCallLogNeedsUpdate$.next(null);

        }
      }
    );
  }

  /* ********** Worker Client ********** */
  private subscribeWorkerClient(): void {
    this.workerClient$
      .pipe(filter(
        (workerClientState: fromWorkerClient.WorkerClientState) =>
          (workerClientState.loaded || workerClientState.tokenUpdated) && !workerClientState.workerUpdatedFromTwilio
      )
      ,map(
        (workspaceClientState: fromWorkerClient.WorkerClientState) =>
          workspaceClientState.worker
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe((workerClient: any) => {
        this.worker = workerClient;
      });
  }

  private setLocationClientStatus(): void {
    // We need workspaceActivities and workerChannels ready
    // when this is executed the worker already exist in the class property
    // set Agent Status to Ready
    const workspaceActivitiesSource = this.workspaceActivities$
      .pipe(filter(
        (
          workspaceActivitiesState: fromWorkspaceActivities.WorkspaceActivitiesState
        ) => workspaceActivitiesState.loaded
      )
      ,map(
        (
          workspaceActivitiesState: fromWorkspaceActivities.WorkspaceActivitiesState
        ) => workspaceActivitiesState.activities
      )
      ,take(1));

    const workerChannelsSource = this.workerChannels$
      .pipe(filter(
        (workerChannelsState: fromWorkerChannels.WorkerChannelsState) =>
          workerChannelsState.loaded
      )
      ,map(
        (workerChannelsState: fromWorkerChannels.WorkerChannelsState) =>
          workerChannelsState.channels
      )
      ,take(1));

    forkJoin([
      workspaceActivitiesSource,
      workerChannelsSource
    ]).subscribe(([workspaceActivities, workerChannels]) => {
      let readyActivity: any = workspaceActivities.find((activity: any) => {
        return activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME;
      });
      this.setAgentState({
        agentState: fromCallCenterVoice.AgentStates.CONNECTING_READY,
        agentStateData: readyActivity,
      }, 'setLocationClientStatus()');
    });
  }

  private twilioHandlers(): void {
    this.twilioEventHandlerService.twilioDeviceSetup$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((twilioDevice : any) => {
      if (twilioDevice) {
        this.twilioDevice = twilioDevice.twilioDevice;
        this.audioContext = twilioDevice.audioContext;
      }
    });
    this.twilioEventHandlerService.twilioDeviceReady$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(device => {
      /** When we lose the internet connection the status bar is offline
       * but we can't update the status on twilio, so when we are online again
       * we check if the status is offline so, update it on twilio.
       */
      this.removeHangupWorkerAttribute();
      this.store.dispatch(new fromCallCenterVoiceAction.GetConnection());
      this.showErrorConnectionToast = false;
      this.couldNotReconnect = false;
      console.log('[Call Center Voice][twilioDeviceReady$.subscribe] - Twilio Device is ready => ', device);
      if (this.loseTwilioConnection) {
        this.store.dispatch(
          new fromCallCenterVoiceAction.ConnectionReEstablished()
        );
      }
      if (!this.loseTwilioConnection && this.initDone) {
        this.reconnectInTwilio();
      }
      if (this.initDone) {
        return;
      }

      this.initDone = true;
      this.contactUri = `client:${this.voiceIdentity}`;
      if (this.worker.attributes && this.worker.attributes.contact_uri) {
        this.contactUri = this.worker.attributes.contact_uri;
      } else {
        this.contactUri = `client:agent_${this.session.user.user_id}`;
      }

      let updatedAttributes = {
        ...this.worker.attributes,
        contact_uri: this.contactUri,
      };
      let props: any = {
        Attributes: updatedAttributes,
      };
      UtilsHelper.workerUpdateSequential(
        this.worker,
        props,
        (error, worker) => {
          if (error) {
            return console.error(
              'There was an error updating the worker: ',
              error
            );
          }
          console.log(
            '[Call Center Voice][twilioDeviceReady$.subscribe] - Worker has been updated successfully',
            this.worker
          );
          this.worker = worker;

          let workerActivitySid: string = worker.activitySid;
          let workerActivity: any = this.workspaceActivities.find(
            (workspaceActivity: any) => {
              return workspaceActivity.sid === workerActivitySid;
            }
          );
          // set worker to Offline status only if she/he was in offline status previously
          if (workerActivity.friendlyName === OFFLINE_ACTIVITY_FRIENDLY_NAME) {
            return this.setAgentState({
              agentState: fromCallCenterVoice.AgentStates.OFFLINE,
            }, 'twilioDeviceReady$.subscribe');
          }
          // otherwise set it to ready status
          return this.setAgentState({
            agentState: fromCallCenterVoice.AgentStates.READY,
          }, 'twilioDeviceReady$.subscribe');
        }
      );
    });
    // Twilio Device incoming handler
    this.twilioEventHandlerService.twilioDeviceIncoming$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      (connection: any) => {
        this.currentConnection = connection;
        if (connection.parameters['To'] === this.contactUri) {
          if (
            this.currentReservation &&
            this.currentReservation.task.attributes.taskchannel === 'custom1'
          ) {
            connection.accept();
            this.unmute();
            this.setCallState({
              callState: fromCallCenterVoice.CallStates.ON_CALL,
            });
            return;
          }
          if (
            this.currentReservation &&
            this.currentReservation.task.attributes.taskchannel === 'voice'
          ) {
            this.unmute();
            this.currentConnection = connection;
            console.log('[Call Center Voice][twilioDeviceIncoming$.subscribe] - Voice connection has been updated successfully]', connection);
            this.currentConnection.accept();
            setTimeout(() => {
              this.callProvider.getListOfCallParticipants({'friendlyName': this.currentReservation.taskSid})
                .pipe(takeUntil(this.ngUnsubscribe))
                .subscribe(
                  (result: any) => {
                    console.log('[Call Center Voice][twilioDeviceIncoming$.subscribe] - Participants => ', result)
                    if (result.body && result.body.length < 2) {
                      this.currentReservation.task.complete(
                        (error, reservation) => {
                          // CAL-7171 -> "Connection" ringtone keeps ringing after agent state changed to "Cooldown"
                          // Trying to disconnect currentConnection before clearing the reference since is
                          // not accessible through the twilioDevice object nor the disconnectAll() device's method
                          if (this.currentConnection && typeof this.currentConnection.disconnect === 'function') {
                            try {
                              console.log('[Call Center Voice][twilioDeviceIncoming$.subscribe] - Disconnecting voice connection => ', this.currentConnection);
                              this.currentConnection.disconnect();
                            } catch (error) {
                              console.error('[Call Center Voice][twilioDeviceIncoming$.subscribe] -> Error disconnecting current connection', error);
                            }
                          }
                          this.currentConnection = null;
                          this.currentReservation = null;
                          let workspaceActivity = this.workspaceActivities.find(
                            (workspaceActivity: any) => {
                              return (
                                workspaceActivity.friendlyName === COOLDOWN_ACTIVITY_FRIENDLY_NAME
                              );
                            });
                          this.updateWorkerStatus(workspaceActivity);
                          this.setCallState({
                            callState: fromCallCenterVoice.CallStates.END_CALL,
                          });
                          this.setAgentState({
                            agentState: fromCallCenterVoice.AgentStates.COOLDOWN,
                          }, 'hangUpCall()');
                          this.callBeingAccepted = false;
                          this.callerHasHungUp = true;
                          this.sharedService.callHungUp$.next(null);
                          this.onCancellingCall();
                          this.sharedService.agentStatePriorCall$.next({ status: '', activity: '' });
                          this.cd.markForCheck();
                        }
                      );
                    }
                  }
                );
              }, 2000);
            console.log('[Call Center Voice][twilioDeviceIncoming$.subscribe] - Unhandled connection for reservation');
            console.log('[Call Center Voice][twilioDeviceIncoming$.subscribe] - connection', connection);
            console.log('[Call Center Voice][twilioDeviceIncoming$.subscribe] - currentReservation', this.currentReservation);
            return;
          }
        }
      }
    );

    // Listen when twilio disconnects the call, and based on the agent's status and the task status
    // check if the task attributes has the callCancelReason flag, that means the call was made to
    // invalid number
    this.twilioEventHandlerService.twilioDeviceDisconnect$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      async (connection) => {
        try {
          if (this.currentAgentStatus === fromCallCenterVoice.AgentStates.ON_OUTBOUND_CALL &&
            this.currentReservation.task.assignmentStatus === 'completed') {
            await this.onCancellingOutboundCall();
          } else if (this.currentAgentStatus === fromCallCenterVoice.AgentStates.ON_OUTBOUND_CALL &&
            this.currentReservation.task.assignmentStatus === 'assigned') {
            await this.endCall();
          }
        } catch (error) {
          console.error('[Call Center Voice] Error in twilioDeviceDisconnect handler:', error);
          this.bugsnagService.leaveBreadcrumb('twilioDeviceDisconnectError', {
            error: error.message || 'Unknown error',
            callSid: this.currentReservation?.task?.attributes?.call_sid
          });
        }
      }
    );
  }
  /* ********** Device Client ********** */
  private registerDeviceClient(): void {
    this.deviceClient$
      .pipe(filter(
        (deviceClientState: fromDeviceClient.DeviceClientState) =>
          deviceClientState.loaded
      )
      ,map(
        (deviceClientState: fromDeviceClient.DeviceClientState) =>
          deviceClientState
      )
      ,take(1))
      .subscribe(async deviceClientState => {
        try {
          console.log('[Call Center Voice][registerDeviceClient] - Device client loaded, setting up event listeners');

          // Register for important events to handle user requests
          this.subscribeToUserInitiatedActions();
        } catch (error) {
          console.error('[Call Center Voice] Error setting up device client:', error);
          this.bugsnagService.notify(error);
        }
      });
  }

  // Subscribe to user-initiated actions that should trigger Twilio initialization
  private subscribeToUserInitiatedActions(): void {
    // Listen for user actions that should trigger initialization if needed
    this.actions$.pipe(
      // Use action type strings instead of action creator classes to avoid type issues
      ofType(
        '[Call Center Voice] Toggle Dial Pad',
        '[Call Center Voice] Toggle Call Mute',
        '[Call Center Voice] Toggle Call Hold'
      ),
      takeUntil(this.ngUnsubscribe)
    ).subscribe(async () => {
      // Ensure AudioContext is running in response to user action
      await this.twilioDeviceService.handleUserInteraction();
    });
  }

  private async disconnectCalls(): Promise<void> {
    try {
      // Resume AudioContext to ensure clean disconnection
      this.twilioDeviceService.handleUserInteraction();

      // First, handle the current connection if it exists
      if (this.currentConnection && typeof this.currentConnection.disconnect === 'function') {
        try {
          this.currentConnection.disconnect();
        } catch (error) {
          console.error('[Call Center Voice][disconnectCalls] - Error disconnecting current connection', error);
          this.bugsnagService.notify(error);
        }
      }

      // Then use the TwilioDeviceService to ensure all connections are closed
      try {
        await this.twilioDeviceService.disconnectAll();
      } catch (error) {
        console.error('[Call Center Voice][disconnectCalls] - Error disconnecting all calls via TwilioDeviceService', error);
        this.bugsnagService.notify(error);
      }

      // Ensure we clear the connection reference
      this.currentConnection = null;
    } catch (error) {
      console.error('[Call Center Voice][disconnectCalls] - Error in disconnectCalls method', error);
      this.bugsnagService.notify(error);
    }
  }

  private toggleMuteForCalls(isCallMuted: boolean) {
    console.log(`[Call Center Voice][toggleMuteForCalls] - Setting mute to: ${isCallMuted}`);
    this.callIsMuted = isCallMuted;

    // Try multiple approaches to get the active connection

    // First try using currentConnection (works for outbound calls)
    if (this.currentConnection && typeof this.currentConnection.mute === 'function') {
      console.log('[Call Center Voice][toggleMuteForCalls] - Muting currentConnection');
      this.currentConnection.mute(isCallMuted);
      return;
    }

    // Then try using device.activeConnection() (works for inbound calls)
    const device = this.getTwilioDevice();
    if (device) {
      try {
        const connection = device._activeCall;
        if (connection && typeof connection.mute === 'function') {
          console.log('[Call Center Voice][toggleMuteForCalls] - Muting device.activeConnection()');
          connection.mute(isCallMuted);
          return;
        }
      } catch (error) {
        console.error('[Call Center Voice][toggleMuteForCalls] - Error accessing activeConnection', error);
        this.bugsnagService.notify(error);
      }
    }

    console.warn('[Call Center Voice][toggleMuteForCalls] - Could not find any active connection to mute');
  }

  private reconnectInTwilio() {
    if (!this.loseTwilioConnection && !this.reconnectWithReservation) {
      let activity: any;
      let state: any;
      switch (this.statusWhenOffline) {
        case fromCallCenterVoice.AgentStates.READY:
          activity = this.workspaceActivities.find((workspaceActivity: any) => {
            return (
              workspaceActivity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME
            );
          });
          state = fromCallCenterVoice.AgentStates.READY;
          this.updateWorker(activity, state);
          break;
        case fromCallCenterVoice.AgentStates.OFFLINE:
          activity = this.workspaceActivities.find((workspaceActivity: any) => {
            return (
              workspaceActivity.friendlyName === OFFLINE_ACTIVITY_FRIENDLY_NAME
            );
          });
          state = fromCallCenterVoice.AgentStates.OFFLINE;
          this.updateWorker(activity, state);
          break;
      }
    }
  }

  private updateWorker(activity, state) {
    UtilsHelper.workerUpdateSequential(
      this.worker,
      { ActivitySid: activity.sid },
      (error, worker) => {
        if (error) {
          return console.error(
            'There was an error updating the worker: ',
            error
          );
        }
        console.log(
          '[Call Center Voice][updateWorker] - Worker Activity has been updated to: ',
          activity.friendlyName
        );
        return this.setAgentState({
          agentState: state,
          agentStateData: activity.sid,
        }, 'updateWorker()');
      }
    );
  }

  private createCall(): void {
    console.log('[Call Center Voice][createCall] - create Call');
    console.log('[Followup OB call] -> Creating the call');

    this.cancelOutboundCall = false;
    this.endCallButtonPress = false;

    // Reset any previous transfer state when creating a new outbound call
    this.twilioDeviceService.resetOnNewCall();

    const systemConfigurationSource: Observable<SysConfig | null> = this.systemConfiguration$
      .pipe(filter(
        (
          systemConfigurationState: fromSystemConfiguration.SystemConfigurationState
        ) => systemConfigurationState.loaded
      )
      ,map(
        (
          systemConfigurationState: fromSystemConfiguration.SystemConfigurationState
        ) => systemConfigurationState.data
      )
      ,take(1));

    const callCenterVoiceSource: Observable<{
      fromLocation: Location;
      toNumber: string;
      leadId?: any;
      isDialPad?: any;
      workflowStep?: any;
      outboundCardLinkId?: string;
    }> = this.callCenterVoice$
      .pipe(map((callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) => {
        return callCenterVoiceState.agentStateData;
      })
      ,take(1));

    //Tracking numbers non excluded are needed.
    const trackingNumbersSource: Observable<Array<
      TrackingnumberListItem
    >> = this.trackingNumberProvider
      .getAll({
        filterActive: 'true',
        page: false,
        appVersion: 2,
        filterExcludeNumber: 0,
      })
      .pipe(take(1)
      ,map((res: any) => res.body.items));

    const sessionSource: Observable<SessionData | null> = this.session$
      .pipe(filter((sessionState: fromSession.SessionState) => sessionState.loaded)
      ,map((sessionState: fromSession.SessionState) => sessionState.session)
      ,take(1));

    // First, get the fromLocationId from callCenterVoiceSource
    callCenterVoiceSource.subscribe(agentStateData => {
      // Add null check for fromLocation which can be null during inbound transfers
      if (!agentStateData || !agentStateData.fromLocation) {
        console.warn('[Call Center Voice][createCall] - Missing fromLocation in agentStateData during call creation',
          { hasAgentStateData: !!agentStateData, callType: this.currentCallState });

        // Log diagnostics for debugging
        this.bugsnagService.leaveBreadcrumb('MissingFromLocation', {
          hasAgentStateData: !!agentStateData,
          callState: this.currentCallState,
          isActiveTransfer: this.twilioDeviceService.isActiveTransfer,
          currentReservation: this.currentReservation?.sid
        });

        // For transfers, we can still continue the call flow without fromLocation
        if (this.twilioDeviceService.isActiveTransfer) {
          console.log('[Call Center Voice][createCall] - Continuing in transfer context without fromLocation');
          this.locationByPass = false; // Default for transfers
          return;
        }

        return;
      }

      const fromLocationId = agentStateData.fromLocation.location_id + '';

      // Now fetch location configuration and set locationByPass
      this.locationConfig$
        .pipe(
          filter((locationConfigState: fromLocationConfig.LocationConfigurationState) => locationConfigState.loaded),
          map((locationConfigState: fromLocationConfig.LocationConfigurationState) => locationConfigState.entities),
          take(1)
        )
        .subscribe(locationConfig => {
          if (locationConfig && locationConfig[fromLocationId]) {
            this.locationByPass = locationConfig[fromLocationId].bypass_press_1;
            console.log(`[Call Center Voice][createCall] - Retrieved bypass_press_1=${this.locationByPass} for locationId=${fromLocationId}`);
          } else {
            console.warn(`[Call Center Voice][createCall] - Location ID ${fromLocationId} not found in configuration, using default`);
            this.locationByPass = false; // Default value

            // Log diagnostics to help debug
            this.bugsnagService.leaveBreadcrumb('LocationConfigMissing', {
              fromLocationId,
              availableLocationIds: Object.keys(locationConfig || {}),
              currentReservation: this.currentReservation?.sid
            });
          }
        });
    });

    forkJoin([
      callCenterVoiceSource,
      trackingNumbersSource,
      systemConfigurationSource,
      sessionSource
    ])
      .pipe(take(1))
      .subscribe(
        ([
          agentStateData,
          trackingNumbers,
          systemConfiguration,
          session
        ]) => {
          console.log('[Call Center Voice][createCall] - creating call');
          let url: string = `${TWILIO_URLS.baseURL}outbound`;
          let agentId: string = '';
          const agentFirstName: string = encodeURI(`${this.session.user.firstname}`);
          const agentLastName: string = encodeURI(`${this.session.user.lastname}`);
          const statusUrl: string = `https://${APP_CONFIG.CALL_API_GW}/${APP_CONFIG.APP_STACK}-conference-event/handler`;
          const reportId: any = this.collectionReportId || '';
          const callType: string  = this.collectionCallType || '';
          const callBackTaskSid: string = this.callBackTaskSid || '';

          let token: string = this.worker.token;
          let workerSID: string = this.worker.sid;

          // Add null checks for fromLocation which can be null during inbound transfers
          let fromLocation: Location | null = agentStateData?.fromLocation || null;
          let fromLocationOverrideTrackingNumber: string = '';
          let fromLocationId: string = '';

          // If we have fromLocation, extract properties
          if (fromLocation) {
            fromLocationOverrideTrackingNumber = fromLocation.overide_tracking_number || '';
            // Location spec indicates that location_id is a number
            fromLocationId = fromLocation.location_id + '';
          } else if (this.twilioDeviceService.isActiveTransfer) {
            // For transfers without fromLocation, try to get location from reservation
            const locationInfo = this.getAccountIdAndLocationIdFromReservation();
            if (locationInfo && locationInfo.locationId) {
              fromLocationId = locationInfo.locationId;
              console.log(`[Call Center Voice][createCall] - Using locationId=${fromLocationId} from reservation during transfer`);
            } else {
              console.warn('[Call Center Voice][createCall] - No location ID available during transfer');
            }
          }

          let from: string = '';
          let to: string = '';
          let authToken: any = session ? session.session_id : null;
          let connectedTo: string = '';
          let dialerNumber: string = '';
          let locBypass: any;
          let leadId: number | undefined;
          let leadLocationId: number | undefined;
          let ledgerLocationId: number | undefined;
          let customerId: number | undefined;
          let leadESId: string | undefined = '';
          let customerESId: string | undefined = '';
          let isDialPad: boolean = agentStateData.isDialPad;
          let outboundCardLinkId: string | undefined = agentStateData.outboundCardLinkId;
          let workflowStep: boolean = agentStateData.workflowStep;

          // this.locationByPass is now set outside this subscription

          let onCallActivity: any = this.workspaceActivities.find(
            (activity: any) => {
              return activity.friendlyName === ONCALL_ACTIVITY_FRIENDLY_NAME;
            }
          );
          let readyActivity: any = this.workspaceActivities.find(
            (activity: any) => {
              return activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME;
            }
          );
          let workspaceActivity: any = this.workspaceActivities.find(
            (workspaceActivity: any) => {
              return workspaceActivity;
            }
          );
          this.reservationUrl = `?fromLocationId=${fromLocationId}&WorkerSid=${workerSID}&bypass_press_1=${this.locationByPass}&workflowStep=${workflowStep}`;
          if (this.leadId) {
            this.reservationUrl += `&leadId=${this.leadId}`;
          }
          if (this.customerId) {
            this.reservationUrl += `&customerId=${this.customerId}`;
          }
          leadESId = this.leadESId;
          leadId = this.leadId;
          customerId = this.customerId;
          customerESId = this.customerESId;
          if (
            this.connectedVia === fromCallCenterVoice.ConnectionMethods.PHONE ||
            this.connectedVia === fromCallCenterVoice.ConnectionMethods.VOIP
          ) {
            connectedTo = 'phone';
            dialerNumber = this.dialerNumber;
            locBypass = this.locationByPass;
            agentId = `${this.session.user.user_id}`;
            leadLocationId = this.leadLocationId;
            ledgerLocationId = this.ledgerLocationId;
            if (
              this.connectedVia === fromCallCenterVoice.ConnectionMethods.VOIP
            ) {
              dialerNumber = encodeURIComponent(dialerNumber);
            }

            this.reservationUrl += `&connectType=phone&dialerNumber=${dialerNumber}`;
          }

          if (this.isLocationLogin) {
            connectedTo = 'location';
            authToken = SessionStorageHelper.getItem('machineSessionToken');
            this.callTo =
              agentStateData.fromLocation.outbound_phone ||
              agentStateData.fromLocation.phone;
            if (!this.callTo) {
              this.showTopToast = true;
              this.topToastMessages = [
                {
                  message:
                    'Location number is not set for outbound call',
                  type: TopToastComponent.BIG_ERROR,
                },
              ];
              return;
            }
            if (this.callTo.includes(',')) {
              this.callTo = this.callTo.substring(0, this.callTo.indexOf(','));
            }
            this.reservationUrl += '&connectType=location';
          }

          let fromLocationNumber = null;

          // Only filter tracking numbers if we have a valid fromLocationId
          if (fromLocationId) {
            fromLocationNumber = trackingNumbers.filter(
              (trackingNumber: TrackingnumberListItem) => {
                if (this.collectionCallType === 'outbound_collection') {
                  return (
                    trackingNumber.trackingnumber.location_id.toString() ===
                      fromLocationId.toString() &&
                    trackingNumber.trackingnumber.type === 'collection'
                  );
                }

                return (
                  trackingNumber.trackingnumber.location_id.toString() ===
                    fromLocationId.toString() &&
                  trackingNumber.trackingnumber.type === 'local'
                );
              }
            )[0];
          } else if (this.twilioDeviceService.isActiveTransfer) {
            console.log('[Call Center Voice][createCall] - Skipping tracking number filtering during transfer with no locationId');
          } else {
            console.warn('[Call Center Voice][createCall] - Cannot filter tracking numbers: No locationId available');
          }

          if (fromLocationOverrideTrackingNumber && !(this.collectionCallType || '').includes('collection')) {
            from = fromLocationOverrideTrackingNumber;
            this.invokeLambda(
              agentStateData,
              to,
              from,
              token,
              authToken,
              connectedTo,
              dialerNumber,
              locBypass,
              workerSID,
              agentId,
              leadId,
              customerId,
              leadLocationId,
              ledgerLocationId,
              fromLocationId,
              leadESId,
              customerESId,
              workflowStep,
              outboundCardLinkId,
              agentFirstName,
              agentLastName,
              statusUrl,
              reportId,
              callType,
              callBackTaskSid
            );
          } else if (
            this.twilioDeviceService.isActiveTransfer &&
            (!fromLocationNumber || !trackingNumbers || trackingNumbers.length === 0)
          ) {
            // For transfers, we can proceed without a tracking number if necessary
            console.log('[Call Center Voice][createCall] - Proceeding with transfer without tracking number');

            // Use a default "from" value for transfers when no tracking number is available
            from = this.dialerNumber || '';

            this.invokeLambda(
              agentStateData,
              to,
              from,
              token,
              authToken,
              connectedTo,
              dialerNumber,
              locBypass,
              workerSID,
              agentId,
              leadId,
              customerId,
              leadLocationId,
              ledgerLocationId,
              fromLocationId,
              leadESId,
              customerESId,
              workflowStep,
              outboundCardLinkId,
              agentFirstName,
              agentLastName,
              statusUrl,
              reportId,
              callType,
              callBackTaskSid
            );
          } else if (this.collectionCallType === 'outbound_collection') {
            if (fromLocationNumber) {
              from = fromLocationNumber.trackingnumber ? fromLocationNumber.trackingnumber.call_number : null;
            }
            this.invokeLambda(
              agentStateData,
              to,
              from,
              token,
              authToken,
              connectedTo,
              dialerNumber,
              locBypass,
              workerSID,
              agentId,
              leadId,
              customerId,
              leadLocationId,
              ledgerLocationId,
              fromLocationId,
              leadESId,
              customerESId,
              workflowStep,
              outboundCardLinkId,
              agentFirstName,
              agentLastName,
              statusUrl,
              reportId,
              callType,
              callBackTaskSid
            );
          } else if (isDialPad || this.isCallBack) {
            if (fromLocationNumber) {
              from = fromLocationNumber.trackingnumber ? fromLocationNumber.trackingnumber.call_number : null;
            }
            this.invokeLambda(
              agentStateData,
              to,
              from,
              token,
              authToken,
              connectedTo,
              dialerNumber,
              locBypass,
              workerSID,
              agentId,
              leadId,
              customerId,
              leadLocationId,
              ledgerLocationId,
              fromLocationId,
              leadESId,
              customerESId,
              workflowStep,
              outboundCardLinkId,
              agentFirstName,
              agentLastName,
              statusUrl,
              reportId,
              callType,
              callBackTaskSid
            );
          } else if (this.isCallSidUsedAsTask || this.isNewIC || this.newFromCall) {
            /* When the IC is opened from the history list or from the new button we don't
             * have a regular taskSid we have either a call sid or a uuid so on this cases
             * we need tu use a local tracking number
             */
            if (fromLocationNumber) {
              from = fromLocationNumber.trackingnumber ? fromLocationNumber.trackingnumber.call_number : null;
            }
            this.invokeLambda(
              agentStateData,
              to,
              from,
              token,
              authToken,
              connectedTo,
              dialerNumber,
              locBypass,
              workerSID,
              agentId,
              leadId,
              customerId,
              leadLocationId,
              ledgerLocationId,
              fromLocationId,
              leadESId,
              customerESId,
              workflowStep,
              outboundCardLinkId,
              agentFirstName,
              agentLastName,
              statusUrl,
              reportId,
              callType,
              callBackTaskSid
            );
          } else {
            let body = {
              leadId: this.leadId,
              variable: '{system.TrackNumber}',
            };

            this.systemVariablesProvider
              .getSystemVariableValue(body)
              .subscribe(res => {
                let trackingNumberEndpointResponse = res.body.value;
                this.trackingNumberFromEndpoint = trackingNumbers.filter(
                  number => {
                    return (
                      number.trackingnumber.call_number ===
                      trackingNumberEndpointResponse
                    );
                  }
                );

                if (this.trackingNumberFromEndpoint.length > 0) {
                  from = this.trackingNumberFromEndpoint[0].trackingnumber
                    .call_number;
                } else {
                  if (fromLocationNumber) {
                    from = fromLocationNumber.trackingnumber ? fromLocationNumber.trackingnumber.call_number : null;
                  }
                }

                this.invokeLambda(
                  agentStateData,
                  to,
                  from,
                  token,
                  authToken,
                  connectedTo,
                  dialerNumber,
                  locBypass,
                  workerSID,
                  agentId,
                  leadId,
                  customerId,
                  leadLocationId,
                  ledgerLocationId,
                  fromLocationId,
                  leadESId,
                  customerESId,
                  workflowStep,
                  outboundCardLinkId,
                  agentFirstName,
                  agentLastName,
                  statusUrl,
                  reportId,
                  callType,
                  callBackTaskSid
                );
              });
          }
        }
      );
  }

  private invokeLambda(
    agentStateData,
    to,
    from,
    token,
    authToken,
    connectedTo,
    dialerNumber,
    locBypass,
    workerSID,
    agentId,
    leadId,
    customerId,
    leadLocationId,
    ledgerLocationId,
    fromLocationId,
    leadESId,
    customerESId,
    workflowStep,
    outboundCardLinkId,
    agentFirstName,
    agentLastName,
    statusUrl,
    reportId,
    callType,
    callBackTaskSid) {
    to = agentStateData.toNumber;
    if (from.includes('@') && !from.includes('sip:')) {
      from = 'sip:' + from;
    }
    if (to.includes('@') && !to.includes('sip:')) {
      to = 'sip:' + to;
    }
    to = encodeURIComponent(to);
    let fromPhone = encodeURIComponent(from);
    this.reservationUrl += `&FromPhone=${fromPhone}`;
    console.log('[Call Center Voice][invokeLambda] - invoking a lambada function');
    console.log('[Followup OB call] -> invoking lambada ');

    this.awsLambdaProvider
      .invokeOutboundCall({
        to,
        from,
        token,
        authToken,
        connectedTo,
        dialerNumber,
        locBypass,
        workerSID,
        agentId,
        leadId,
        customerId,
        leadLocationId,
        ledgerLocationId,
        fromLocationId,
        leadESId,
        customerESId,
        workflowStep,
        outboundCardLinkId,
        agentFirstName,
        agentLastName,
        statusUrl,
        reportId,
        callType,
        callBackTaskSid
      })
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((response: { sid: string }) => {
        console.log('[Call Center Voice][invokeLambda] - call started');
        console.log(response.sid, this.cancelOutboundCall);
        if (this.cancelOutboundCall && response.sid) {
          this.bugsnagService.leaveBreadcrumb('CancelOutboundCall', {
            taskSid: response?.sid,
            worker: this.worker?.sid,
            task: response,
          });
          return this.workerClient.updateTask(
            response.sid,
            {
              AssignmentStatus: 'canceled',
            },
            (error, updatedTask) => {
              if (error) {
                this.bugsnagService.notify(error);
                return console.error(
                  '[Call Center Voice]: There was an error cancelling the just created task for the outbound call => ',
                  error
                );
              }
              let workspaceActivity = this.workspaceActivities.find(
                (workspaceActivity: any) => {
                  return (
                    workspaceActivity.friendlyName === COOLDOWN_ACTIVITY_FRIENDLY_NAME
                  );
                });
              this.updateWorkerStatus(workspaceActivity);
              this.setCallState({
                callState: fromCallCenterVoice.CallStates.IDLE,
              });
              return console.log(
                '[Call Center Voice][invokeLambda] - The task create for the outbound call, has been canceled'
              );
            }
          );
        }
        this.leadESId = '';
        this.leadId = undefined;
        this.customerESId = '';
        this.customerId = undefined;
      });
  }

  /**
   * Gathers the required context information needed to Executes a call transfer.
   * @param {Object} config - The configuration object containing payloadBuilder and responseHandler.
   */
  public withinCallTransferContext(config: {
    payloadBuilder: (context: fromCallCenterVoice.CallTransferContext) => fromCallCenterVoice.CallTransferPayload;
    responseHandler?: (response: { sid: string }) => void;
  }) {
    this.bugsnagService.leaveBreadcrumb('WithinCallTransferContext', {
      callSid: this.currentReservation?.task?.sid,
      callState: this.currentAgentStatus,
    });
    const callCenterVoiceSource: Observable<{
      fromLocation: Location;
      toNumber: string;
    }> = this.callCenterVoice$
      .pipe(map((callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) => {
        return callCenterVoiceState.agentStateData;
      })
      ,take(1));

    const sessionSource: Observable<SessionData | null> = this.session$
      .pipe(filter((sessionState: fromSession.SessionState) => sessionState.loaded)
      ,map((sessionState: fromSession.SessionState) => sessionState.session)
      ,take(1));

    forkJoin([
      callCenterVoiceSource,
      sessionSource
    ])
      .pipe(take(1))
      .subscribe(
        ([agentStateData, session]) => {

          /**
           * The call transfer context is the data that will be passed to the handle function
           */
          const transferContext = { agentStateData, session } as fromCallCenterVoice.CallTransferContext;

          /**
           * The handle function will return the payload that will be passed to the lambda function
           */
          const transferencePayload = config.payloadBuilder(transferContext);

          /**
           * The lambda function will return the call sid of the new call
           */
          const lambdaSub$ = this.awsLambdaProvider
            .invokeTransferCall(transferencePayload)
            // .pipe(takeUntil(this.ngUnsubscribe))
            .subscribe((response: { sid: string }) => {

              /**
               * The response handler will handle the response of the lambda function
               */
              if (config.responseHandler) {
                config.responseHandler(response);
              }

              /**
               * Unsubscribe from the lambda function subscription
               */
              lambdaSub$.unsubscribe();

            });
        }, (error) => {
          if (error) {
            this.bugsnagService.notify(error);
          }
        }
      );
  }

  /**
   * Creates a transfer call.
   */
  createTransferCall() {
    this.bugsnagService.leaveBreadcrumb('createTransferCall', {
      callSid: this.currentReservation?.task?.sid,
      callState: this.currentAgentStatus,
    });
    this.withinCallTransferContext({
      /**
       * Builds the payload for starting call transfer operation with the backend.
       * @param {Object} context - The context object containing agentStateData and session.
       * @returns {Object} The call transfer payload.
       */
      payloadBuilder: (context) => {
        const { agentStateData, session } = context;
        let to = agentStateData ? agentStateData.toNumber : this.callToWhenChange;
        if (to.includes('@') && !to.includes('sip:')) {
          to = `sip:${to}`;
        }

        const payload = {
          to,
          authToken: session ? session.session_id : '',
          StatusCallbackEvent: fromCallCenterVoice.CallTransferStates.START,
          FriendlyName: this.currentReservation.task.sid,
        } as fromCallCenterVoice.CallTransferPayload;

        datadogLogs.logger.debug(
          'Action: Transfer started',
          {
            component: 'Call Center voice',
            method: 'createTransferCall',
            data: {
              transferencePayload: payload
            }
          }
        );

        return payload;
      },
      /**
       * Handles the backend response of the call transfer operation
       * @param {any} _response - The response object.
       */
      responseHandler: () => {
        console.log('[Call Center Voice][createTransferCall] - call transfer started');

        // dispatch call state change
        this.store.dispatch(
          new fromCallCenterVoiceAction.SetCallState(
            fromCallCenterVoice.CallStates.ON_CALL
          )
        );
      }
    });
  }

  /**
   * Continues the transfer call.
   */
  continueTransferCall() {
    this.bugsnagService.leaveBreadcrumb('continueTransferCall', {
      callSid: this.currentReservation?.task?.sid,
      callState: this.currentAgentStatus,
    });
    this.holdStatus = false;
    this.store.dispatch(new fromCallCenterVoiceAction.ToggleCallHold(this.holdStatus));

    // Get any available conference SID from the active connection
    let conferenceSid = null;
    try {
      const device = this.getTwilioDevice();
      if (device) {
        const activeConnection = device._activeCall;
        if (activeConnection) {
          if (activeConnection.parameters?.ConferenceSid) {
            conferenceSid = activeConnection.parameters.ConferenceSid;
          } else if (activeConnection.customParameters?.conferenceSid) {
            conferenceSid = activeConnection.customParameters.conferenceSid;
          }
        }
      }
    } catch (error) {
      console.error('[Call Center Voice][continueTransferCall] - Error getting conference SID:', error);
      this.bugsnagService.notify(error);
    }

    // Mark the transfer as active in the service
    this.twilioDeviceService.markTransferActive(conferenceSid);
    console.log('[Call Center Voice][continueTransferCall] - Marked transfer as active with conference SID:', conferenceSid);

    this.withinCallTransferContext({
      /**
       * Builds the payload for continuing a call transfer.  This represents
       * user confirmation of the transfer.
       * @param {Object} context - The context object containing session.
       * @returns {Object} The call transfer payload.
       */
      payloadBuilder: (context) => {
        const { session } = context;

        datadogLogs.logger.debug(
          'Action: Transfer confirmed, remove the hold',
          {
            component: 'Call Center voice',
            method: 'ngOnChanges',
            data: {
              holdStatus: this.holdStatus,
              transferConfirmed: true,
            }
          }
        );

        return {
          authToken: session ? session.session_id : '',
          StatusCallbackEvent: fromCallCenterVoice.CallTransferStates.CONTINUE,
          FriendlyName: this.currentReservation.task.sid,
        } as fromCallCenterVoice.CallTransferPayload;
      }
    });
  }

  /**
   * Cancels the transfer call.
   */
  cancelTransferCall() {
    this.bugsnagService.leaveBreadcrumb('continueTransferCall', {
      callSid: this.currentReservation?.task?.sid,
      callState: this.currentAgentStatus,
    });
    this.holdStatus = false;
    this.store.dispatch(new fromCallCenterVoiceAction.ToggleCallHold(this.holdStatus));

    // Clear the transfer state in the service
    this.twilioDeviceService.clearTransferState();
    console.log('[Call Center Voice][cancelTransferCall] - Transfer state cleared');

    this.withinCallTransferContext({
      /**
       * Builds the payload for canceling a call transfer.
       * @param {Object} context - The context object containing session.
       * @returns {Object} The call transfer payload.
       */
      payloadBuilder: (context) => {
        const { session } = context;

        datadogLogs.logger.debug(
          'Action: Transfer canceled, remove the hold',
          {
            component: 'Call Center voice',
            method: 'ngOnChanges',
            data: {
              holdStatus: this.holdStatus,
              transferConfirmed: false,
            }
          }
        );

        return {
          authToken: session ? session.session_id : '',
          StatusCallbackEvent: fromCallCenterVoice.CallTransferStates.CANCEL,
          FriendlyName: this.currentReservation.task.sid,
        } as fromCallCenterVoice.CallTransferPayload;

      }
    });
  }


  public endCall(): void {
    // Handle user interaction to ensure AudioContext is running
    this.twilioDeviceService.handleUserInteraction();

    this.changeWorkerAvailability(true);
    this.endCallButtonPress = true;

    // Handle mute state first
    if (this.currentReservation && this.callIsMuted) {
      this.store.dispatch(new fromCallCenterVoiceAction.ToggleCallMute());
    }

    // Handle hold state second
    if (this.currentReservation && this.holdStatus) {
      // Pass fromEndCall flag to ensure proper cleanup
      this.toggleCallHold(undefined, this.endCallButtonPress);
    }

    this.changeCallToNumber({ callToNumber: '', lastNumberPressed: null });
    console.log('[Call Center Voice][endCall] - Ending current call');

    // Special handling for active transfers
    if (this.twilioDeviceService.isActiveTransfer) {
      console.log('[Call Center Voice][endCall] - Active transfer detected, disconnecting agent only');

      // Disconnect the agent's connection only, without affecting the conference
      try {
        // Get device and current connection
        const device = this.getTwilioDevice();
        if (device && device._activeCall) {
          // Disconnect just the current connection
          device._activeCall.disconnect();
          console.log('[Call Center Voice][endCall] - Agent disconnected from conference');
        } else if (this.currentConnection && typeof this.currentConnection.disconnect === 'function') {
          this.currentConnection.disconnect();
          console.log('[Call Center Voice][endCall] - Agent disconnected from conference via currentConnection');
        }
      } catch (error) {
        console.error('[Call Center Voice][endCall] - Error disconnecting agent from conference', error);
        this.bugsnagService.notify(error);
      }

      // Reset UI state
      this.setCallState({
        callState: fromCallCenterVoice.CallStates.IDLE
      });

      if (this.wasAgentOnACall) {
        this.moveToCoolDown = true;
        const workspaceActivity = this.workspaceActivities.find(
          (activity: any) => {
            return activity.friendlyName === COOLDOWN_ACTIVITY_FRIENDLY_NAME;
          });
        this.updateWorkerStatus(workspaceActivity);
        this.setAgentState({
          agentState: fromCallCenterVoice.AgentStates.COOLDOWN,
        }, 'endCall()');
      }

      this.currentConnection = null;
      this.currentReservation = null;
      this.disableHold = false;
      this.endCallButtonPress = false;
      return;
    }

    if (
      this.currentReservation && this.currentReservation.task &&
      (this.currentReservation?.task.attributes.direction === 'outbound' ||
       this.currentReservation.task.attributes.type === 'callback')
    ) {
      this.cancelOutboundCall = true;
      this.onCancellingOutboundCall();
    } else {
      // Use the centralized method for disconnection
      this.disconnectCalls();

      this.setCallState({
        callState: fromCallCenterVoice.CallStates.IDLE
      });

      if (this.wasAgentOnACall) {
        this.moveToCoolDown = true;
        const workspaceActivity = this.workspaceActivities.find(
          (activity: any) => {
            return activity.friendlyName === COOLDOWN_ACTIVITY_FRIENDLY_NAME;
          });
        this.updateWorkerStatus(workspaceActivity);
        this.setAgentState({
          agentState: fromCallCenterVoice.AgentStates.COOLDOWN,
        }, 'endCall()');
      }
    }

    this.currentReservation = null;
    this.disableHold = false;
    this.currentConnection = null;
  }

  /* ********** Agent States Handling ********** */
  private onDisconnectingAgent(workspaceActivity?: any): void {
    if (workspaceActivity === undefined) {
      workspaceActivity = this.workspaceActivities.find(
        (workspaceActivity: any) => {
          return (
            workspaceActivity.friendlyName === NOANSWER_ACTIVITY_FRIENDLY_NAME
          );
        }
      );
      this.setAgentState({
        agentState: fromCallCenterVoice.AgentStates.INACTIVE,
        agentStateData: workspaceActivity.sid,
      }, 'onDisconnectingAgent()');
      this.updateWorkerStatus(workspaceActivity);
    } else {
      workspaceActivity = this.workspaceActivities.find(
        (workspaceActivity: any) => {
          return (
            workspaceActivity.friendlyName === OFFLINE_ACTIVITY_FRIENDLY_NAME
          );
        }
      );
      this.updateWorkerStatus(workspaceActivity);
    }
  }

  /**
   * Update worker request with desired worker status
   *
   * @private
   * @param {*} workspaceActivity
   * @memberof CallCenterVoiceComponent
   */
  private updateWorkerStatus(workspaceActivity) {
    if (this.worker) {
      this.bugsnagService.leaveBreadcrumb('UpdateWorkerStatus', {
        workerSid: this.worker?.sid,
        workerName: this.worker?.attributes?.name,
        workerActivity: this.worker?.activityName,
        workspaceActivity: workspaceActivity,
      });
      UtilsHelper.workerUpdateSequential(
        this.worker,
        { ActivitySid: workspaceActivity.sid },
        (error, worker) => {
          if (error) {
            this.bugsnagService.notify(error)
            return console.error(
              'There was an error updating the worker: ',
              error
            );
          }
          console.log(
            '[Call Center Voice][updateWorkerStatus] - Worker Activity has been updated to: ',
            workspaceActivity.friendlyName
          );
        }
      );
    }
  }
  private onConnectingAgentReady(workspaceActivity: any): void {
    // update the channels availability
    this.bugsnagService.leaveBreadcrumb('UpdateWorkerChannels', {
      workerSid: this.worker?.sid,
      workerName: this.worker?.attributes?.name,
      workerActivity: this.worker?.activityName,
      workspaceActivity: workspaceActivity,
    });
    this.enableWorkerChannels().pipe(takeUntil(this.ngUnsubscribe)).subscribe(
      (updatedWorkerChannels: Array<TwilioWorkerChannel>) => {
        console.log('[Call Center Voice][onConnectingAgentReady] - workerChannels', this.workerChannels);
        return updatedWorkerChannels.forEach(updatedWorkerChannel => {
          let updatedWorkerChannelIndex: number = this.workerChannels.findIndex(
            workerChannel => {
              return workerChannel.sid === updatedWorkerChannel.sid;
            }
          );
          let newWorkerChannels = [
            ...this.workerChannels.slice(0, updatedWorkerChannelIndex),
            updatedWorkerChannel,
            ...this.workerChannels.slice(updatedWorkerChannelIndex + 1),
          ];
          this.workerChannels = newWorkerChannels;
        });
      }
    );
    if (this.worker) {
      let readyActivity: any = this.getWorkerActivityByFriendlyName(
        READY_ACTIVITY_FRIENDLY_NAME
      );
      /** CCC-716 The UtilsHelper.workerUpdateSequential was causing kind of a race condition
       * for scenarios in which we need to update the worker to a different status than the
       * twilio natural one following a CP config like on_cascade_inactive that is why it was
       * remove and replaced to a normal worker update.
       *
       * NOTE:
       * The UtilsHelper.workerUpdateSequential will prevent Twilio errors that look like this:
       * "Worker update conflicts with another update for worker"
       * but this replacement is no reitroducing this error since it was not changed in all places
       * we still using UtilsHelper.workerUpdateSequential in other places.
       *
       */
      // Enhanced logging before and after worker activity update
      this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateAttempt', {
        workerId: this.worker?.sid,
        fromActivity: this.worker?.activitySid,
        toActivity: readyActivity.sid,
        toActivityName: readyActivity.friendlyName,
        currentAgentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
        context: 'onConnectingAgentReady',
        timestamp: new Date().toISOString()
      });

      this.workerClient.update(
        'ActivitySid',
        readyActivity.sid,
        (error, worker) => {
          if (error) {
            this.bugsnagService.notify(error, {
              severity: 'error',
              metadata: {
                workerActivityUpdate: {
                  workerId: this.worker?.sid,
                  targetActivitySid: readyActivity.sid,
                  targetActivityName: readyActivity.friendlyName,
                  errorCode: error.code,
                  errorMessage: error.message,
                  context: 'onConnectingAgentReady',
                  currentWorkerState: this.worker
                }
              }
            });
            
            this.cpLoggingService.registerEvent(
              'worker.activity.update.failed',
              'error',
              {
                workerId: this.worker?.sid,
                targetActivity: readyActivity.friendlyName,
                error: error.message,
                errorCode: error.code,
                context: 'onConnectingAgentReady'
              }
            );
            
            return console.error(
              '[Call Center Voice][onConnectingAgentReady] - There was an error updating the worksers activity to READY => ',
              error
            );
          }
          
          // Success logging
          this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateSuccess', {
            workerId: worker?.sid,
            newActivity: worker?.activitySid,
            newActivityName: worker?.activityName,
            context: 'onConnectingAgentReady',
            timestamp: new Date().toISOString()
          });
          
          this.cpLoggingService.registerEvent(
            'worker.activity.update.success',
            'success',
            {
              workerId: worker?.sid,
              newActivity: worker?.activityName,
              context: 'onConnectingAgentReady'
            }
          );
          
          console.log(
            '[Call Center Voice][onConnectingAgentReady] - Workers activity was successfully updated to READY'
          );
          this.worker = worker;
          this.cd.markForCheck();
        }
      );
    }
  }

  private onConnectingAgentInactive(workspaceActivity: any): void {
    this.bugsnagService.leaveBreadcrumb('UpdateWorkerStatus', {
      workerSid: this.worker?.sid,
      workerName: this.worker?.attributes?.name,
      workerActivity: this.worker?.activityName,
      workspaceActivity: workspaceActivity,
    });
    
    // Enhanced logging before and after worker activity update
    this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateAttempt', {
      workerId: this.worker?.sid,
      fromActivity: this.worker?.activitySid,
      toActivity: workspaceActivity.sid,
      toActivityName: workspaceActivity.friendlyName,
      currentAgentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
      context: 'onConnectingAgentInactive',
      timestamp: new Date().toISOString()
    });
    
    UtilsHelper.workerUpdateSequential(
      this.worker,
      { ActivitySid: workspaceActivity.sid },
      (error, worker) => {
        if (error) {
          this.bugsnagService.notify(error, {
            severity: 'error',
            metadata: {
              workerActivityUpdate: {
                workerId: this.worker?.sid,
                targetActivitySid: workspaceActivity.sid,
                targetActivityName: workspaceActivity.friendlyName,
                errorCode: error.code,
                errorMessage: error.message,
                context: 'onConnectingAgentInactive',
                currentWorkerState: this.worker
              }
            }
          });
          
          this.cpLoggingService.registerEvent(
            'worker.activity.update.failed',
            'error',
            {
              workerId: this.worker?.sid,
              targetActivity: workspaceActivity.friendlyName,
              error: error.message,
              errorCode: error.code,
              context: 'onConnectingAgentInactive'
            }
          );
          
          return console.error(
            'There was an error updating the worker: ',
            error
          );
        }

        // Success logging
        this.bugsnagService.leaveBreadcrumb('WorkerActivityUpdateSuccess', {
          workerId: worker?.sid,
          newActivity: worker?.activitySid,
          newActivityName: worker?.activityName,
          context: 'onConnectingAgentInactive',
          timestamp: new Date().toISOString()
        });
        
        this.cpLoggingService.registerEvent(
          'worker.activity.update.success',
          'success',
          {
            workerId: worker?.sid,
            newActivity: worker?.activityName,
            context: 'onConnectingAgentInactive'
          }
        );

        console.log(
          '[Call Center Voice][onConnectingAgentInactive] - Worker Activity has been updated to: ',
          workspaceActivity.friendlyName
        );
        return this.setAgentState({
          agentState: fromCallCenterVoice.AgentStates.INACTIVE,
          agentStateData: workspaceActivity.sid,
        }, 'onConnectingAgentInactive()');
      }
    );
  }

  private onAgentWrappingCall(): void {
    this.workspaceActivities$
      .pipe(filter(
        (
          workspaceActivities: fromWorkspaceActivities.WorkspaceActivitiesState
        ) => workspaceActivities.loaded
      )
      ,map(
        (
          workspaceActivities: fromWorkspaceActivities.WorkspaceActivitiesState
        ) => workspaceActivities.activities
      )
      ,take(1))
      .subscribe((activities: Array<any>) => {
        let coolDownActivity: any = activities.find((activity: any) => {
          return activity.friendlyName === COOLDOWN_ACTIVITY_FRIENDLY_NAME;
        });
        let coolDownActivitySid: string = coolDownActivity.sid;
      });
  }

  private onCompletingCall(nextAgentActivity: any): void {
    switch (nextAgentActivity.friendlyName) {
      case READY_ACTIVITY_FRIENDLY_NAME: {
        this.setAgentState({
          agentState: fromCallCenterVoice.AgentStates.CONNECTING_READY,
          agentStateData: nextAgentActivity,
        }, 'onCompletingCall()');
        break;
      }

      case OFFLINE_ACTIVITY_FRIENDLY_NAME: {
        this.setAgentState({
          agentState: fromCallCenterVoice.AgentStates.DISCONNECTING,
          agentStateData: nextAgentActivity,
        }, 'onCompletingCall()');
        break;
      }

      default: {
        this.setAgentState({
          agentState: fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE,
          agentStateData: nextAgentActivity,
        }, 'onCompletingCall()');
      }
    }

    return;
  }

  /**
   * Sets the call state to IDLE and agent state to COOLDOWN
   * @param tag Logging identifier
   */
  private agentToIdleCooldown(tag: string): void {
    this.setCallState({ callState: fromCallCenterVoice.CallStates.IDLE });
    this.setAgentState({ agentState: fromCallCenterVoice.AgentStates.COOLDOWN }, tag);
  }

  /**
   * Updates the worker's activity to COOLDOWN in Twilio TaskRouter
   * @param tag Logging identifier
   */
  private workerActivityToCooldown(tag: string): void {
    if (this.worker?.connected) {
      let workspaceActivity = this.workspaceActivities.find((workspaceActivity: any) => workspaceActivity.friendlyName === COOLDOWN_ACTIVITY_FRIENDLY_NAME);
      if (workspaceActivity) {
        UtilsHelper.workerUpdateSequential(this.worker, { ActivitySid: workspaceActivity.sid }, (error) => {
            if (error) {
              this.bugsnagService.notify(error);
              return console.error('There was an error updating the worker: ', error);
            }
            console.log(`[Call Center Voice][${tag}] - Worker Activity has been updated to: `, workspaceActivity.friendlyName);
        });
      }
    }
  }

  /**
   * Completes a Twilio TaskRouter task with error handling for WebSocket issues
   * @param taskSid The task SID to complete
   * @param tag Logging identifier
   * @param callback Function to call after task completion attempt
   */
  private completeTaskRouterTask(task: { sid: string, assignmentStatus: string }, tag: string, callback: (error: any, updatedTask: any) => void): void {
    // Check if task exists and is in a valid state for updating
    // Also verify that worker connection is active
    if (
      this.workerClient && task && task.sid &&
      task.assignmentStatus !== 'completed' &&
      task.assignmentStatus !== 'canceled' &&
      task.assignmentStatus !== 'wrapping' &&
      this.worker?.connected
    ) {
      this.workerClient.updateTask(task.sid, {
        AssignmentStatus: 'completed',
      }, (error, updatedTask) => {
        if (error) {
          this.bugsnagService.notify(error);
          console.error(`[Call Center Voice][${tag}] - There was an error updating the task: `, error);
          // If we get a WebSocket error (400 Bad Request), try to reconnect and retry
          if (error.status === 400 && error.message && error.message.includes('WebSocket')) {
            console.log('[Call Center Voice][onCancellingOutboundCall()] - WebSocket error detected, will try to reconnect');
            this.reconnectInTwilio();
          }
        }
        console.log(`[Call Center Voice][${tag}] - Task updated: `, updatedTask);
        callback(error, updatedTask);
      });
    } else {
      callback(new Error('Worker client not initialized'), null);
    }
  }

  private onCancellingOutboundCall(): void {
    this.bugsnagService.leaveBreadcrumb('onCancellingOutboundCall', {
      callSid: this.currentReservation?.task?.sid,
      callNumber: this.currentReservation?.task?.attributes?.caller,
      destinationNumber: this.currentReservation?.task?.attributes?.to,
      callStatus: this.currentReservation?.reservationStatus,
      cancelOutboundCall: this.cancelOutboundCall,
      currentReservation: this.currentReservation,
      workspaceActivities: this.workspaceActivities,
    });

    // Fix for outbound calls not disconnecting - Always disconnect calls regardless of agent state
    this.disconnectCalls();

    this.bugsnagService.leaveBreadcrumb('CancellingOutboundCall', {
      taskSid: this.currentReservation?.task?.sid,
      worker: this.worker?.sid,
      task: this.currentReservation?.task,
    });

    this.completeTaskRouterTask(
      this.currentReservation?.task,
      'onCancellingOutboundCall()',
      () => {
        console.log('[Call Center Voice][onCancellingOutboundCall()] - this.currentAgentStatus: ', fromCallCenterVoice.AgentStates[this.currentAgentStatus]);
        if (this.currentAgentStatus !== fromCallCenterVoice.AgentStates.INACTIVE &&
            this.currentAgentStatus !== fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE &&
            this.currentAgentStatus !== fromCallCenterVoice.AgentStates.READY &&
            this.currentAgentStatus !== fromCallCenterVoice.AgentStates.CONNECTING_READY &&
            this.currentAgentStatus !== fromCallCenterVoice.AgentStates.OFFLINE) {

          this.store.dispatch(new fromCollectionEventsActions.CollectionCallCancelled());
          this.agentToIdleCooldown('onCancellingOutboundCall()');
          this.workerActivityToCooldown('onCancellingOutboundCall()');

        }
      }
    );
  }

  private onCancellingCall(): void {
    this.topToastMessages = [
      {
        message: 'The caller has hung up',
        type: TopToastComponent.NORMAL,
      },
    ];
    this.showTopToast = true;
    this.cd.detectChanges();
  }

  private onTimedOutCall(): void {
    this.topToastMessages = [
      {
        message: 'The pending reservation you had, has timed out.',
        type: TopToastComponent.BIG_NORMAL,
      },
    ];
    this.showTopToast = true;
    this.cd.detectChanges();
  }

  private onCascadedCall(): void {
    this.topToastMessages.push({
      message: 'The pending reservation you had, has been cascaded.',
      type: TopToastComponent.BIG_NORMAL,
    });
    this.showTopToast = true;
  }

  public topToastHidden(event: any): void {
    this.showTopToast = false;
    this.topToastMessages = [];
    this.disableHold = false;
  }

  /* ********** Sync Client ********** */

  /**
   * Registers a SyncClient when Session is ready
   *
   * @private
   * @returns {void}
   *
   * @memberOf DashboardComponent
   */
  private registerSyncClient(): void {
    this.session$
      .pipe(filter((sessionState: fromSession.SessionState) => sessionState.loaded)
      ,map((sessionState: fromSession.SessionState) => sessionState.session)
      ,take(1))
      .subscribe((session: SessionData) => {
        this.store.dispatch(new fromSyncClientAction.RegisterSyncClient());
      });

    return;
  }

  private attachSendDigitsHandler() {
    this.callCenterVoice$
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe(({ lastNumberPressed, callState }) => {
        // First check if twilioDeviceService or getDevice() returns null
        const device = this.twilioDeviceService.getDevice();
        if (!device) {
          console.log('[Call Center Voice][attachSendDigitsHandler] - Twilio device not initialized');
          return;
        }

        // Safely access activeConnection with a try-catch block
        let connection = null;
        try {
          connection = device._activeCall;
        } catch (error) {
          console.log('[Call Center Voice][attachSendDigitsHandler] - Error accessing activeConnection', error.message);
          return;
        }

        if (
          lastNumberPressed !== null &&
          connection &&
          callState === fromCallCenterVoice.CallStates.ON_CALL
        ) {
          try {
            // Use the connection obtained from the device service
            connection.sendDigits(`${lastNumberPressed}`);
          } catch (error) {
            console.log(
              '[Call Center Voice][attachSendDigitsHandler] - Error sending digits',
              error.message
            );
          }
        }
      });
  }

  /**
   * Updates the current list of workers with the new data received through the
   * agent_status sync map
   *
   * @private
   * @param {Array<any>} workers
   * @param {Array<any>} mapItems
   * @returns {Array<any>}
   *
   * @memberOf DashboardComponent
   */
  private updateWorkersStatus(
    workers: Array<any>,
    mapItems: Array<any>
  ): Array<any> {
    let workersEntities: { [id: string]: any } = workers.reduce(
      (entities: { [id: string]: any }, worker: any) => {
        return {
          ...entities,
          [worker.sid]: worker,
        };
      },
      {}
    );
    mapItems.forEach((item: any) => {
      let workerSid: string = item.descriptor.key;

      // update Worker status
      if (workersEntities[workerSid]) {
        workersEntities[workerSid].status = item.descriptor.data;
      }
    });

    return Object.keys(workersEntities).map(id => workersEntities[id]);
  }

  /**
   * Updates the current list of workers with the new data received through the
   * agent_stats sync map
   *
   * @private
   * @param {Array<any>} workers
   * @param {Array<any>} mapItems
   * @returns {Array<any>}
   *
   * @memberOf DashboardComponent
   */
  private updateWorkersStats(
    workers: Array<any>,
    mapItems: Array<any>
  ): Array<any> {
    let workersEntities: { [id: string]: any } = workers.reduce(
      (entities: { [id: string]: any }, worker: any) => {
        return {
          ...entities,
          [worker.sid]: worker,
        };
      },
      {}
    );
    mapItems.forEach((item: any) => {
      let workerSid: string = item.descriptor.key;

      // update Worker status
      if (workersEntities[workerSid]) {
        workersEntities[workerSid].stats = item.descriptor.data;
      }
    });

    return Object.keys(workersEntities).map(id => workersEntities[id]);
  }

  /**
   * Updates the current hash of tasks with the new data received through the
   * task_list sync map
   *
   * @private
   * @param {{[id: string]: any}} tasks
   * @param {Array<any>} mapItems
   * @returns {*}
   *
   * @memberOf CallCenterVoiceComponent
   */
  private updateTasks(tasks: { [id: string]: any }, mapItems: Array<any>): any {
    let updatedTasks = {
      ...tasks,
    };
    mapItems.forEach(item => {
      updatedTasks[item.key] = {
        ...updatedTasks[item.key],
        status: item.descriptor.data,
      };
    });

    return updatedTasks;
  }

  /**
   * Listen for callQueues
   *
   * @private
   *
   * @memberOf CallCenterVoiceComponent
   */
  private attachToCallQueues() {
    this.callQueues$
      .pipe(takeUntil(this.ngUnsubscribe)
      ,filter((state: fromCallQueues.CallQueuesState) => state.loaded)
      ,map((state: fromCallQueues.CallQueuesState) => state.queues))
      .subscribe(callQueues => {
        this.callQueues = callQueues;
      });
  }

  /* ********** Worker Channels ********** */
  private getWorkerChannelyByName(name: string): any {
    return this.workerChannels.find(
      channel => channel.taskChannelUniqueName === name
    );
  }

  private getWorkerChannelBySid(workerChannelSid: string): any {
    return this.workerChannels.find(
      channel => channel.sid === workerChannelSid
    );
  }

  private enableWorkerChannels(): Observable<Array<TwilioWorkerChannel>> {
    return forkJoin(
      this.workerChannels
        .filter((workerChannel: TwilioWorkerChannel) => {
          return !workerChannel.available;
        })
        .map((workerChannel: TwilioWorkerChannel) => {
          return this.enableWorkerChannel(workerChannel.sid);
        })
    );
  }

  private enableWorkerChannel(
    workerChannelSid: string
  ): Observable<TwilioWorkerChannel> {
    let workerSid: string = this.worker.sid;
    return this.twilioWorkerChannelsProvider.update({
      workerSid,
      workerchannelSid: workerChannelSid,
      body: {
        available: true,
      },
    });
  }

  /* ********** Worker Activities ********** */
  private getWorkerActivityByFriendlyName(friendlyName: string): any {
    if (!this.workspaceActivities) {
      return null;
    }
    return this.workspaceActivities.find((workspaceActivity: any) => {
      return workspaceActivity.friendlyName === friendlyName;
    });
  }

  public setNumber(number) {
    this.dialerNumber = number;
    this.store.dispatch(
      new fromCallCenterVoiceAction.SetAgentNumber({
        agentNumber: this.dialerNumber,
      })
    );
    this.contactUri = this.dialerNumber.toString();
    this.getFreshWorkerForUpdate(this.worker.sid, { contact_uri:this.contactUri },  (error, worker) => {
      if (error) {
        return console.error(
          'There was an error updating the worker: ',
          error
        );
      }
      console.log('[Call Center Voice][setNumber] - Worker has been updated successfully');
    });
  }

  public setValues(
    callStateParams,
    leadId,
    leadLocationId,
    leadESId,
    ledgerId,
    customerId,
    ledgerLocationId,
    customerESId
  ) {
    this.leadId = leadId;
    this.ledgerId = ledgerId;
    this.customerId = customerId;
    this.leadLocationId = leadLocationId;
    this.ledgerLocationId = ledgerLocationId;
    this.leadESId = leadESId;
    this.customerESId = customerESId;
    this.isCallPlaced = true;
    this.setCallState(callStateParams);
  }

  public ensureTwilioDeviceInitialization() {
    // This is always a user-initiated action, so ensure AudioContext is running
    this.twilioDeviceService.handleUserInteraction();

    // Set up the subscription to track call placement state changes
    // This is from the original implementation and must be maintained
    let subscription = this.callCenterVoice$
      .pipe(filter(
        (callCenterVoiceState: fromCallCenterVoice.CallCenterVoiceState) =>
          callCenterVoiceState.isCallPlaced
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe(res => {
        if (res) {
          this.isCallPlaced = true;
          this.setCallState(res);
          subscription.unsubscribe();
        }
      });

    // First check if device is initialized
    if (!this.twilioDeviceService.isInitialized()) {
      console.log('[Call Center Voice][placeCall] - Twilio device not initialized, attempting initialization');

      // Show loading state
      this.topToastMessages = [
        {
          message: 'Initializing call system...',
          type: TopToastComponent.NORMAL,
        },
      ];
      this.showTopToast = true;
      this.cd.detectChanges();

      // Initialize device before proceeding with call
      this.twilioDeviceService.initializeDevice(true).then(() => {
        console.log('[Call Center Voice][placeCall] - Device initialized successfully, proceeding with call');
        this.showTopToast = false;
      }).catch(error => {
        console.error('[Call Center Voice][placeCall] - Failed to initialize Twilio device', error);
        this.bugsnagService.notify(error);

        // Show error message
        this.topToastMessages = [
          {
            message: 'Unable to initialize call system. Please try again.',
            type: TopToastComponent.ERROR,
          },
        ];
        this.showTopToast = true;
        this.cd.detectChanges();
      });
    }
  }

  /**
   *check for connection method
   *
   * @private
   * @param {*} [worker]
   * @returns {number}
   * @memberof CallCenterVoiceComponent
   */
  private getConnectionMethod(worker?): number {
    if (worker) {
      this.worker = worker;
    }
    if (this.worker.attributes && this.worker.attributes.contact_uri) {
      if (this.worker.attributes.contact_uri.search(/^sip:/) !== -1) {
        return fromCallCenterVoice.ConnectionMethods.VOIP;
      }
      if (this.worker.attributes.contact_uri.search(/^\+?\d{10,11}$/) !== -1) {
        return fromCallCenterVoice.ConnectionMethods.PHONE;
      }
      if (this.worker.attributes.contact_uri.search(/^client:/) !== -1) {
        return fromCallCenterVoice.ConnectionMethods.COMPUTER;
      }
    }
    return fromCallCenterVoice.ConnectionMethods.NONE;
  }

  private attatchToCallCenterConfigurationUpdate() {
    this.callCenterConfiguration$
      .pipe(takeUntil(this.ngUnsubscribe)
      ,filter(
        (state: fromConfigureCallCenter.CallCenterConfigurationState) =>
          state.loaded
      )
      ,map(
        (state: fromConfigureCallCenter.CallCenterConfigurationState) =>
          state.callCenterConfiguration
      ))
      .subscribe((res: CallCenter) => {
        this.callCenterConfiguration = res;
        this.hideQueueNameInbound =
          this.callCenterConfiguration.hide_queue_from_agent === 1;
        this.hideCallerTypeInbound =
          this.callCenterConfiguration.hide_caller_type_from_agent === 1;
        this.coolDownTime = parseInt(res.cooldown_period + '', 10);
        this.noCallCenterConfig = false;
      });
  }

  private attatchToCallCenterConfigurationError() {
    this.callCenterConfiguration$
      .pipe(takeUntil(this.ngUnsubscribe)
      ,filter(
        (state: fromConfigureCallCenter.CallCenterConfigurationState) =>
          !state.loaded
      )
      ,map(
        (state: fromConfigureCallCenter.CallCenterConfigurationState) =>
          state.error
      ))
      .subscribe((res: any) => {
        if (res) {
          this.noCallCenterConfig = true;
        }
      });
  }

  private checkWorkerConnectionMethod(worker) {
    let connectionMethod = this.getConnectionMethod(worker);
    this.store.dispatch(
      new fromCallCenterVoiceAction.SetConnectionMethod(connectionMethod)
    );
    this.isLocationLogin = UtilsHelper.isLocationLogin();
    this.connectedVia = this.isLocationLogin
      ? fromCallCenterVoice.ConnectionMethods.COMPUTER
      : connectionMethod;
  }

  /**
   *
   * This function is subscribe to twilio worker events, so is going to update the status bar
   * once we detect the twilio change, and not assuming the status changed.
   * @private
   * @memberof CallCenterVoiceComponent
   */
  public listenTwilioWorkerUpdates() {
    this.sharedService.twilioWorkerUpdate$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((activityName: any) => {
      this.inactiveStatusUpdated = false;
      this.cd.detectChanges();

      this.cpLoggingService.registerEvent(
        'agent.activity.changed',
        'success',
        {
          activityName,
          agentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus]
        }
      );
      this.cpLoggingService.stopRecordingPerformance('inbound.call.agent.status.change.duration');

      console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] - Activity update received from Twilio: ', activityName);
      console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] - currentAgentStatus: ',  fromCallCenterVoice.AgentStates[this.currentAgentStatus]);
      let workspaceActivity: any;
      switch (activityName) {
        case COOLDOWN_ACTIVITY_FRIENDLY_NAME:
          if (!this.endCallButtonPress && this.connectedVia === fromCallCenterVoice.ConnectionMethods.COMPUTER) {
            this.toggleCallHold(undefined, false, true);
          }
          console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] - Dispatch status bar update to Cool-down');
          // CCC-1550. this condition was added due to a timing issue that happens when the queue time and the cascade timeout
          // are the same, in this case the agent status is moved to cooldown by the lambda (CHECK THIS) even though the call is not made.
          // in this case it is necessary to stop the cooldown process and return to the agent to a ready status.
          if (this.wasAgentOnACall || this.callerHasHungUp) {
            this.setAgentState({
              agentState: fromCallCenterVoice.AgentStates.COOLDOWN,
            }, 'twilioWorkerUpdate$.subscribe');
          } else {
            let readyActivity: any = this.workspaceActivities.find(
              (activity: any) => {
                return (
                  activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME
                );
              }
            );
            this.setAgentState({
              agentState: fromCallCenterVoice.AgentStates.CONNECTING_READY,
              agentStateData: readyActivity,
            }, 'twilioWorkerUpdate$.subscribe');
          }

          this.setCallState({
            callState: fromCallCenterVoice.CallStates.IDLE,
          });
          this.changeWorkerAvailability(true);
          this.wasAgentOnACall = false;
          break;
        case READY_ACTIVITY_FRIENDLY_NAME:
          this.endCallButtonPress = false;
          this.agentNoAnswer = false;
          this.callerHasHungUp = false;
          console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] - Dispatch status bar update to Ready');
          this.setAgentState({
            agentState: fromCallCenterVoice.AgentStates.READY,
          }, 'twilioWorkerUpdate$.subscribe');
          this.sharedService.agentBarChangedToReadyStatus$.next(null);
          this.wasAgentOnACall = false;
          break;
        case OFFLINE_ACTIVITY_FRIENDLY_NAME:
          workspaceActivity = this.workspaceActivities.find((activity: any) => {
            return activity.friendlyName === OFFLINE_ACTIVITY_FRIENDLY_NAME;
          });
          console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] - Dispatch status bar update to Offline');
          this.setAgentState({
            agentState: fromCallCenterVoice.AgentStates.OFFLINE,
            agentStateData: workspaceActivity.sid,
          }, 'twilioWorkerUpdate$.subscribe');
          this.wasAgentOnACall = false;
          break;
        case BUSY_ACTIVITY_FRIENDLY_NAME:
          let busyActivity: any = this.workspaceActivities.find(
            (workspaceActivity: any) => {
              return workspaceActivity.friendlyName === activityName;
            }
          );
          console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] - Dispatch status bar update to Inactive status');
          this.setAgentState({
            agentState: fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE,
            agentStateData: busyActivity,
          }, 'twilioWorkerUpdate$.subscribe');
          this.wasAgentOnACall = false;
          break;
        case NOANSWER_ACTIVITY_FRIENDLY_NAME:
          this.inboundRingStop = new Date();
          this.agentNoAnswer = true;
          let inactiveActivity: any = this.workspaceActivities.find(
            (workspaceActivity: any) => {
              return workspaceActivity.friendlyName === activityName;
            }
          );
          console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] - Dispatch status bar update to Inactive status');
          this.setAgentState({
            agentState: fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE,
            agentStateData: inactiveActivity,
          }, 'twilioWorkerUpdate$.subscribe');
          this.wasAgentOnACall = false;
          break;
        case ONCALL_ACTIVITY_FRIENDLY_NAME:
          this.setCallState({
            callState: fromCallCenterVoice.CallStates.IDLE,
          });
          if (this.currentWorkerOnCallStatus !== 'custom1') {
            console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] - Dispatch status bar update to On-call status');
            this.callBeingAccepted = false;
            this.setAgentState({
              agentState: fromCallCenterVoice.AgentStates.ON_INBOUND_CALL,
            }, 'twilioWorkerUpdate$.subscribe');
          } else {
            // tslint:disable-line
            if (!this.cancelOutboundCall) {
              console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] - Dispatch status bar update to On-call status');
              this.setAgentState({
                agentState: fromCallCenterVoice.AgentStates.ON_OUTBOUND_CALL,
                agentStateData: {
                  toNumber: this.oBcallNumber ? this.oBcallNumber.toString() : '',
                },
              }, 'twilioWorkerUpdate$.subscribe');
              this.setCallState({
                callState: fromCallCenterVoice.CallStates.ON_CALL,
              });
            }
          }
          this.wasAgentOnACall = true;
          break;
        default:
          // CCC-1338 -> CCC-915 - When there is a callback call and it is canceled through the dashboard.
          // This code is similar to the next if statement but for better readability, it is kept separately.
          if (this.currentReservation  && this.currentReservation.task && this.currentReservation.task.attributes.rejectedBy && this.currentReservation.task.attributes.rejectedBy === 'dashboard' &&
              activityName !== 'Ready' && activityName !== 'Offline' && activityName !== 'On-call' && activityName !== 'Rejected') {

            const connectingInactiveActivity: any = this.workspaceActivities.find((workspaceActivity: any) => {
              return workspaceActivity.friendlyName === activityName;
            });
            console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] setAgentState - Default case Rejected By Dashboard');
            this.setAgentState({
              agentState: fromCallCenterVoice.AgentStates.INACTIVE,
              agentStateData: connectingInactiveActivity.sid,
            }, 'twilioWorkerUpdate$.subscribe');
            this.cd.markForCheck();
            break;
          }

          if ((activityName !== 'On-call' && activityName !== 'Rejected') || (activityName === 'Rejected' && this.callCenterConfiguration.on_reject_inactive)) {
            let connectingInactiveActivity: any = this.workspaceActivities.find(
              (workspaceActivity: any) => {
                return workspaceActivity.friendlyName === activityName;
              }
            );
            /* CCC-706 Instead of use fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE now we are using
             * fromCallCenterVoice.AgentStates.INACTIVE, since the fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE
             * is calling the UtilsHelper.workerUpdateSequential function and in this case we don't need to update the worker status
             * since this switch is listening to the worker status change.
             */
            console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe] setAgentState - Default case No Dashboard');
            this.setAgentState({
              agentState: fromCallCenterVoice.AgentStates.INACTIVE,
              agentStateData: connectingInactiveActivity.sid,
            }, 'twilioWorkerUpdate$.subscribe');
            this.cd.markForCheck();
          }
          this.wasAgentOnACall = false;
          break;
      }

      if (activityName !== READY_ACTIVITY_FRIENDLY_NAME) {
        this.getFreshWorkerForUpdate(this.worker.sid, { callback: 'none' },  (error, worker) => {
          if (error) {
            return console.error(
              'Call Center Voice][twilioWorkerUpdate$.subscribe][getFreshWorkerForUpdate] - There was an error updating the worker\'s attributes: ',
              error
            );
          }
          console.log('[Call Center Voice][twilioWorkerUpdate$.subscribe][getFreshWorkerForUpdate] - Worker\'s attributes has been updated successfully');
        });
      }
    });
  }

/**
 * It updates the worker's activity to the rejected activity
 */
  private voIpCallWasRejected() {
    let rejectedActivity: any = this.getWorkerActivityByFriendlyName(
      REJECTED_ACTIVITY_FRIENDLY_NAME
    );
    this.workerClient.update(
      'ActivitySid',
      rejectedActivity.sid,
      (error, worker) => {
        if (error) {
          return console.error(
            '[Call Center Voice][voIpCallWasRejected] - There was an error updating the worker\'s activity to Inactive => ',
            error
          );
        }
        console.log(
          '[Call Center Voice][voIpCallWasRejected] - Worker\'s activity was successfully updated to Inactive'
        );
        this.worker = worker;
        this.cd.markForCheck();
      }
    );
  }

  private checkConnection() {
    this.checkNetworkConnection();
    this.couldNotReconnect = true;
    this.showAttemptToReconnect = false;
    this.cd.markForCheck();
  }

  public manuallyReload() {
    window.location.reload();
  }

  /**
   * This function will reproduce the incoming callback audio.
   *
   * @private
   * @memberof CallCenterVoiceComponent
   */
  private playIncomingAudio(channelName: string) {
    let promise: any = null;
    if (this.audio === null || this.audio === undefined) {
      switch (channelName) {
        case 'Callback': {
          this.audio = new Audio('assets/mp3/incoming.mp3');
          break;
        }
        case 'video': {
          this.audio = new Audio('assets/mp3/incoming-video.wav');
          break;
        }
        default: {
          this.audio = new Audio('assets/mp3/incoming.mp3');
          break;
        }
      }
      this.audio.loop = true;
      promise = this.audio.play();
    } else {
      promise = this.audio.pause();
      this.audio = null;
    }
    if (promise) {
      promise.catch(err => {
        this.audio = null;
        console.log(err);
      });
    }
  }

  /**
   * Listen to the worker attributr change for when the caller hang up.
   * @private
   * @memberof CallCenterVoiceComponent
   */
  private subscribeHangup() {
    /** This function is probably going to be removed since we are not going
     * since we are not going to need the attribute validation, but leave it for
     * now until we confirm is not longer needed. NOTE: Backend is not sending
     * the attribute anymore.
     */
    this.sharedService.workerAttributesUpdated$
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((res: any) => {
        this.customerHangUpMessage = [
          {
            message: 'The caller has hung up',
            type: TopToastComponent.NORMAL,
          },
        ];
        this.customerHangUp = res.attributes.hangup;
        this.hangupWorker = res;
        if (UtilsHelper.isLocationLogin()) {
          this.removeHangupWorkerAttribute();
        }
        this.cd.markForCheck();
      });
  }

  /**
   *
   * When the caller hangup modal disappear delete the worker attribute
   * @private
   * @param {*} event
   * @memberof CallCenterVoiceComponent
   */
  public topToastClientHungUpHidden(event: any): void {
    this.customerHangUp = false;
    this.customerHangUpMessage = [];
    this.removeHangupWorkerAttribute();
  }

  private removeHangupWorkerAttribute() {
    if (this.hangupWorker) {
      // Remove the attribute once we move or complete the followup step
      let updatedAttributes = {
        ...this.hangupWorker.attributes,
      };
      if (updatedAttributes.hangup) {
        delete updatedAttributes.hangup;
        let props: any = {
          Attributes: updatedAttributes,
        };
        this.hangupWorker.update(props, (error, worker) => {
          if (error) {
            return console.error(
              'There was an error updating the worker: ',
              error
            );
          }
          console.log('[Call Center Voice][removeHangupWorkerAttribute] - Worker has been updated successfully');
        });
      }
    }
  }
  private listenToMakeReady() {
    this.sharedService.makeAgentReady$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(res => {
      let readyActivity = this.workspaceActivities.find(
        (workspaceActivity: any) => {
          return (
            workspaceActivity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME
          );
        }
      );

      //update worker with inactive status
      UtilsHelper.workerUpdateSequential(
        this.worker,
        {
          ActivitySid: readyActivity.sid,
        },
        (error, worker) => {
          if (error) {
            return console.error(
              'There was an error updating the worker: ',
              error
            );
          }
          console.log(
            '[Call Center Voice][listenToMakeReady] - Worker Activity has been updated to: ',
            readyActivity.friendlyName
          );
          console.log('[Call Center Voice][listenToMakeReady] - make agent ready');
          this.store.dispatch(
            new fromCallCenterVoiceAction.SetAgentState({
              agentState: fromCallCenterVoice.AgentStates.READY,
            })
          );
        }
      );
    });
  }

  /**
   *  Subscribe to get opened chats
   *
   * @memberOf CallCenterChatComponent
   */
  private getOpenedChats(): void {
    this.callCenterChat$
      .pipe(filter(
        (callCenterChatState: fromCallCenterChat.CallCenterChatState) =>
          callCenterChatState.openedChats !== null
      )
      ,map(
        (callCenterChatState: fromCallCenterChat.CallCenterChatState) =>
          callCenterChatState.openedChats
      )
      ,takeUntil(this.ngUnsubscribe))
      .subscribe(openedChats => {
        this.isThereActiveConversation = openedChats > 0 ? true : false;
      });
  }
  public activeConversationAlert($event) {
    if ($event) {
      this.showActiveModal = $event;
    }
  }
  public onCloseActiveConversation() {
    this.showActiveModal = false;
  }
  private listenToTwilioInitializationError() {
    this.twilioEventHandlerService.twilioInitializeError$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(() => {
      this.checkNetworkConnection();
      this.showErrorConnectionToast = false;
      this.couldNotReconnect = false;
      this.showTwilioInitializationError = true;
      this.showAttemptToReconnect = false;
      this.cd.markForCheck();
    });
  }

  private listenToAttemptToRecoveInitializationError() {
    this.twilioEventHandlerService.twilioAttemptToRecoverInitializeError$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(() => {
      if (!this.showTwilioInitializationError && !this.couldNotReconnect) {
        this.checkNetworkConnection();
        this.showAttemptToReconnect = true;
      }
      this.cd.markForCheck();
    });
  }

  public closeDisconnectedMessage() {
    this.showAttemptToReconnect = false;
    this.showErrorConnectionToast = false;
  }

  private updateWorkerContactUri(contactUri) {
    this.getFreshWorkerForUpdate(this.worker.sid, { contact_uri: contactUri }, (error, worker) => {
      if (error) {
        return console.error(
          '[Call Center Voice][updateWorkerContactUri] - There was an error updating the worker: ',
          error
        );
      }
      console.log('[Call Center Voice][updateWorkerContactUri] - Worker has been updated successfully');
    });
  }

  /**
   * function to subscribe get all locations from store
   */

  private attachToLocations() {
    this.allLocations$
      .pipe(
        filter((allLocationsState: fromAllLocations.AllLocationsState) => allLocationsState.loaded),
        map((allLocationsState: fromAllLocations.AllLocationsState) =>  allLocationsState.activeLocations),
        takeUntil(this.ngUnsubscribe)
      )
      .subscribe(locations => {
        if (Array.isArray(locations)) {
          this.fullLocationInfo = locations.map(a => Object.assign({}, a));
        }
      });
  }

  /**
   * This function listens to Twilio device no mic error and is an insurance in case the
   * error reach Twilio
   * @private
   * @memberof CallCenterVoiceComponent
   */
  private subscribeToNoMicError() {
    this.sharedService.moveAgentToNoMic$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(res => {
      setTimeout(() => {
        let inactiveActivity: any = this.workspaceActivities.find(
          (workspaceActivity: any) => {
            return workspaceActivity.friendlyName === NOMIC_ACTIVITY_FRIENDLY_NAME;
          }
        );
        this.setAgentState({
          agentState: fromCallCenterVoice.AgentStates.CONNECTING_INACTIVE,
          agentStateData: inactiveActivity,
        }, 'subscribeToNoMicError()');
      }, 1000);
    });
  }

  private subscribeToInactiveNotAvailable() {
    this.sharedService.inactiveStatusNotAvailableToast$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(res => {
      this.showInactiveNotAvailableTopToast = true;

      this.callWhileInactiveMessages = [
        {
          message: 'Select a different status to make an outbound call',
          type: TopToastComponent.ERROR,
        },
      ];
    });
  }

  /**
   *
   * @private
   * @param {*} event
   * @memberof CallCenterVoiceComponent
   */
  public topToastCallWhileInactive(event: any): void {
    this.showInactiveNotAvailableTopToast = false;
    this.callWhileInactiveMessages = [];
  }
  private listenToCallBackRescinded() {
    this.twilioEventHandlerService.workerClientCallbackReservationRescinded$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(res => {
      this.bugsnagService.leaveBreadcrumb('WorkerClientCallbackReservationRescinded', {
        callSid: this.currentReservation?.task?.attributes?.call_sid,
        callNumber: this.currentReservation?.task?.attributes?.caller,
        destinationNumber: this.currentReservation?.task?.attributes?.to,
        callStatus: this.currentReservation?.reservationStatus,
      });
      this.callBackRescindedOnAccept = true;
      this.showCallBackPopup = false;
      this.store.dispatch(
        new fromCallCenterVoiceAction.ToggleInboundCallbackPopUp(false)
      );
      if (this.audio) {
        this.audio.pause();
        this.audio = null;
      }
      this.topToastMessages = [
        {
          message: 'The call has been accepted by another agent.',
          type: TopToastComponent.BIG_NORMAL,
        },
      ];
      this.showTopToast = true;
      console.log(
        '[Call Center Voice][listenToCallBackRescinded] - Worker pending reservation was rescinded => ',
      );
    });
  }
  private subscribeToCallFromInactiveActivity() {
    this.sharedService.agentStatePriorCall$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((res: any) => {
      if (res) {
        this.agentStateInactive = res.status;
        this.inactiveActivitySid = res.activity;
      }
    });
  }
  /**
   * This function is used, to retrieve fresh worker attributes before to call the
   * worker attribute update, this is to prevent missing attributes;
   * @private
   * @param {*} workerId - worker id from the worker we want to get the attributes.
   * @param {*} attributesToUpdte - attribute to add/update
   * @param {*} cb - callback function
   * @memberof HeaderComponent
   */
  public getFreshWorkerForUpdate(workerId, attributesToUpdte, cb) {
    this.callQueuesWorkerProvider.getOne({ id: workerId }).subscribe(
      res => {
        if (res) {
          let freshAttributes = res.body.attributes;
          let updatedAttributes = { ...freshAttributes, ...attributesToUpdte };
          let props: any = {
            Attributes: updatedAttributes,
          };
          UtilsHelper.workerUpdateSequential(this.worker, props, cb);
        }
      },
      error => {
        if (error) {
          console.log(error);
        }
      }
    );
  }

  /**
   * Use this function to change the worker avalability and avoid problems with IB and OB reservations
   *
   * @private
   * @param {*} availableStatus
   * @memberof CallCenterVoiceComponent
   */
  public changeWorkerAvailability(availableStatus, callBackInProgress?) {
    this.bugsnagService.leaveBreadcrumb('ChangeWorkerAvailability', {
      workerSid: this.worker?.sid,
      workerName: this.worker?.attributes?.name,
      workerActivity: this.worker?.activityName,
    });
    let workerVoiceChannel = this.workerChannels.find(
      (channel: TwilioWorkerChannel) => {
        return channel.taskChannelUniqueName === 'voice';
      }
    );
    let workerCallbackChannel = this.workerChannels.find(
      (channel: TwilioWorkerChannel) => {
        return channel.taskChannelUniqueName === 'callback';
      }
    );
    let workerVideoCallChannel = this.workerChannels.find(
      (channel: TwilioWorkerChannel) => {
        return channel.taskChannelUniqueName === 'video';

      }
    );
    // Change video call channel availability to FALSE
    if (!availableStatus && callBackInProgress) {
      let workerSid: string = this.worker.sid;
      if (workerVideoCallChannel) {
        let workerVideoCallChannelSid: string = workerVideoCallChannel.sid;
        this.twilioWorkerChannelsProvider
        .update({
          workerSid,
          workerchannelSid: workerVideoCallChannelSid,
          body: {
            available: availableStatus,
          },
        })
        .subscribe(
        res => {
          // Log successful channel change
          this.logWorkerChannelChange(workerVideoCallChannel, 'video_channel_updated', {
            callBackInProgress,
            requestedAvailability: availableStatus,
            newAvailability: availableStatus
          });
          
          console.log('[Call Center Voice][changeWorkerAvailability] - workerVideoCallChannel - Change video call worker channel availability to', availableStatus);
        },
        error => {
          this.bugsnagService.notify(error);
          
          // Log failed channel change
          this.logWorkerChannelChange(workerVideoCallChannel, 'video_channel_update_failed', {
            callBackInProgress,
            requestedAvailability: availableStatus,
            error: error.message
          });
          
          console.log('[Call Center Voice][changeWorkerAvailability] - workerVideoCallChannel - Error changing worker availability');
        });
      }
    }
    if (workerVoiceChannel || workerCallbackChannel) {
      let workerSid: string = this.worker.sid;
      if (workerVoiceChannel) {
        let workerVoiceChannelSid: string = workerVoiceChannel.sid;
        this.twilioWorkerChannelsProvider
          .update({
            workerSid,
            workerchannelSid: workerVoiceChannelSid,
            body: {
              available: availableStatus,
            },
          })
          .subscribe(
          res => {
            // Log successful channel change
            this.logWorkerChannelChange(workerVoiceChannel, 'voice_channel_updated', {
              requestedAvailability: availableStatus,
              newAvailability: availableStatus
            });
            
            this.cpLoggingService.registerEvent(
              'agent.availability.changed',
              'success',
              {
                availableStatus
              }
            );

            console.log('[Call Center Voice][changeWorkerAvailability] - workerVoiceChannel - Change voice worker availability to', availableStatus);
          },
          error => {
            this.bugsnagService.notify(error);
            
            // Log failed channel change
            this.logWorkerChannelChange(workerVoiceChannel, 'voice_channel_update_failed', {
              requestedAvailability: availableStatus,
              error: error.message
            });
            
            this.cpLoggingService.registerEvent(
              'agent.availability.changed',
              'failure',
              {
                availableStatus
              }
            );

            console.log('[Call Center Voice][changeWorkerAvailability] - workerVoiceChannel - Error changing worker availability');
          });
      }
      //Callback channel availability
      if (workerCallbackChannel) {
        let workerCallbackChannelSid: string = workerCallbackChannel.sid;
        this.twilioWorkerChannelsProvider
          .update({
            workerSid,
            workerchannelSid: workerCallbackChannelSid,
            body: {
              available: availableStatus,
            },
          })
          .subscribe(
          res => {
            // Log successful channel change
            this.logWorkerChannelChange(workerCallbackChannel, 'callback_channel_updated', {
              requestedAvailability: availableStatus,
              newAvailability: availableStatus
            });
            
            console.log('[Call Center Voice][changeWorkerAvailability] - workerCallbackChannel - Change callback worker availability to', availableStatus);
          },
          error => {
            this.bugsnagService.notify(error);
            
            // Log failed channel change
            this.logWorkerChannelChange(workerCallbackChannel, 'callback_channel_update_failed', {
              requestedAvailability: availableStatus,
              error: error.message
            });
            
            console.log('[Call Center Voice][changeWorkerAvailability] - workerCallbackChannel - Error changing worker availability');
          });
      }
    }
  }
   /* Subscribe to the twilioInitializationError service in order to display the error toast.
   *
   * @private
   * @memberof CallCenterVoiceComponent
   */
  private listenToInitializationErrors() {
    this.sharedService.twilioInitializationError$.pipe(takeUntil(this.ngUnsubscribe)).subscribe((res:any) => {
      this.checkNetworkConnection();
      this.errorWithWorker = true;
      this.errorWithWorkerMessage = res.errorMessage;
      this.showRefeshForWorker = res.ableToRefresh;
      this.cd.markForCheck();
    });
  }

  /**
   * Function called from app-v2 component at the moment of closing/refresh the webapp
   * It sets the agent state to DISCONNECTING when the agent is in cooldown status
   */
  stopCoolDownProcess() {
    let offlineActivity = this.workspaceActivities.find(activity => activity.friendlyName === OFFLINE_ACTIVITY_FRIENDLY_NAME);
    this.setAgentState({
      agentState: fromCallCenterVoice.AgentStates.DISCONNECTING,
      agentStateData: offlineActivity,
    }, 'stopCoolDownProcess()');
  }
  /**
   * This function is going to help us to reject/clear pending reservations
   * when a browser refresh occurs
   *
   * @memberof CallCenterVoiceComponent
   */
  clearReservations() {
    let rejectedActivity: any = this.getWorkerActivityByFriendlyName(
      REJECTED_ACTIVITY_FRIENDLY_NAME
    );
    if (this.currentReservation) {
      this.currentReservation.reject(
        rejectedActivity.sid,
        (error, reservation) => {
          if (error) {
            console.error('[Call Center Voice]: Current reservation is already rejected: ', error);
          }
          this.setAgentState({
            agentState: fromCallCenterVoice.AgentStates.INACTIVE,
            agentStateData: rejectedActivity.sid,
          }, 'clearReservations()');
          this.setCallState({
            callState: fromCallCenterVoice.CallStates.IDLE,
          });
        }
      );
    }
  }
  /**
   * Function to display the chrome alert for all connection methods
   *
   * @private
   * @param {*} taskAttributes
   * @memberof CallCenterVoiceComponent
   */
  private showCallBackChromeAlert(taskAttributes) {
    if (!this.isLocationLogin && taskAttributes && taskAttributes.taskchannel === 'Callback') {
      const displayName = 'Callback';
      let callerNumber = this.telephoneNumberPipe.transform(
        taskAttributes.caller_id,
        PHONE_FORMATS.DIGITS_AND_DASHES_WITH_PARENTHESIS
      );
      let notificationBody;
      if (!this.hideCallerTypeInbound) {
        notificationBody = `${taskAttributes.caller_name ||
          taskAttributes.customer_name ||
          callerNumber} - ${taskAttributes.disposition} - ${
          taskAttributes.ad_name
        }`;
      } else {
        notificationBody = `${taskAttributes.caller_name ||
          taskAttributes.customer_name ||
          callerNumber} - ${taskAttributes.ad_name}`;
      }
      ChromeNotifications.notify(displayName, notificationBody);
    }
  }
  private displayChromeAlertForVideo(taskAttributes) {
    const displayName: string = 'Video Call';
    let notificationBody;
    let callerNumber =  this.telephoneNumberPipe.transform(
      taskAttributes.caller_id,
      PHONE_FORMATS.DIGITS_AND_DASHES
    );
    let customerName: string;
    if (taskAttributes.customer_name === 'unknown' && (!taskAttributes.first_name && !taskAttributes.last_name)) {
      customerName = 'Unknown';
    } else if (!taskAttributes.first_name && !taskAttributes.last_name && taskAttributes.customer_name !== 'unknown') {
      customerName = taskAttributes.customer_name;
    } else {
      customerName = taskAttributes.first_name + ' ' + taskAttributes.last_name;
    }
    taskAttributes.customer_name === 'unknown' ? 'Unknown' : taskAttributes.customer_name;
    if (customerName === 'Unknown' && !callerNumber && taskAttributes.disposition === 'Unknown') {
      notificationBody = 'Unknown';
    } else if (customerName === 'Unknown' && callerNumber) {
      notificationBody = `${ callerNumber } - ${taskAttributes.disposition}`;
    } else {
      notificationBody = `${customerName } - ${taskAttributes.disposition}`;
    }
    ChromeNotifications.notify(displayName, notificationBody);
  }

   /**
   * Function to check internet connection
   *
   * @private
   * @memberof CallCenterVoiceComponent
   */
  private async checkNetworkConnection() {
    this.isCheckingNetworkConnection = true;
    const status = await this.internetConnectivityService.checkNetworkStatus();
    return status;
  }

  /**
   * Retry 5 times to check Internet connection
   *
   * @private
   * @memberof CallCenterVoiceComponent
   */
  public  retryToCheckNetworkConnection() {
    this.retryingForCheckNetworkConnection = true;
    let retryCount = 5;
    const retryFunction = async () => {
      if (retryCount > 0) {
        retryCount--;
        const status = await this.checkNetworkConnection();
        if (!status) {
          setTimeout(() => {
            retryFunction();
          }, 500);
        } else {
          retryCount = 0;
          this.retryingForCheckNetworkConnection = false;
          this.cd.detectChanges();
        }
      }
      if (retryCount === 0) {
        this.retryingForCheckNetworkConnection = false;
        this.cd.detectChanges();
      }
    };
    retryFunction();
  }

  /**
   * Function to select a map of locations from the store.  The information
   * can then be referenced in this component under the `locationsMap` property.
   * This way, given a location id, we can get the full location details
   * from the `locationsMap` property.
   */
  watchLocationsMap() {

    this.locationsMap$ = this.store.pipe(select(selectLocationsMap))
    .pipe(takeUntil(this.ngUnsubscribe)).subscribe({
      next: (locationsMap: any) => {
        this.locationsMap = locationsMap;
      },
      error: (err: Error) => console.error('ERROR: ' + err),
    });

  }
  private subscribeToCallBackStarted() {
    this.sharedService.callBackStarted$.pipe(takeUntil(this.ngUnsubscribe)).subscribe(res => {
      this.bugsnagService.leaveBreadcrumb('CallBackStarted', {
        callSid: this.currentReservation?.task?.attributes?.call_sid,
        callNumber: this.currentReservation?.task?.attributes?.caller,
        destinationNumber: this.currentReservation?.task?.attributes?.to,
        callStatus: this.currentReservation?.reservationStatus,
      });
      let placingCallbackProcessStarted = true;
      this.changeWorkerAvailability(false, placingCallbackProcessStarted);
    });
  }

  /**
   * Safely get the Twilio device instance
   * @returns the device or null if not initialized
   */
  private getTwilioDevice(): any {
    try {
      const device = this.twilioDeviceService.getDevice();
      if (!device) {
        console.log('[Call Center Voice][getTwilioDevice] - Twilio device not yet initialized');
      }
      return device;
    } catch (error) {
      console.error('[Call Center Voice][getTwilioDevice] - Error getting Twilio device', error);
      this.bugsnagService.notify(error);
      return null;
    }
  }

  /**
   * Safely disconnect all active connections
   */
  private async disconnectAllConnections(): Promise<void> {
    try {
      // Ensure AudioContext is resumed for clean disconnection
      this.twilioDeviceService.handleUserInteraction();
      await this.twilioDeviceService.disconnectAll();
      this.currentConnection = null;
    } catch (error) {
      console.error('[Call Center Voice][disconnectAllConnections] - Error disconnecting all connections', error);
      this.bugsnagService.notify(error);
    }
  }

  /*
   * Logs reservation state transitions with comprehensive context
   */
  private logReservationStateTransition(
    event: string, 
    reservation: any, 
    additionalData?: any
  ) {
    const stateData = {
      event,
      reservationId: reservation?.task?.sid,
      reservationStatus: reservation?.reservationStatus,
      workerId: reservation?.workerSid || this.worker?.sid,
      taskChannel: reservation?.task?.attributes?.taskchannel,
      currentAgentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
      currentCallState: fromCallCenterVoice.CallStates[this.currentCallState],
      workerActivity: this.worker?.activityName,
      timestamp: new Date().toISOString(),
      ...additionalData
    };

    this.bugsnagService.leaveBreadcrumb(`ReservationStateTransition_${event}`, stateData);
    
    this.cpLoggingService.registerEvent(
      `reservation.${event.toLowerCase()}`,
      'info',
      stateData
    );
  }

  /**
   * Detects potential race conditions between reservations
   */
  private logReservationTiming(reservation: any, event: string) {
    const timingData = {
      event,
      reservationId: reservation?.task?.sid,
      taskChannel: reservation?.task?.attributes?.taskchannel,
      hasCurrentReservation: !!this.currentReservation,
      currentReservationId: this.currentReservation?.task?.sid,
      currentReservationChannel: this.currentReservation?.task?.attributes?.taskchannel,
      timeBetweenReservations: this.currentReservation ? 
        Date.now() - new Date(this.currentReservation.dateCreated).getTime() : null,
      agentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
      timestamp: new Date().toISOString()
    };

    this.bugsnagService.leaveBreadcrumb('ReservationTiming', timingData);
    
    if (timingData.hasCurrentReservation && timingData.timeBetweenReservations < 5000) {
      // Potential race condition - log as warning
      this.bugsnagService.notify(new Error('Potential race condition detected'), {
        severity: 'warning',
        metadata: { raceCondition: timingData }
      });
      
      this.cpLoggingService.registerEvent(
        'reservation.race.condition.detected',
        'warning',
        timingData
      );
    }
  }

  /**
   * Validates state consistency and logs inconsistencies
   */
  private validateStateConsistency() {
    const stateData = {
      agentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
      callState: fromCallCenterVoice.CallStates[this.currentCallState],
      workerActivity: this.worker?.activityName,
      workerSid: this.worker?.sid,
      hasReservation: !!this.currentReservation,
      reservationStatus: this.currentReservation?.reservationStatus,
      voiceChannelAvailable: this.workerChannels?.find(ch => ch.taskChannelUniqueName === 'voice')?.available,
      timestamp: new Date().toISOString()
    };

    // Check for inconsistent states
    const inconsistencies = [];
    
    if (this.currentAgentStatus === fromCallCenterVoice.AgentStates.READY && 
        this.worker?.activityName !== READY_ACTIVITY_FRIENDLY_NAME) {
      inconsistencies.push('Agent state READY but worker activity is not Ready');
    }
    
    if (this.currentReservation && 
        this.currentAgentStatus === fromCallCenterVoice.AgentStates.READY) {
      inconsistencies.push('Has active reservation but agent state is READY');
    }
    
    if (this.currentCallState === fromCallCenterVoice.CallStates.IDLE &&
        this.currentReservation?.reservationStatus === 'accepted') {
      inconsistencies.push('Call state IDLE but has accepted reservation');
    }

    if (inconsistencies.length > 0) {
      this.bugsnagService.notify(new Error('State inconsistency detected'), {
        severity: 'warning',
        metadata: {
          stateValidation: {
            ...stateData,
            inconsistencies
          }
        }
      });
      
      this.cpLoggingService.registerEvent(
        'state.inconsistency.detected',
        'warning',
        {
          ...stateData,
          inconsistencies
        }
      );
    }

    this.bugsnagService.leaveBreadcrumb('StateValidation', {
      ...stateData,
      inconsistencies
    });
  }

  /**
   * Logs worker channel availability changes with comprehensive context
   */
  private logWorkerChannelChange(channel: TwilioWorkerChannel, action: string, additionalData?: any) {
    this.bugsnagService.leaveBreadcrumb('WorkerChannelChange', {
      action,
      channelSid: channel.sid,
      taskChannel: channel.taskChannelUniqueName,
      available: channel.available,
      availableCapacityPercentage: channel.availableCapacityPercentage,
      workerId: this.worker?.sid,
      timestamp: new Date().toISOString(),
      ...additionalData
    });
    
    this.cpLoggingService.registerEvent(
      'worker.channel.availability.changed',
      'info',
      {
        action,
        taskChannel: channel.taskChannelUniqueName,
        available: channel.available,
        workerId: this.worker?.sid,
        ...additionalData
      }
    );
  }

  /**
   * Performance timing utility for measuring critical operations
   */
  private performanceTimers: Map<string, number> = new Map();

  /**
   * Start performance timing for an operation
   */
  private startPerformanceTimer(operationId: string, operationType: string, metadata?: any) {
    const startTime = performance.now();
    this.performanceTimers.set(operationId, startTime);
    
    try {
      this.bugsnagService.leaveBreadcrumb('PerformanceTimerStart', {
        operationId,
        operationType,
        startTime,
        timestamp: new Date().toISOString(),
        ...metadata
      });
    } catch (error) {
      console.warn('[Performance Timer] Failed to log start breadcrumb:', error);
    }
  }

  /**
   * End performance timing and log results
   */
  private endPerformanceTimer(operationId: string, operationType: string, metadata?: any): number {
    const startTime = this.performanceTimers.get(operationId);
    if (!startTime) {
      console.warn(`[Performance Timer] No start time found for operation: ${operationId}`);
      return 0;
    }

    const endTime = performance.now();
    const duration = endTime - startTime;
    this.performanceTimers.delete(operationId);

    const timingData = {
      operationId,
      operationType,
      duration,
      startTime,
      endTime,
      timestamp: new Date().toISOString(),
      ...metadata
    };

    try {
      this.bugsnagService.leaveBreadcrumb('PerformanceTimerEnd', timingData);
    } catch (error) {
      console.warn('[Performance Timer] Failed to log end breadcrumb:', error);
    }
    
    // Log as warning if operation takes too long
    const logLevel = this.getPerformanceLogLevel(operationType, duration);
    try {
      this.cpLoggingService.registerEvent(
        `performance.${operationType.toLowerCase().replace(/\s+/g, '.')}.completed`,
        logLevel,
        timingData
      );
    } catch (error) {
      console.warn('[Performance Timer] Failed to register event:', error);
    }

    // Log slow operations as warnings
    if (logLevel === 'warning' || logLevel === 'error') {
      try {
        this.bugsnagService.notify(new Error(`Slow ${operationType} operation detected`), {
          severity: logLevel as 'warning' | 'error',
          metadata: { performanceTiming: timingData }
        });
      } catch (error) {
        console.warn('[Performance Timer] Failed to notify slow operation:', error);
      }
    }

    return duration;
  }

  /**
   * Determine log level based on operation type and duration
   */
  private getPerformanceLogLevel(operationType: string, duration: number): 'info' | 'warning' | 'error' {
    const thresholds = {
      'Worker Activity Update': { warning: 2000, error: 5000 },
      'Call Pop Presentation': { warning: 1000, error: 3000 },
      'Lambda Response': { warning: 3000, error: 8000 },
      'Reservation Processing': { warning: 1500, error: 4000 },
      'State Transition': { warning: 500, error: 2000 }
    };

    const threshold = thresholds[operationType] || { warning: 2000, error: 5000 };
    
    if (duration >= threshold.error) return 'error';
    if (duration >= threshold.warning) return 'warning';
    return 'info';
  }

  /**
   * Enhanced reservation-to-popup presentation timing
   */
  private trackCallPopPresentationTiming(reservation: any, stage: 'start' | 'popup_requested' | 'popup_visible') {
    const reservationId = reservation?.task?.sid;
    if (!reservationId) return;

    const timerId = `call_pop_${reservationId}`;
    
    switch (stage) {
      case 'start':
        this.startPerformanceTimer(timerId, 'Call Pop Presentation', {
          reservationId,
          taskChannel: reservation?.task?.attributes?.taskchannel,
          reservationStatus: reservation?.reservationStatus
        });
        break;
        
      case 'popup_requested':
        const duration = this.endPerformanceTimer(timerId, 'Call Pop Presentation', {
          reservationId,
          stage: 'popup_requested',
          popupToggled: true
        });
        
        // Start timing for popup visibility
        this.startPerformanceTimer(`${timerId}_visibility`, 'Call Pop Visibility', {
          reservationId,
          previousDuration: duration
        });
        break;
        
      case 'popup_visible':
        this.endPerformanceTimer(`${timerId}_visibility`, 'Call Pop Visibility', {
          reservationId,
          stage: 'popup_visible'
        });
        break;
    }
  }

  /**
   * Enhanced worker activity update timing
   */
  private trackWorkerActivityUpdateTiming(
    stage: 'start' | 'end', 
    activitySid: string, 
    activityName: string, 
    reservationId?: string,
    error?: any
  ) {
    const timerId = `worker_activity_${reservationId || 'manual'}_${Date.now()}`;
    
    if (stage === 'start') {
      this.startPerformanceTimer(timerId, 'Worker Activity Update', {
        activitySid,
        activityName,
        reservationId,
        workerId: this.worker?.sid,
        currentActivity: this.worker?.activityName
      });
      
      // Store timer ID for later retrieval
      if (this.performanceTimers) {
        this.performanceTimers.set(`current_worker_update`, performance.now());
        this.performanceTimers.set(`current_worker_update_id`, timerId as any);
      }
    } else {
      // Get the stored timer ID
      if (this.performanceTimers) {
        const storedTimerId = this.performanceTimers.get(`current_worker_update_id`) as any;
        if (storedTimerId) {
          this.endPerformanceTimer(storedTimerId, 'Worker Activity Update', {
            activitySid,
            activityName,
            reservationId,
            workerId: this.worker?.sid,
            success: !error,
            error: error?.message
          });
          
          this.performanceTimers.delete(`current_worker_update`);
          this.performanceTimers.delete(`current_worker_update_id`);
        }
      }
    }
  }



  /**
   * Initialize periodic state validation and monitoring
   */
  private initializePeriodicMonitoring() {
    // Run state validation every 30 seconds
    this.stateValidationInterval = setInterval(() => {
      this.performPeriodicStateValidation();
    }, 30000);

    // Log initialization
    if (this.bugsnagService) {
      this.bugsnagService.leaveBreadcrumb('PeriodicMonitoringInitialized', {
        interval: 30000,
        timestamp: new Date().toISOString()
      });
    }

    if (this.cpLoggingService) {
      this.cpLoggingService.registerEvent(
        'monitoring.periodic.initialized',
        'info',
        {
          interval: 30000,
          features: ['state_validation', 'performance_baseline', 'inconsistency_alerts']
        }
      );
    }
  }

  /**
   * Perform comprehensive periodic state validation
   */
  private performPeriodicStateValidation() {
    const validationStartTime = performance.now();
    this.lastStateValidationTime = Date.now();

    try {
      // Validate current state consistency
      const stateInconsistencies = this.detectStateInconsistencies();
      const workerConnectionHealth = this.validateWorkerConnectionHealth();
      const reservationHealth = this.validateReservationHealth();
      const performanceMetrics = this.collectPerformanceMetrics();

      const validationResults = {
        timestamp: new Date().toISOString(),
        validationDuration: performance.now() - validationStartTime,
        stateInconsistencies,
        workerConnectionHealth,
        reservationHealth,
        performanceMetrics,
        consecutiveInconsistencies: this.consecutiveInconsistencies
      };

      // Log validation results
      if (this.bugsnagService) {
        this.bugsnagService.leaveBreadcrumb('PeriodicStateValidation', validationResults);
      }

      // Handle inconsistencies
      if (stateInconsistencies.length > 0 || !workerConnectionHealth.healthy || !reservationHealth.healthy) {
        this.consecutiveInconsistencies++;
        this.handleStateInconsistencies(validationResults);
      } else {
        this.consecutiveInconsistencies = 0;
      }

      // Update performance baselines
      this.updatePerformanceBaselines(performanceMetrics);

      // Log successful validation
      if (this.cpLoggingService) {
        this.cpLoggingService.registerEvent(
          'monitoring.periodic.validation.completed',
          stateInconsistencies.length > 0 ? 'warning' : 'info',
          validationResults
        );
      }

    } catch (error) {
      if (this.bugsnagService) {
        this.bugsnagService.notify(error, {
          severity: 'error',
          metadata: {
            periodicValidation: {
              error: error.message,
              validationDuration: performance.now() - validationStartTime
            }
          }
        });
      }

      if (this.cpLoggingService) {
        this.cpLoggingService.registerEvent(
          'monitoring.periodic.validation.failed',
          'error',
          {
            error: error.message,
            timestamp: new Date().toISOString()
          }
        );
      }
    }
  }

  /**
   * Detect state inconsistencies with enhanced logic
   */
  private detectStateInconsistencies(): any[] {
    const inconsistencies = [];
    const currentTime = Date.now();

    // Enhanced state consistency checks
    const checks = [
      {
        condition: this.currentAgentStatus === fromCallCenterVoice.AgentStates.READY && 
                  this.worker?.activityName !== READY_ACTIVITY_FRIENDLY_NAME,
        issue: 'Agent state READY but worker activity not Ready',
        severity: 'high'
      },
      {
        condition: this.currentReservation && 
                  this.currentAgentStatus === fromCallCenterVoice.AgentStates.READY,
        issue: 'Has active reservation but agent state is READY',
        severity: 'critical'
      },
      {
        condition: this.currentCallState === fromCallCenterVoice.CallStates.IDLE &&
                  this.currentReservation?.reservationStatus === 'accepted',
        issue: 'Call state IDLE but has accepted reservation',
        severity: 'high'
      },
      {
        condition: this.currentAgentStatus === fromCallCenterVoice.AgentStates.ON_INBOUND_CALL &&
                  !this.currentReservation,
        issue: 'Agent on inbound call but no current reservation',
        severity: 'critical'
      },
      {
        condition: this.currentReservation && 
                  (currentTime - new Date(this.currentReservation.dateCreated).getTime()) > 300000, // 5 minutes
        issue: 'Reservation older than 5 minutes still active',
        severity: 'medium'
      },
      {
        condition: this.internetConnectionLost && 
                  this.currentAgentStatus !== fromCallCenterVoice.AgentStates.OFFLINE,
        issue: 'Internet connection lost but agent not offline',
        severity: 'high'
      }
    ];

    checks.forEach((check, index) => {
      if (check.condition) {
        inconsistencies.push({
          checkId: index,
          issue: check.issue,
          severity: check.severity,
          detectedAt: new Date().toISOString(),
          currentState: {
            agentState: fromCallCenterVoice.AgentStates[this.currentAgentStatus],
            callState: fromCallCenterVoice.CallStates[this.currentCallState],
            workerActivity: this.worker?.activityName,
            hasReservation: !!this.currentReservation,
            reservationStatus: this.currentReservation?.reservationStatus,
            internetConnected: !this.internetConnectionLost
          }
        });
      }
    });

    return inconsistencies;
  }

  /**
   * Validate worker connection health
   */
  private validateWorkerConnectionHealth(): any {
    const health = {
      healthy: true,
      issues: [] as string[],
      metrics: {
        workerConnected: !!this.worker,
        workerClientConnected: !!this.workerClient,
        hasActivities: this.workspaceActivities && this.workspaceActivities.length > 0,
        hasChannels: this.workerChannels && this.workerChannels.length > 0,
        deviceConnected: !!this.twilioDevice
      }
    };

    if (!this.worker) {
      health.issues.push('No worker instance');
      health.healthy = false;
    }

    if (!this.workerClient) {
      health.issues.push('No worker client instance');
      health.healthy = false;
    }

    if (!this.workspaceActivities || this.workspaceActivities.length === 0) {
      health.issues.push('No workspace activities loaded');
      health.healthy = false;
    }

    if (!this.workerChannels || this.workerChannels.length === 0) {
      health.issues.push('No worker channels loaded');
      health.healthy = false;
    }

    if (!this.twilioDevice) {
      health.issues.push('No Twilio device instance');
      health.healthy = false;
    }

    return health;
  }

  /**
   * Validate reservation health
   */
  private validateReservationHealth(): any {
    const health = {
      healthy: true,
      issues: [] as string[],
      metrics: {
        hasReservation: !!this.currentReservation,
        reservationAge: this.currentReservation ? 
          Date.now() - new Date(this.currentReservation.dateCreated).getTime() : null,
        reservationStatus: this.currentReservation?.reservationStatus
      }
    };

    if (this.currentReservation) {
      const reservationAge = Date.now() - new Date(this.currentReservation.dateCreated).getTime();
      
      if (reservationAge > 600000) { // 10 minutes
        health.issues.push('Reservation older than 10 minutes');
        health.healthy = false;
      }

      if (!this.currentReservation.reservationStatus) {
        health.issues.push('Reservation missing status');
        health.healthy = false;
      }

      if (this.currentReservation.reservationStatus === 'timeout') {
        health.issues.push('Reservation has timed out');
        health.healthy = false;
      }
    }

    return health;
  }

  /**
   * Collect current performance metrics
   */
  private collectPerformanceMetrics(): any {
    const activeTimers = this.performanceTimers.size;
    const lastValidationAge = this.lastStateValidationTime ? 
      Date.now() - this.lastStateValidationTime : null;

    return {
      activePerformanceTimers: activeTimers,
      lastValidationAge,
      memoryUsage: (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
      } : null,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Handle state inconsistencies with automated alerts
   */
  private handleStateInconsistencies(validationResults: any) {
    const { stateInconsistencies, consecutiveInconsistencies } = validationResults;
    
    // Determine alert severity based on inconsistency count and consecutive occurrences
    let alertSeverity: 'warning' | 'error' = 'warning';
    if (consecutiveInconsistencies >= 3 || 
        stateInconsistencies.some(i => i.severity === 'critical')) {
      alertSeverity = 'error';
    }

    // Send alert
    this.bugsnagService.notify(new Error('State inconsistencies detected'), {
      severity: alertSeverity,
      metadata: {
        stateInconsistencies: validationResults,
        consecutiveCount: consecutiveInconsistencies,
        alertTriggered: true
      }
    });

    // Log inconsistency event
    this.cpLoggingService.registerEvent(
      'monitoring.state.inconsistency.alert',
      alertSeverity,
      {
        inconsistencyCount: stateInconsistencies.length,
        consecutiveCount: consecutiveInconsistencies,
        criticalIssues: stateInconsistencies.filter(i => i.severity === 'critical').length,
        validationResults
      }
    );

    // Attempt automatic recovery for certain issues
    this.attemptAutomaticRecovery(stateInconsistencies);
  }

  /**
   * Attempt automatic recovery for known issues
   */
  private attemptAutomaticRecovery(inconsistencies: any[]) {
    inconsistencies.forEach(inconsistency => {
      switch (inconsistency.checkId) {
        case 0: // Agent state READY but worker activity not Ready
          if (this.currentAgentStatus === fromCallCenterVoice.AgentStates.READY) {
            this.bugsnagService.leaveBreadcrumb('AutoRecoveryAttempt', {
              issue: inconsistency.issue,
              action: 'Attempting to sync worker activity to Ready'
            });
            
            const readyActivity = this.workspaceActivities?.find(
              activity => activity.friendlyName === READY_ACTIVITY_FRIENDLY_NAME
            );
            
            if (readyActivity) {
              this.setAgentState({
                agentState: fromCallCenterVoice.AgentStates.CONNECTING_READY,
                agentStateData: readyActivity,
              }, 'automaticRecovery');
            }
          }
          break;
          
        case 4: // Old reservation cleanup
          if (this.currentReservation) {
            const reservationAge = Date.now() - new Date(this.currentReservation.dateCreated).getTime();
            if (reservationAge > 600000) { // 10 minutes
              this.bugsnagService.leaveBreadcrumb('AutoRecoveryAttempt', {
                issue: inconsistency.issue,
                action: 'Clearing stale reservation'
              });
              
              this.currentReservation = null;
              this.setCallState({
                callState: fromCallCenterVoice.CallStates.IDLE,
              });
            }
          }
          break;
      }
    });
  }

  /**
   * Update performance baselines for trend analysis
   */
  private updatePerformanceBaselines(metrics: any) {
    const metricTypes = ['validation_duration', 'memory_usage', 'active_timers'];
    
    metricTypes.forEach(metricType => {
      if (!this.performanceBaselineData.has(metricType)) {
        this.performanceBaselineData.set(metricType, []);
      }
      
      const baseline = this.performanceBaselineData.get(metricType);
      let value: number;
      
      switch (metricType) {
        case 'validation_duration':
          value = metrics.lastValidationAge || 0;
          break;
        case 'memory_usage':
          value = metrics.memoryUsage?.usedJSHeapSize || 0;
          break;
        case 'active_timers':
          value = metrics.activePerformanceTimers || 0;
          break;
        default:
          return;
      }
      
      baseline.push(value);
      
      // Keep only last 100 measurements
      if (baseline.length > 100) {
        baseline.shift();
      }
    });

    // Analyze trends and alert on anomalies
    this.analyzePerformanceTrends();
  }

  /**
   * Analyze performance trends and detect anomalies
   */
  private analyzePerformanceTrends() {
    this.performanceBaselineData.forEach((values, metricType) => {
      if (values.length < 10) return; // Need at least 10 data points
      
      const recent = values.slice(-5); // Last 5 measurements
      const historical = values.slice(0, -5); // All but last 5
      
      const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const historicalAvg = historical.reduce((a, b) => a + b, 0) / historical.length;
      
      // Detect significant performance degradation (>50% increase)
      if (recentAvg > historicalAvg * 1.5 && historicalAvg > 0) {
        this.bugsnagService.notify(new Error('Performance degradation detected'), {
          severity: 'warning',
          metadata: {
            performanceTrend: {
              metricType,
              recentAverage: recentAvg,
              historicalAverage: historicalAvg,
              degradationPercent: ((recentAvg - historicalAvg) / historicalAvg) * 100,
              timestamp: new Date().toISOString()
            }
          }
        });
        
        this.cpLoggingService.registerEvent(
          'monitoring.performance.degradation',
          'warning',
          {
            metricType,
            recentAverage: recentAvg,
            historicalAverage: historicalAvg,
            degradationPercent: ((recentAvg - historicalAvg) / historicalAvg) * 100
          }
        );
      }
    });
  }

  /**
   * Cleanup periodic monitoring on component destruction
   */
  private cleanupPeriodicMonitoring() {
    if (this.stateValidationInterval) {
      clearInterval(this.stateValidationInterval);
      this.stateValidationInterval = null;
    }
    
    this.performanceBaselineData.clear();
    
    this.bugsnagService.leaveBreadcrumb('PeriodicMonitoringCleanup', {
      timestamp: new Date().toISOString()
    });
  }
}
