import { TestBed, ComponentFixture } from '@angular/core/testing';
import { CallCenterVoiceComponent } from './call-center-voice.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { provideMockStore, MockStore } from '@ngrx/store/testing';
import { Router } from '@angular/router';
import { ChangeDetectorRef } from '@angular/core';
import { AWSLambdaProvider } from 'common/services/aws-lambda.provider';
import { AWSCredentialsProvider } from 'common/services/aws-credentials.provider';
import { BugsnagService } from 'app/bugsnag/bugsnag.service';
import { TwilioWorkerChannelsProvider } from 'common/twilio/twilio-worker-channels.provider';
import { SharedService } from '../../app-v2/system-configuration/call-routes/models';
import { TwilioEventHandlerService } from '../twilio-event-handlers.service';
import { SystemVariablesProvider } from 'common/system-variables';
import { TrackingNumberProvider } from 'common/tracking-number';
import { TwilioProvider } from 'common/twilio';
import { TelephoneFormatPipe } from 'common/pipes/telephone-format.pipe';
import { CallQueuesWorkerProvider } from 'common/callqueues-worker';
import { InternetConnectivityService } from 'common/check-internet-connectivity/internet-connectivity.service';
import { VideoCommunicationsService } from '../../store/services/video-communications.service';
import { DeviceService } from '../../store/services/device.service';
import { CpLoggingService } from '@callpotential/cp-logging-service';
import { CallProvider } from '../../../common/call-data';
import { CoreApi } from 'common/swagger-providers/core-api.provider';
import { SessionProvider } from 'common/session/session.provider';
import { MultiCCApi } from 'common/swagger-providers/mcc-api.provider';
import { CallAPI } from 'common/swagger-providers/call-api.provider';
import { of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('CallCenterVoiceComponent - Enhanced Logging Integration Tests', () => {
  let component: CallCenterVoiceComponent;
  let fixture: ComponentFixture<CallCenterVoiceComponent>;
  let mockStore: MockStore;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockBugsnagService: jasmine.SpyObj<BugsnagService>;
  let mockCpLoggingService: jasmine.SpyObj<CpLoggingService>;
  let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

  const initialState = {
    session: {
      loaded: true,
      session: { account_id: 'test-account' }
    },
    systemConfiguration: {
      loaded: true,
      systemConfiguration: {
        twilioDefaultLambdaEnvironment: 'test'
      }
    },
    callCenterVoice: { 
      loaded: true,
      agentState: 1,
      callState: 1 
    },
    callQueues: { loaded: true },
    deviceClient: { loaded: true },
    trackingNumbers: { loaded: true },
    voIPExtensions: { loaded: true },
    workerChannels: { loaded: true },
    workerClient: { loaded: true },
    workspaceActivities: { loaded: true },
    workspaceTasks: { loaded: true },
    workspaceWorkers: { loaded: true },
    callCenterConfiguration: { loaded: true },
    callCenterChat: { loaded: true },
    locationConfiguration: { loaded: true },
    allLocations: { loaded: true }
  };

  beforeEach(async () => {
    // Create comprehensive mocks for all dependencies
    mockRouter = jasmine.createSpyObj('Router', ['navigate']);
    mockBugsnagService = jasmine.createSpyObj('BugsnagService', ['leaveBreadcrumb', 'notify']);
    mockCpLoggingService = jasmine.createSpyObj('CpLoggingService', ['registerEvent']);
    mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck', 'detectChanges']);
    
    const mockAWSLambdaProvider = jasmine.createSpyObj('AWSLambdaProvider', ['invokeOutboundCall', 'invokeTransferCall']);
    const mockAWSCredentialsProvider = jasmine.createSpyObj('AWSCredentialsProvider', ['getOne']);
    const mockTwilioWorkerChannelsProvider = jasmine.createSpyObj('TwilioWorkerChannelsProvider', ['getAll']);
    const mockSharedService = jasmine.createSpyObj('SharedService', ['get']);
    const mockTwilioEventHandlerService = jasmine.createSpyObj('TwilioEventHandlerService', ['handleEvent']);
    const mockSystemVariablesProvider = jasmine.createSpyObj('SystemVariablesProvider', ['getAll']);
    const mockTrackingNumberProvider = jasmine.createSpyObj('TrackingNumberProvider', ['getAll']);
    const mockTwilioProvider = jasmine.createSpyObj('TwilioProvider', ['getWorker']);
    const mockTelephoneFormatPipe = jasmine.createSpyObj('TelephoneFormatPipe', ['transform']);
    const mockCallQueuesWorkerProvider = jasmine.createSpyObj('CallQueuesWorkerProvider', ['getAll']);
    const mockInternetConnectivityService = jasmine.createSpyObj('InternetConnectivityService', ['getNetworkStatus']);
    const mockVideoCommunicationsService = jasmine.createSpyObj('VideoCommunicationsService', ['startCall']);
         const mockDeviceService = jasmine.createSpyObj('DeviceService', ['getDevices']);
     const mockCallProvider = jasmine.createSpyObj('CallProvider', ['makeCall']);
     const mockCoreApi = jasmine.createSpyObj('CoreApi', ['getOne', 'getAll']);
     const mockSessionProvider = jasmine.createSpyObj('SessionProvider', ['getSession', 'getOne']);
     const mockMultiCCApi = jasmine.createSpyObj('MultiCCApi', ['getOne', 'getAll']);
     const mockCallAPI = jasmine.createSpyObj('CallAPI', ['getOne', 'getAll', 'createCall', 'updateCall']);

          // Setup return values for observables
     mockAWSCredentialsProvider.getOne.and.returnValue(of({
       body: {
         Credentials: {
           AccessKeyId: 'test-key',
           SecretAccessKey: 'test-secret',
           SessionToken: 'test-token'
         }
       }
     }));

     mockSessionProvider.getOne.and.returnValue(of({
       body: { 
         account_id: 'test-account', 
         session_token: 'test-token',
         user: {
           authtoken: 'mock-auth-token',
           sid: 'mock-sid'
         }
       }
     }));

    await TestBed.configureTestingModule({
      declarations: [CallCenterVoiceComponent],
      imports: [HttpClientTestingModule],
      providers: [
        provideMockStore({ initialState }),
        { provide: Router, useValue: mockRouter },
        { provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
        { provide: AWSLambdaProvider, useValue: mockAWSLambdaProvider },
        { provide: AWSCredentialsProvider, useValue: mockAWSCredentialsProvider },
        { provide: BugsnagService, useValue: mockBugsnagService },
        { provide: TwilioWorkerChannelsProvider, useValue: mockTwilioWorkerChannelsProvider },
        { provide: SharedService, useValue: mockSharedService },
        { provide: TwilioEventHandlerService, useValue: mockTwilioEventHandlerService },
        { provide: SystemVariablesProvider, useValue: mockSystemVariablesProvider },
        { provide: TrackingNumberProvider, useValue: mockTrackingNumberProvider },
        { provide: TwilioProvider, useValue: mockTwilioProvider },
        { provide: TelephoneFormatPipe, useValue: mockTelephoneFormatPipe },
        { provide: CallQueuesWorkerProvider, useValue: mockCallQueuesWorkerProvider },
        { provide: InternetConnectivityService, useValue: mockInternetConnectivityService },
                 { provide: VideoCommunicationsService, useValue: mockVideoCommunicationsService },
         { provide: DeviceService, useValue: mockDeviceService },
         { provide: CpLoggingService, useValue: mockCpLoggingService },
         { provide: CallProvider, useValue: mockCallProvider },
         { provide: CoreApi, useValue: mockCoreApi },
         { provide: SessionProvider, useValue: mockSessionProvider },
         { provide: MultiCCApi, useValue: mockMultiCCApi },
         { provide: CallAPI, useValue: mockCallAPI }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(CallCenterVoiceComponent);
    component = fixture.componentInstance;
    mockStore = TestBed.inject(MockStore);
    
    // Initialize component properties that are accessed by the enhanced logging methods
    (component as any).performanceTimers = new Map();
    
    // Prevent TwilioProvider initialization issues
    spyOn(sessionStorage, 'getItem').and.returnValue(JSON.stringify({
      user: {
        authtoken: 'mock-auth-token',
        sid: 'mock-sid'
      }
    }));
  });

  afterEach(() => {
    // Clean up component to prevent cleanup errors
    if (fixture) {
      try {
        fixture.destroy();
      } catch (error) {
        console.warn('Component cleanup error (expected):', error);
      }
    }
  });

  describe('Performance Timing Utilities Integration', () => {
    describe('when startPerformanceTimer and endPerformanceTimer are called', () => {
      it('then should execute actual component methods without throwing errors', () => {
        // Given - Component has actual performance timing methods
        const operationId = 'test-operation';
        const testStart = performance.now();
        
        // When - Call the actual component methods
        (component as any).startPerformanceTimer(operationId, 'Test Operation', { test: true });
        
        // Simulate some work
        for (let i = 0; i < 1000; i++) {
          Math.random();
        }
        
        const duration = (component as any).endPerformanceTimer(operationId, 'Test Operation', { test: true });
        const totalTime = performance.now() - testStart;

        // Then - Should complete successfully and log appropriately
        expect(duration).toBeGreaterThanOrEqual(0); // Should not crash, even if timer fails
        expect(totalTime).toBeLessThan(50); // Should be fast (<50ms including setup)
        expect(mockBugsnagService.leaveBreadcrumb).toHaveBeenCalled();
      });
    });

    describe('when logging services fail during performance timing', () => {
      it('then should continue operations without crashing', () => {
        // Given - Mock services that throw errors
        mockBugsnagService.leaveBreadcrumb.and.throwError('Service unavailable');
        mockCpLoggingService.registerEvent.and.throwError('Service unavailable');

        // When & Then - Should not throw errors
        expect(() => {
          const operationId = 'failing-operation';
          (component as any).startPerformanceTimer(operationId, 'Test Operation');
          (component as any).endPerformanceTimer(operationId, 'Test Operation');
        }).not.toThrow();
      });
    });

    describe('when performance timers map is used extensively', () => {
      it('then should not cause memory leaks', () => {
        // Given - Get reference to the actual performance timers map
        const performanceTimers = (component as any).performanceTimers;
        const initialSize = performanceTimers.size;

        // When - Use many performance timers
        for (let i = 0; i < 100; i++) {
          const operationId = `operation-${i}`;
          (component as any).startPerformanceTimer(operationId, 'Test');
          (component as any).endPerformanceTimer(operationId, 'Test');
        }

        // Then - Map should be cleaned up (no memory leak)
        expect(performanceTimers.size).toBe(initialSize);
      });
    });
  });

  describe('Worker Activity Update Timing Integration', () => {
    describe('when trackWorkerActivityUpdateTiming is called', () => {
      it('then should execute actual component method without errors', () => {
        // Given
        const activitySid = 'activity-123';
        const activityName = 'On-call';
        const reservationId = 'reservation-456';

        // When - Call the actual component method
        (component as any).trackWorkerActivityUpdateTiming('start', activitySid, activityName, reservationId);
        (component as any).trackWorkerActivityUpdateTiming('end', activitySid, activityName, reservationId, null);

        // Then - Should have executed without throwing
        expect(mockBugsnagService.leaveBreadcrumb).toHaveBeenCalled();
      });
    });

    describe('when trackWorkerActivityUpdateTiming fails', () => {
      it('then should not crash worker activity operations', () => {
        // Given
        mockBugsnagService.leaveBreadcrumb.and.throwError('Logging failed');

        // When & Then - Should not throw
        expect(() => {
          (component as any).trackWorkerActivityUpdateTiming('start', 'test', 'Test', 'res-123');
          (component as any).trackWorkerActivityUpdateTiming('end', 'test', 'Test', 'res-123');
        }).not.toThrow();
      });
    });
  });

  describe('Enhanced Logging Non-Interference Verification', () => {
    describe('when logging services are completely unavailable', () => {
      it('then component should continue functioning normally', () => {
        // Given - All logging services fail
        mockBugsnagService.leaveBreadcrumb.and.throwError('Service down');
        mockBugsnagService.notify.and.throwError('Service down');
        mockCpLoggingService.registerEvent.and.throwError('Service down');

        // When & Then - Component operations that use enhanced logging should not crash
        expect(() => {
          // Performance timing operations
          (component as any).startPerformanceTimer('test', 'Test');
          (component as any).endPerformanceTimer('test', 'Test');
          
          // Worker activity timing
          (component as any).trackWorkerActivityUpdateTiming('start', 'test', 'Test', 'res');
          (component as any).trackWorkerActivityUpdateTiming('end', 'test', 'Test', 'res');
          
        }).not.toThrow();

        // Then - Component should still be functional
        expect(component).toBeDefined();
      });
    });
  });

  describe('Real Component Integration', () => {
    describe('when component is initialized', () => {
      it('then enhanced logging methods should be available', () => {
        // Then - Component should have the enhanced logging methods that were actually implemented
        expect(typeof (component as any).startPerformanceTimer).toBe('function');
        expect(typeof (component as any).endPerformanceTimer).toBe('function');
        expect(typeof (component as any).trackWorkerActivityUpdateTiming).toBe('function');
        expect((component as any).performanceTimers).toBeInstanceOf(Map);
      });
    });

    describe('when component methods are called', () => {
      it('then original functionality should be preserved', () => {
        // Given - Mock the necessary dependencies
        (component as any).worker = { sid: 'worker-123', activityName: 'Ready' };
        (component as any).currentAgentStatus = 1;

        // When - Use the enhanced logging methods
        (component as any).startPerformanceTimer('test', 'Test');
        (component as any).endPerformanceTimer('test', 'Test');
        
        // Then - Component state should be maintained (logging doesn't interfere)
        expect((component as any).worker.sid).toBe('worker-123');
        expect((component as any).currentAgentStatus).toBe(1);
      });
    });
  });
}); 