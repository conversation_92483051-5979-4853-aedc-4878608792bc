import { Injectable } from '@angular/core';

import { Store } from '@ngrx/store';
import * as fromRoot from 'app/store';
import * as fromSession from 'app/store/reducers/session.reducer';
import * as fromSystemConfiguration from 'app/store/reducers/system-configuration.reducer';
import * as fromSystemConfigurationActions from 'app/store/actions/system-configuration.action';

import * as AWS from 'aws-sdk/global';
import Lambda from 'aws-sdk/clients/lambda';

import { AWSCredentialsProvider } from 'common/services';
import { BugsnagService } from 'app/bugsnag/bugsnag.service';
import {
  AWS_API_VERSION,
  AWS_REGION,
  REQUEST_PARAMS,
  TASK_ROUTER_TOKEN_LAMBDA_FUNCTION_NAME_POSTFIX,
  OUTBOUND_CALL_LAMBDA_FUNCTION_NAME_POSTFIX,
  CONFERENCE_EVENT_LAMBDA_FUNCTION_NAME_POSTFIX,
} from './aws-constants';

import {
  IamCredentialsWrapper,
  SessionData,
  SysConfig,
} from 'common/swagger-providers/core-api.provider';
import { Observable, filter, forkJoin, map, switchMap, take } from 'rxjs';

interface OutboundCallParams {
  to: string;
  from: string;
  token: string;
  authToken: string;
  connectedTo: string;
  dialerNumber: string;
  locBypass: any;
  workerSID: any;
  agentId: any;
  leadId: any;
  customerId: any;
  leadLocationId: any;
  ledgerLocationId: any;
  fromLocationId: any;
  leadESId: any;
  customerESId: any;
  workflowStep: boolean;
  outboundCardLinkId: string;
  agentFirstName: string;
  agentLastName: string;
  statusUrl: string;
  reportId: number;
  callType: string;
  callBackTaskSid: string;
}
interface TransferCallParams {
  to?: string;
  from?: string;
  StatusCallbackEvent: string;
  authToken: string;
  FriendlyName: string;
}

@Injectable()
export class AWSLambdaProvider {
  private static readonly WARNING_THRESHOLD = 3000; // 3 seconds
  private static readonly ERROR_THRESHOLD = 8000; // 8 seconds

  private session$: Observable<fromSession.SessionState>;
  private systemConfiguration$: Observable<
    fromSystemConfiguration.SystemConfigurationState
  >;

  constructor(
    private store: Store<fromRoot.AppState>,
    private awsCredentialsProvider: AWSCredentialsProvider,
    private bugsnagService: BugsnagService
  ) {
    this.session$ = this.store.select('session');
    this.systemConfiguration$ = this.store.select('systemConfiguration');
    this.systemConfiguration$
      .pipe(take(1))
      .subscribe(
        (sysState: fromSystemConfiguration.SystemConfigurationState) => {
          if (!sysState.loaded) {
            this.store.dispatch(
              new fromSystemConfigurationActions.LoadSystemConfiguration()
            );
          }
        }
      );
  }

  public invokeOutboundCall(
    outboundCallParams: OutboundCallParams
  ): Observable<any> {
    let {
      to,
      from,
      token,
      authToken,
      connectedTo,
      dialerNumber,
      locBypass,
      workerSID,
      agentId,
      leadId,
      customerId,
      leadLocationId,
      ledgerLocationId,
      fromLocationId,
      leadESId,
      customerESId,
      workflowStep,
      outboundCardLinkId,
      agentFirstName,
      agentLastName,
      statusUrl,
      reportId,
      callType,
      callBackTaskSid
    } = outboundCallParams;
    const sessionSource: Observable<SessionData | null> = this.session$
      .pipe(filter((state: fromSession.SessionState) => state.loaded)
      ,take(1)
      ,map((state: fromSession.SessionState) => state.session));

    const outboundCallFunctionNameSource: Observable<
      string
    > = this.getOutboundCallLambdaFunctionName();

    const awsCredentials = this.awsCredentialsProvider
      .getOne()
      .pipe(map((response: IamCredentialsWrapper) => response.body));

    return forkJoin(
      sessionSource,
      outboundCallFunctionNameSource,
      awsCredentials
    ).pipe(switchMap(([session, outboundCallFunctionName, awsCredentials]) => {
      let payload: any = {
        queryStringParameters: {
          To: to,
          From: from,
          Token: token,
          authToken,
          connectType: connectedTo,
          agentPhone: dialerNumber,
          bypass_press_1: locBypass,
          WorkerSid: workerSID,
          agentId,
          leadId,
          customerId,
          leadLocationId,
          ledgerLocationId,
          fromLocationId,
          leadESId,
          customerESId,
          workflowStep,
          outboundCardLinkId,
          agentFirstName,
          agentLastName,
          statusUrl,
          reportId,
          callType,
          callback_task_sid: callBackTaskSid
        },
      };
      let requestParams: any = Object.assign({}, REQUEST_PARAMS, {
        FunctionName: outboundCallFunctionName,
        Payload: JSON.stringify(payload),
      });

      this.awsConfigUpdate(awsCredentials.Credentials);
      const lambda = new Lambda({
        region: AWS_REGION,
        apiVersion: AWS_API_VERSION,
      });

      return this.invokeLambdaWithErrorHandling(lambda, requestParams, outboundCallFunctionName);
    }));
  }

  public invokeTransferCall(
    transferCallParams: TransferCallParams
  ): Observable<any> {

    const conferenceEventFunctionNameSource: Observable<
      string
    > = this.getConferenceEventLambdaFunctionName();

    const awsCredentials = this.awsCredentialsProvider
      .getOne()
      .pipe(map((response: IamCredentialsWrapper) => response.body));

    return forkJoin(
      conferenceEventFunctionNameSource,
      awsCredentials
    ).pipe(switchMap(([conferenceEventFunctionName, awsCredentials]: [string, any]) => {
      let requestParams = Object.assign({}, REQUEST_PARAMS, {
        FunctionName: conferenceEventFunctionName,
        Payload: JSON.stringify({
          queryStringParameters: {
            ...transferCallParams
          },
        }),
      });
      this.awsConfigUpdate(awsCredentials.Credentials);

      const lambda = new Lambda({
        region: AWS_REGION,
        apiVersion: AWS_API_VERSION,
      });

      return this.invokeLambdaWithErrorHandling(lambda, requestParams, conferenceEventFunctionName);
    }));
  }

  /**
   * Helper method to invoke Lambda function with performance timing and error handling
   * @private
   * @param lambda - Lambda instance
   * @param requestParams - Request parameters for Lambda invocation
   * @param functionName - Name of the Lambda function being invoked
   * @returns Promise with parsed response body
   */
  private invokeLambdaWithErrorHandling(
    lambda: Lambda,
    requestParams: any,
    functionName: string
  ): Promise<any> {
    // Start performance timing for Lambda invocation
    const lambdaStartTime = performance.now();
    this.bugsnagService.leaveBreadcrumb('LambdaInvocationStart', {
      functionName: functionName,
      startTime: lambdaStartTime,
      timestamp: new Date().toISOString()
    });

    return new Promise((resolve, reject) => {
      lambda.invoke(requestParams, (error, data) => {
        // End performance timing
        const lambdaEndTime = performance.now();
        const lambdaDuration = lambdaEndTime - lambdaStartTime;
        
        this.bugsnagService.leaveBreadcrumb('LambdaInvocationEnd', {
          functionName: functionName,
          duration: lambdaDuration,
          success: !error,
          timestamp: new Date().toISOString()
        });

        // Log slow Lambda responses
        if (lambdaDuration > AWSLambdaProvider.WARNING_THRESHOLD) {
          const severity = lambdaDuration > AWSLambdaProvider.ERROR_THRESHOLD ? 'error' : 'warning';
          this.bugsnagService.notify(new Error(`Slow Lambda response detected`), {
            severity: severity as 'warning' | 'error',
            metadata: {
              lambdaPerformance: {
                functionName: functionName,
                duration: lambdaDuration,
                threshold: lambdaDuration > AWSLambdaProvider.ERROR_THRESHOLD ? AWSLambdaProvider.ERROR_THRESHOLD : AWSLambdaProvider.WARNING_THRESHOLD
              }
            }
          });
        }

        if (error) {
          this.bugsnagService.notify(error, {
            severity: 'error',
            metadata: {
              lambdaInvocation: {
                functionName: functionName,
                error: error.message,
                duration: lambdaDuration,
                requestParams
              }
            }
          });
          return reject(error);
        }

        // Enhanced error handling for JSON parsing
        this.bugsnagService.leaveBreadcrumb('LambdaResponseProcessing', {
          functionName: functionName,
          hasPayload: !!data.Payload,
          payloadType: typeof data.Payload,
          payloadLength: data.Payload ? data.Payload.toString().length : 0
        });

        try {
          let responsePayload: string = data.Payload as string;
          
          if (!responsePayload || responsePayload === 'undefined' || responsePayload.trim() === '') {
            const errorMsg = `Invalid Lambda response payload: ${responsePayload}`;
            this.bugsnagService.notify(new Error(errorMsg), {
              severity: 'error',
              metadata: {
                lambdaResponse: {
                  functionName: functionName,
                  rawPayload: data.Payload,
                  payloadType: typeof data.Payload,
                  requestParams
                }
              }
            });
            throw new Error(errorMsg);
          }
          
          let parsedPayload: any;
          try {
            parsedPayload = JSON.parse(responsePayload);
          } catch (parseError) {
            this.bugsnagService.notify(parseError, {
              severity: 'error',
              metadata: {
                lambdaResponseParsing: {
                  functionName: functionName,
                  rawPayload: responsePayload,
                  parseError: parseError.message
                }
              }
            });
            throw new Error(`Failed to parse Lambda response payload: ${parseError.message}`);
          }
          
          if (!parsedPayload.body) {
            const errorMsg = 'Lambda response missing body property';
            this.bugsnagService.notify(new Error(errorMsg), {
              severity: 'warning',
              metadata: {
                lambdaResponse: {
                  functionName: functionName,
                  parsedPayload
                }
              }
            });
          }
          
          let responseBody: any;
          try {
            responseBody = JSON.parse(parsedPayload.body);
          } catch (bodyParseError) {
            this.bugsnagService.notify(bodyParseError, {
              severity: 'error',
              metadata: {
                lambdaBodyParsing: {
                  functionName: functionName,
                  rawBody: parsedPayload.body,
                  parseError: bodyParseError.message
                }
              }
            });
            throw new Error(`Failed to parse Lambda response body: ${bodyParseError.message}`);
          }
          
          this.bugsnagService.leaveBreadcrumb('LambdaResponseSuccess', {
            functionName: functionName,
            hasBody: !!responseBody
          });
          
          return resolve(responseBody);
          
        } catch (error) {
          this.bugsnagService.notify(error, {
            severity: 'error',
            metadata: {
              lambdaProcessing: {
                functionName: functionName,
                rawData: data,
                error: error.message
              }
            }
          });
          return reject(error);
        }
      });
    });
  }

  /**
   * Updates AWS Configuration with the given credentials
   *
   * @private
   * @param {*} awsCredentials
   *
   * @memberOf TwilioTokenProvider
   */
  private awsConfigUpdate(awsCredentials: any): void {
    AWS.config.update({
      region: AWS_REGION,
      accessKeyId: awsCredentials.AccessKeyId,
      secretAccessKey: awsCredentials.SecretAccessKey,
      sessionToken: awsCredentials.SessionToken,
    });
  }

  private getTaskRouterTokenLambdaFunctionName(): Observable<string> {
    return this.systemConfiguration$
      .pipe(filter(
        (state: fromSystemConfiguration.SystemConfigurationState) =>
          state.loaded
      )
      ,take(1)
      ,map(
        (state: fromSystemConfiguration.SystemConfigurationState) => state.data
      )
      ,map((sysConfig: SysConfig) => {
        let appPrefix: string = sysConfig.appPrefix;
        return `${appPrefix}${TASK_ROUTER_TOKEN_LAMBDA_FUNCTION_NAME_POSTFIX}`;
      }));
  }

  private getOutboundCallLambdaFunctionName(): Observable<string> {
    return this.systemConfiguration$
      .pipe(filter(
        (state: fromSystemConfiguration.SystemConfigurationState) =>
          state.loaded
      )
      ,take(1)
      ,map(
        (state: fromSystemConfiguration.SystemConfigurationState) => state.data
      )
      ,map((sysConfig: SysConfig) => {
        let appPrefix: string = sysConfig.appPrefix;
        return `${appPrefix}${OUTBOUND_CALL_LAMBDA_FUNCTION_NAME_POSTFIX}`;
      }));
  }

  private getConferenceEventLambdaFunctionName(): Observable<string> {
    return this.systemConfiguration$
      .pipe(filter(
        (state: fromSystemConfiguration.SystemConfigurationState) =>
          state.loaded
      )
      ,take(1)
      ,map(
        (state: fromSystemConfiguration.SystemConfigurationState) => state.data
      )
      ,map((sysConfig: SysConfig) => {
        let appPrefix: string = sysConfig.appPrefix;
        return `${appPrefix}${CONFERENCE_EVENT_LAMBDA_FUNCTION_NAME_POSTFIX}`;
      }));
  }
}
