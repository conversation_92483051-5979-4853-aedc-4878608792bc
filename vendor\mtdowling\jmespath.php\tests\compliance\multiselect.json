[{"given": {"foo": {"bar": "bar", "baz": "baz", "qux": "qux", "nested": {"one": {"a": "first", "b": "second", "c": "third"}, "two": {"a": "first", "b": "second", "c": "third"}, "three": {"a": "first", "b": "second", "c": {"inner": "third"}}}}, "bar": 1, "baz": 2, "qux\"": 3}, "cases": [{"expression": "foo.{bar: bar}", "result": {"bar": "bar"}}, {"expression": "foo.{\"bar\": bar}", "result": {"bar": "bar"}}, {"expression": "foo.{\"foo.bar\": bar}", "result": {"foo.bar": "bar"}}, {"expression": "foo.{bar: bar, baz: baz}", "result": {"bar": "bar", "baz": "baz"}}, {"expression": "foo.{\"bar\": bar, \"baz\": baz}", "result": {"bar": "bar", "baz": "baz"}}, {"expression": "{\"baz\": baz, \"qux\\\"\": \"qux\\\"\"}", "result": {"baz": 2, "qux\"": 3}}, {"expression": "foo.{bar:bar,baz:baz}", "result": {"bar": "bar", "baz": "baz"}}, {"expression": "foo.{bar: bar,qux: qux}", "result": {"bar": "bar", "qux": "qux"}}, {"expression": "foo.{bar: bar, noexist: noexist}", "result": {"bar": "bar", "noexist": null}}, {"expression": "foo.{noexist: noexist, alsonoexist: alsonoexist}", "result": {"noexist": null, "alsonoexist": null}}, {"expression": "foo.badkey.{nokey: nokey, alsonokey: alsonokey}", "result": null}, {"expression": "foo.nested.*.{a: a,b: b}", "result": [{"a": "first", "b": "second"}, {"a": "first", "b": "second"}, {"a": "first", "b": "second"}]}, {"expression": "foo.nested.three.{a: a, cinner: c.inner}", "result": {"a": "first", "cinner": "third"}}, {"expression": "foo.nested.three.{a: a, c: c.inner.bad.key}", "result": {"a": "first", "c": null}}, {"expression": "foo.{a: nested.one.a, b: nested.two.b}", "result": {"a": "first", "b": "second"}}, {"expression": "{bar: bar, baz: baz}", "result": {"bar": 1, "baz": 2}}, {"expression": "{bar: bar}", "result": {"bar": 1}}, {"expression": "{otherkey: bar}", "result": {"otherkey": 1}}, {"expression": "{no: no, exist: exist}", "result": {"no": null, "exist": null}}, {"expression": "foo.[bar]", "result": ["bar"]}, {"expression": "foo.[bar,baz]", "result": ["bar", "baz"]}, {"expression": "foo.[bar,qux]", "result": ["bar", "qux"]}, {"expression": "foo.[bar,noexist]", "result": ["bar", null]}, {"expression": "foo.[noexist,alsonoexist]", "result": [null, null]}]}, {"given": {"foo": {"bar": 1, "baz": [2, 3, 4]}}, "cases": [{"expression": "foo.{bar:bar,baz:baz}", "result": {"bar": 1, "baz": [2, 3, 4]}}, {"expression": "foo.[bar,baz[0]]", "result": [1, 2]}, {"expression": "foo.[bar,baz[1]]", "result": [1, 3]}, {"expression": "foo.[bar,baz[2]]", "result": [1, 4]}, {"expression": "foo.[bar,baz[3]]", "result": [1, null]}, {"expression": "foo.[bar[0],baz[3]]", "result": [null, null]}]}, {"given": {"foo": {"bar": 1, "baz": 2}}, "cases": [{"expression": "foo.{bar: bar, baz: baz}", "result": {"bar": 1, "baz": 2}}, {"expression": "foo.[bar,baz]", "result": [1, 2]}]}, {"given": {"foo": {"bar": {"baz": [{"common": "first", "one": 1}, {"common": "second", "two": 2}]}, "ignoreme": 1, "includeme": true}}, "cases": [{"expression": "foo.{bar: bar.baz[1],includeme: includeme}", "result": {"bar": {"common": "second", "two": 2}, "includeme": true}}, {"expression": "foo.{\"bar.baz.two\": bar.baz[1].two, includeme: includeme}", "result": {"bar.baz.two": 2, "includeme": true}}, {"expression": "foo.[includeme, bar.baz[*].common]", "result": [true, ["first", "second"]]}, {"expression": "foo.[includeme, bar.baz[*].none]", "result": [true, []]}, {"expression": "foo.[includeme, bar.baz[].common]", "result": [true, ["first", "second"]]}]}, {"given": {"reservations": [{"instances": [{"id": "id1", "name": "first"}, {"id": "id2", "name": "second"}]}, {"instances": [{"id": "id3", "name": "third"}, {"id": "id4", "name": "fourth"}]}]}, "cases": [{"expression": "reservations[*].instances[*].{id: id, name: name}", "result": [[{"id": "id1", "name": "first"}, {"id": "id2", "name": "second"}], [{"id": "id3", "name": "third"}, {"id": "id4", "name": "fourth"}]]}, {"expression": "reservations[].instances[].{id: id, name: name}", "result": [{"id": "id1", "name": "first"}, {"id": "id2", "name": "second"}, {"id": "id3", "name": "third"}, {"id": "id4", "name": "fourth"}]}, {"expression": "reservations[].instances[].[id, name]", "result": [["id1", "first"], ["id2", "second"], ["id3", "third"], ["id4", "fourth"]]}]}, {"given": {"foo": [{"bar": [{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}]}, {"bar": [{"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]}]}, "cases": [{"expression": "foo", "result": [{"bar": [{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}]}, {"bar": [{"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]}]}, {"expression": "foo[]", "result": [{"bar": [{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}]}, {"bar": [{"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]}]}, {"expression": "foo[].bar", "result": [[{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}], [{"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]]}, {"expression": "foo[].bar[]", "result": [{"qux": 2, "baz": 1}, {"qux": 4, "baz": 3}, {"qux": 6, "baz": 5}, {"qux": 8, "baz": 7}]}, {"expression": "foo[].bar[].[baz, qux]", "result": [[1, 2], [3, 4], [5, 6], [7, 8]]}, {"expression": "foo[].bar[].[baz]", "result": [[1], [3], [5], [7]]}, {"expression": "foo[].bar[].[baz, qux][]", "result": [1, 2, 3, 4, 5, 6, 7, 8]}]}, {"given": {"foo": {"baz": [{"bar": "abc"}, {"bar": "def"}], "qux": ["zero"]}}, "cases": [{"expression": "foo.[baz[*].bar, qux[0]]", "result": [["abc", "def"], "zero"]}]}, {"given": {"foo": {"baz": [{"bar": "a", "bam": "b", "boo": "c"}, {"bar": "d", "bam": "e", "boo": "f"}], "qux": ["zero"]}}, "cases": [{"expression": "foo.[baz[*].[bar, boo], qux[0]]", "result": [[["a", "c"], ["d", "f"]], "zero"]}]}, {"given": {"foo": {"baz": [{"bar": "a", "bam": "b", "boo": "c"}, {"bar": "d", "bam": "e", "boo": "f"}], "qux": ["zero"]}}, "cases": [{"expression": "foo.[baz[*].not_there || baz[*].bar, qux[0]]", "result": [["a", "d"], "zero"]}]}, {"given": {"type": "object"}, "cases": [{"comment": "Nested multiselect", "expression": "[[*],*]", "result": [null, ["object"]]}]}, {"given": [], "cases": [{"comment": "Nested multiselect", "expression": "[[*]]", "result": [[]]}]}]