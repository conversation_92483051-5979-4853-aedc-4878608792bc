# CRM Call Center Debugging Runbook

## Overview

This runbook provides comprehensive guidance for debugging Twilio TaskRouter state management issues in the CRM call center application using the enhanced logging system implemented across Phases 1-4.

## Quick Reference

### Common Bug Types and Logging Events

| Bug Type | Key Logging Events | Search Terms |
|----------|-------------------|--------------|
| Call re-presenting to agent | `reservation.created`, `call.popup.presentation` | `CallPopPresentationAttempt`, `ReservationCreated` |
| Unable to connect to call | `worker.activity.update`, `reservation.accepted` | `WorkerActivityUpdateAttempt`, `ReservationAccepted` |
| JSON parse errors | `lambda.response.processing` | `LambdaResponseProcessing`, `parseError` |
| Call pop not presented | `call.popup.presentation.requested` | `CallPopPresentationRequested`, `ToggleInboundCallPopUp` |
| State inconsistencies | `monitoring.state.inconsistency` | `StateInconsistency`, `validateStateConsistency` |

## Logging System Architecture

### Phase 1: Critical Path Logging
- **Worker Activity Updates**: Enhanced logging for all worker state transitions
- **JSON Parsing Error Prevention**: Comprehensive error handling for Lambda responses
- **Basic Reservation State Transitions**: Logging for reservation lifecycle events

### Phase 2: State Management Monitoring
- **Comprehensive Reservation Logging**: All reservation events with timing
- **Call Pop Presentation Tracking**: Full popup lifecycle monitoring
- **Worker Channel Availability**: Channel state change logging

### Phase 3: Advanced Diagnostics
- **Performance Timing Analysis**: Timing for all critical operations
- **State Synchronization Monitoring**: Automated state consistency validation
- **Race Condition Detection**: Timing-based race condition identification

### Phase 4: Monitoring & Alerting
- **Periodic State Validation**: Automated 30-second state consistency checks
- **Automated Inconsistency Alerts**: Proactive problem detection
- **Performance Baseline Metrics**: Trend analysis and anomaly detection

## Debugging Workflows

### 1. Call Re-presenting to Agent (CPBUG-525)

**Symptoms**: Call presents to agent multiple times, agent unable to answer

**Debugging Steps**:

1. **Search for Reservation Events**:
   ```
   Search: reservation.created AND task.sid:[TASK_SID]
   ```

2. **Check Call Pop Presentation Timing**:
   ```
   Search: CallPopPresentationAttempt AND reservationId:[TASK_SID]
   Look for: trackCallPopPresentationTiming events
   ```

3. **Analyze Worker Activity Updates**:
   ```
   Search: WorkerActivityUpdateAttempt AND reservationId:[TASK_SID]
   Check: Duration and success/failure of updates
   ```

4. **Look for Race Conditions**:
   ```
   Search: ReservationTiming AND timeBetweenReservations:<5000
   Check: Multiple reservations within 5 seconds
   ```

**Expected Log Flow**:
1. `ReservationCreated` → `CallPopPresentationAttempt` → `CallPopPresentationRequested` → `ReservationAccepted` → `WorkerActivityUpdateAttempt` → `WorkerActivityUpdateSuccess`

### 2. Unable to Connect to Call (CPBUG-539)

**Symptoms**: Call stuck in hanging status, unable to connect

**Debugging Steps**:

1. **Check Lambda Response Times**:
   ```
   Search: LambdaInvocationStart AND functionName:outbound-call
   Look for: Duration >3000ms (warning) or >8000ms (error)
   ```

2. **Validate Worker Activity Update Success**:
   ```
   Search: worker.activity.update.failed AND reservationId:[TASK_SID]
   Check: Error codes and messages
   ```

3. **Check State Consistency**:
   ```
   Search: StateValidation AND inconsistencies
   Look for: Agent state vs. worker activity mismatches
   ```

4. **Review Connection Health**:
   ```
   Search: validateWorkerConnectionHealth AND healthy:false
   Check: Missing worker, client, or device instances
   ```

### 3. JSON Parse Errors (CPBUG-553)

**Symptoms**: Follow up call stuck in 'Connecting...' with JSON parse errors

**Debugging Steps**:

1. **Search for Lambda Response Issues**:
   ```
   Search: LambdaResponseProcessing AND (payloadType:undefined OR rawPayload:undefined)
   ```

2. **Check Enhanced Error Handling**:
   ```
   Search: lambdaResponseParsing AND parseError
   Look for: Specific parse error messages
   ```

3. **Validate Request Parameters**:
   ```
   Search: lambdaInvocation AND error
   Check: Request parameters and function names
   ```

### 4. Call Pop Not Presented (CPBUG-881)

**Symptoms**: Reservation created but UI doesn't show popup

**Debugging Steps**:

1. **Check Reservation Creation**:
   ```
   Search: ReservationCreated AND taskChannel:voice
   Verify: Reservation properly created
   ```

2. **Trace Call Pop Presentation**:
   ```
   Search: trackCallPopPresentationTiming AND stage:start
   Follow: start → popup_requested → popup_visible
   ```

3. **Check UI State Transitions**:
   ```
   Search: ToggleInboundCallPopUp AND reservationId:[TASK_SID]
   Verify: Popup toggle events
   ```

4. **Validate Worker Channel Availability**:
   ```
   Search: WorkerChannelChange AND taskChannel:voice
   Check: Channel availability status
   ```

## Performance Analysis

### Timing Thresholds

| Operation | Warning Threshold | Error Threshold | Search Term |
|-----------|------------------|-----------------|-------------|
| Worker Activity Update | >2 seconds | >5 seconds | `performance.worker.activity.update` |
| Call Pop Presentation | >1 second | >3 seconds | `performance.call.pop.presentation` |
| Lambda Response | >3 seconds | >8 seconds | `performance.lambda.response` |
| Reservation Processing | >1.5 seconds | >4 seconds | `performance.reservation.processing` |

### Performance Degradation Detection

**Search for Performance Issues**:
```
Search: performance.degradation AND metricType:[METRIC_TYPE]
Look for: degradationPercent >50%
```

**Analyze Performance Trends**:
```
Search: PerformanceTimerEnd AND duration:[THRESHOLD]
Group by: operationType
```

## Automated Monitoring

### Periodic State Validation

**Validation Frequency**: Every 30 seconds
**Search Term**: `PeriodicStateValidation`

**Key Metrics**:
- State inconsistencies count
- Worker connection health
- Reservation health
- Performance metrics

### State Inconsistency Alerts

**Critical Issues** (Immediate attention required):
- Has active reservation but agent state is READY
- Agent on inbound call but no current reservation

**High Priority Issues**:
- Agent state READY but worker activity not Ready
- Call state IDLE but has accepted reservation
- Internet connection lost but agent not offline

**Medium Priority Issues**:
- Reservation older than 5 minutes still active

### Automatic Recovery

The system attempts automatic recovery for:

1. **Agent State/Worker Activity Mismatch**:
   - Action: Sync worker activity to Ready state
   - Search: `AutoRecoveryAttempt AND action:"sync worker activity"`

2. **Stale Reservation Cleanup**:
   - Action: Clear reservations older than 10 minutes
   - Search: `AutoRecoveryAttempt AND action:"Clearing stale reservation"`

## Alert Response Procedures

### Critical Alerts (Error Level)

1. **State Inconsistency (3+ consecutive)**:
   - Immediate investigation required
   - Check worker connection health
   - Verify Twilio service status

2. **Performance Degradation (>50% increase)**:
   - Monitor system resources
   - Check for memory leaks
   - Review recent deployments

### Warning Alerts

1. **Slow Operations**:
   - Monitor trends
   - Check network connectivity
   - Review Lambda function performance

2. **Single State Inconsistency**:
   - Log for tracking
   - Monitor for patterns
   - Check if auto-recovery succeeded

## Best Practices

### Log Analysis

1. **Start with Timeline**: Use timestamps to create event timeline
2. **Follow the Chain**: Trace related events using reservation/task IDs
3. **Check Performance**: Always review timing data for bottlenecks
4. **Validate State**: Use state validation logs to identify inconsistencies

### Performance Monitoring

1. **Establish Baselines**: Track normal operation metrics
2. **Monitor Trends**: Look for gradual degradation over time
3. **Alert Thresholds**: Adjust based on observed normal performance
4. **Resource Usage**: Monitor memory and timer usage

### Incident Response

1. **Immediate**: Check for critical state inconsistencies
2. **Short-term**: Analyze performance timing data
3. **Long-term**: Review trends and baseline metrics
4. **Follow-up**: Validate automatic recovery attempts
