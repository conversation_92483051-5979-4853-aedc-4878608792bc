<?php
// This file was auto-generated from sdk-root/src/data/lightsail/2016-11-28/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-11-28', 'endpointPrefix' => 'lightsail', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Amazon Lightsail', 'serviceId' => 'Lightsail', 'signatureVersion' => 'v4', 'targetPrefix' => 'Lightsail_20161128', 'uid' => 'lightsail-2016-11-28', ], 'operations' => [ 'AllocateStaticIp' => [ 'name' => 'AllocateStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AllocateStaticIpRequest', ], 'output' => [ 'shape' => 'AllocateStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'AttachDisk' => [ 'name' => 'AttachDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachDiskRequest', ], 'output' => [ 'shape' => 'AttachDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'AttachInstancesToLoadBalancer' => [ 'name' => 'AttachInstancesToLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachInstancesToLoadBalancerRequest', ], 'output' => [ 'shape' => 'AttachInstancesToLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'AttachLoadBalancerTlsCertificate' => [ 'name' => 'AttachLoadBalancerTlsCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachLoadBalancerTlsCertificateRequest', ], 'output' => [ 'shape' => 'AttachLoadBalancerTlsCertificateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'AttachStaticIp' => [ 'name' => 'AttachStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AttachStaticIpRequest', ], 'output' => [ 'shape' => 'AttachStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CloseInstancePublicPorts' => [ 'name' => 'CloseInstancePublicPorts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CloseInstancePublicPortsRequest', ], 'output' => [ 'shape' => 'CloseInstancePublicPortsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDisk' => [ 'name' => 'CreateDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDiskRequest', ], 'output' => [ 'shape' => 'CreateDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDiskFromSnapshot' => [ 'name' => 'CreateDiskFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDiskFromSnapshotRequest', ], 'output' => [ 'shape' => 'CreateDiskFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDiskSnapshot' => [ 'name' => 'CreateDiskSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDiskSnapshotRequest', ], 'output' => [ 'shape' => 'CreateDiskSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDomain' => [ 'name' => 'CreateDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDomainRequest', ], 'output' => [ 'shape' => 'CreateDomainResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateDomainEntry' => [ 'name' => 'CreateDomainEntry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDomainEntryRequest', ], 'output' => [ 'shape' => 'CreateDomainEntryResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateInstanceSnapshot' => [ 'name' => 'CreateInstanceSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstanceSnapshotRequest', ], 'output' => [ 'shape' => 'CreateInstanceSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateInstances' => [ 'name' => 'CreateInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstancesRequest', ], 'output' => [ 'shape' => 'CreateInstancesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateInstancesFromSnapshot' => [ 'name' => 'CreateInstancesFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateInstancesFromSnapshotRequest', ], 'output' => [ 'shape' => 'CreateInstancesFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateKeyPair' => [ 'name' => 'CreateKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateKeyPairRequest', ], 'output' => [ 'shape' => 'CreateKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateLoadBalancer' => [ 'name' => 'CreateLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLoadBalancerRequest', ], 'output' => [ 'shape' => 'CreateLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'CreateLoadBalancerTlsCertificate' => [ 'name' => 'CreateLoadBalancerTlsCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateLoadBalancerTlsCertificateRequest', ], 'output' => [ 'shape' => 'CreateLoadBalancerTlsCertificateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteDisk' => [ 'name' => 'DeleteDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDiskRequest', ], 'output' => [ 'shape' => 'DeleteDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteDiskSnapshot' => [ 'name' => 'DeleteDiskSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDiskSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteDiskSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteDomain' => [ 'name' => 'DeleteDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDomainRequest', ], 'output' => [ 'shape' => 'DeleteDomainResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteDomainEntry' => [ 'name' => 'DeleteDomainEntry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDomainEntryRequest', ], 'output' => [ 'shape' => 'DeleteDomainEntryResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteInstance' => [ 'name' => 'DeleteInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceRequest', ], 'output' => [ 'shape' => 'DeleteInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteInstanceSnapshot' => [ 'name' => 'DeleteInstanceSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteInstanceSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteInstanceSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteKeyPair' => [ 'name' => 'DeleteKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteKeyPairRequest', ], 'output' => [ 'shape' => 'DeleteKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteLoadBalancer' => [ 'name' => 'DeleteLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLoadBalancerRequest', ], 'output' => [ 'shape' => 'DeleteLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DeleteLoadBalancerTlsCertificate' => [ 'name' => 'DeleteLoadBalancerTlsCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteLoadBalancerTlsCertificateRequest', ], 'output' => [ 'shape' => 'DeleteLoadBalancerTlsCertificateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DetachDisk' => [ 'name' => 'DetachDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachDiskRequest', ], 'output' => [ 'shape' => 'DetachDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DetachInstancesFromLoadBalancer' => [ 'name' => 'DetachInstancesFromLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachInstancesFromLoadBalancerRequest', ], 'output' => [ 'shape' => 'DetachInstancesFromLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DetachStaticIp' => [ 'name' => 'DetachStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DetachStaticIpRequest', ], 'output' => [ 'shape' => 'DetachStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'DownloadDefaultKeyPair' => [ 'name' => 'DownloadDefaultKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DownloadDefaultKeyPairRequest', ], 'output' => [ 'shape' => 'DownloadDefaultKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetActiveNames' => [ 'name' => 'GetActiveNames', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetActiveNamesRequest', ], 'output' => [ 'shape' => 'GetActiveNamesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetBlueprints' => [ 'name' => 'GetBlueprints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBlueprintsRequest', ], 'output' => [ 'shape' => 'GetBlueprintsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetBundles' => [ 'name' => 'GetBundles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBundlesRequest', ], 'output' => [ 'shape' => 'GetBundlesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDisk' => [ 'name' => 'GetDisk', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDiskRequest', ], 'output' => [ 'shape' => 'GetDiskResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDiskSnapshot' => [ 'name' => 'GetDiskSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDiskSnapshotRequest', ], 'output' => [ 'shape' => 'GetDiskSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDiskSnapshots' => [ 'name' => 'GetDiskSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDiskSnapshotsRequest', ], 'output' => [ 'shape' => 'GetDiskSnapshotsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDisks' => [ 'name' => 'GetDisks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDisksRequest', ], 'output' => [ 'shape' => 'GetDisksResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDomain' => [ 'name' => 'GetDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDomainRequest', ], 'output' => [ 'shape' => 'GetDomainResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetDomains' => [ 'name' => 'GetDomains', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDomainsRequest', ], 'output' => [ 'shape' => 'GetDomainsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstance' => [ 'name' => 'GetInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceRequest', ], 'output' => [ 'shape' => 'GetInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceAccessDetails' => [ 'name' => 'GetInstanceAccessDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceAccessDetailsRequest', ], 'output' => [ 'shape' => 'GetInstanceAccessDetailsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceMetricData' => [ 'name' => 'GetInstanceMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceMetricDataRequest', ], 'output' => [ 'shape' => 'GetInstanceMetricDataResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstancePortStates' => [ 'name' => 'GetInstancePortStates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstancePortStatesRequest', ], 'output' => [ 'shape' => 'GetInstancePortStatesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceSnapshot' => [ 'name' => 'GetInstanceSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceSnapshotRequest', ], 'output' => [ 'shape' => 'GetInstanceSnapshotResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceSnapshots' => [ 'name' => 'GetInstanceSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceSnapshotsRequest', ], 'output' => [ 'shape' => 'GetInstanceSnapshotsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstanceState' => [ 'name' => 'GetInstanceState', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstanceStateRequest', ], 'output' => [ 'shape' => 'GetInstanceStateResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetInstances' => [ 'name' => 'GetInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetInstancesRequest', ], 'output' => [ 'shape' => 'GetInstancesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetKeyPair' => [ 'name' => 'GetKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetKeyPairRequest', ], 'output' => [ 'shape' => 'GetKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetKeyPairs' => [ 'name' => 'GetKeyPairs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetKeyPairsRequest', ], 'output' => [ 'shape' => 'GetKeyPairsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetLoadBalancer' => [ 'name' => 'GetLoadBalancer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoadBalancerRequest', ], 'output' => [ 'shape' => 'GetLoadBalancerResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetLoadBalancerMetricData' => [ 'name' => 'GetLoadBalancerMetricData', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoadBalancerMetricDataRequest', ], 'output' => [ 'shape' => 'GetLoadBalancerMetricDataResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetLoadBalancerTlsCertificates' => [ 'name' => 'GetLoadBalancerTlsCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoadBalancerTlsCertificatesRequest', ], 'output' => [ 'shape' => 'GetLoadBalancerTlsCertificatesResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetLoadBalancers' => [ 'name' => 'GetLoadBalancers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLoadBalancersRequest', ], 'output' => [ 'shape' => 'GetLoadBalancersResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetOperation' => [ 'name' => 'GetOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOperationRequest', ], 'output' => [ 'shape' => 'GetOperationResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetOperations' => [ 'name' => 'GetOperations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOperationsRequest', ], 'output' => [ 'shape' => 'GetOperationsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetOperationsForResource' => [ 'name' => 'GetOperationsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOperationsForResourceRequest', ], 'output' => [ 'shape' => 'GetOperationsForResourceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetRegions' => [ 'name' => 'GetRegions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRegionsRequest', ], 'output' => [ 'shape' => 'GetRegionsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetStaticIp' => [ 'name' => 'GetStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStaticIpRequest', ], 'output' => [ 'shape' => 'GetStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'GetStaticIps' => [ 'name' => 'GetStaticIps', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStaticIpsRequest', ], 'output' => [ 'shape' => 'GetStaticIpsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'ImportKeyPair' => [ 'name' => 'ImportKeyPair', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportKeyPairRequest', ], 'output' => [ 'shape' => 'ImportKeyPairResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'IsVpcPeered' => [ 'name' => 'IsVpcPeered', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'IsVpcPeeredRequest', ], 'output' => [ 'shape' => 'IsVpcPeeredResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'OpenInstancePublicPorts' => [ 'name' => 'OpenInstancePublicPorts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'OpenInstancePublicPortsRequest', ], 'output' => [ 'shape' => 'OpenInstancePublicPortsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'PeerVpc' => [ 'name' => 'PeerVpc', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PeerVpcRequest', ], 'output' => [ 'shape' => 'PeerVpcResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'PutInstancePublicPorts' => [ 'name' => 'PutInstancePublicPorts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutInstancePublicPortsRequest', ], 'output' => [ 'shape' => 'PutInstancePublicPortsResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'RebootInstance' => [ 'name' => 'RebootInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootInstanceRequest', ], 'output' => [ 'shape' => 'RebootInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'ReleaseStaticIp' => [ 'name' => 'ReleaseStaticIp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ReleaseStaticIpRequest', ], 'output' => [ 'shape' => 'ReleaseStaticIpResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'StartInstance' => [ 'name' => 'StartInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartInstanceRequest', ], 'output' => [ 'shape' => 'StartInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'StopInstance' => [ 'name' => 'StopInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopInstanceRequest', ], 'output' => [ 'shape' => 'StopInstanceResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UnpeerVpc' => [ 'name' => 'UnpeerVpc', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UnpeerVpcRequest', ], 'output' => [ 'shape' => 'UnpeerVpcResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateDomainEntry' => [ 'name' => 'UpdateDomainEntry', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDomainEntryRequest', ], 'output' => [ 'shape' => 'UpdateDomainEntryResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], 'UpdateLoadBalancerAttribute' => [ 'name' => 'UpdateLoadBalancerAttribute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateLoadBalancerAttributeRequest', ], 'output' => [ 'shape' => 'UpdateLoadBalancerAttributeResult', ], 'errors' => [ [ 'shape' => 'ServiceException', ], [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'OperationFailureException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'AccountSetupInProgressException', ], [ 'shape' => 'UnauthenticatedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'AccessDirection' => [ 'type' => 'string', 'enum' => [ 'inbound', 'outbound', ], ], 'AccountSetupInProgressException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'AllocateStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], ], ], 'AllocateStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AttachDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', 'instanceName', 'diskPath', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], 'instanceName' => [ 'shape' => 'ResourceName', ], 'diskPath' => [ 'shape' => 'NonEmptyString', ], ], ], 'AttachDiskResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AttachInstancesToLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'instanceNames', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'instanceNames' => [ 'shape' => 'ResourceNameList', ], ], ], 'AttachInstancesToLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AttachLoadBalancerTlsCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'certificateName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'certificateName' => [ 'shape' => 'ResourceName', ], ], ], 'AttachLoadBalancerTlsCertificateResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AttachStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', 'instanceName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'AttachStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'AttachedDiskMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ResourceName', ], 'value' => [ 'shape' => 'DiskMapList', ], ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'zoneName' => [ 'shape' => 'NonEmptyString', ], 'state' => [ 'shape' => 'NonEmptyString', ], ], ], 'AvailabilityZoneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'Base64' => [ 'type' => 'string', ], 'Blueprint' => [ 'type' => 'structure', 'members' => [ 'blueprintId' => [ 'shape' => 'NonEmptyString', ], 'name' => [ 'shape' => 'ResourceName', ], 'group' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'BlueprintType', ], 'description' => [ 'shape' => 'string', ], 'isActive' => [ 'shape' => 'boolean', ], 'minPower' => [ 'shape' => 'integer', ], 'version' => [ 'shape' => 'string', ], 'versionCode' => [ 'shape' => 'string', ], 'productUrl' => [ 'shape' => 'string', ], 'licenseUrl' => [ 'shape' => 'string', ], 'platform' => [ 'shape' => 'InstancePlatform', ], ], ], 'BlueprintList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Blueprint', ], ], 'BlueprintType' => [ 'type' => 'string', 'enum' => [ 'os', 'app', ], ], 'Bundle' => [ 'type' => 'structure', 'members' => [ 'price' => [ 'shape' => 'float', ], 'cpuCount' => [ 'shape' => 'integer', ], 'diskSizeInGb' => [ 'shape' => 'integer', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'instanceType' => [ 'shape' => 'string', ], 'isActive' => [ 'shape' => 'boolean', ], 'name' => [ 'shape' => 'string', ], 'power' => [ 'shape' => 'integer', ], 'ramSizeInGb' => [ 'shape' => 'float', ], 'transferPerMonthInGb' => [ 'shape' => 'integer', ], 'supportedPlatforms' => [ 'shape' => 'InstancePlatformList', ], ], ], 'BundleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Bundle', ], ], 'CloseInstancePublicPortsRequest' => [ 'type' => 'structure', 'required' => [ 'portInfo', 'instanceName', ], 'members' => [ 'portInfo' => [ 'shape' => 'PortInfo', ], 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'CloseInstancePublicPortsResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'CreateDiskFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', 'diskSnapshotName', 'availabilityZone', 'sizeInGb', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], 'diskSnapshotName' => [ 'shape' => 'ResourceName', ], 'availabilityZone' => [ 'shape' => 'NonEmptyString', ], 'sizeInGb' => [ 'shape' => 'integer', ], ], ], 'CreateDiskFromSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', 'availabilityZone', 'sizeInGb', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], 'availabilityZone' => [ 'shape' => 'NonEmptyString', ], 'sizeInGb' => [ 'shape' => 'integer', ], ], ], 'CreateDiskResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateDiskSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', 'diskSnapshotName', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], 'diskSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateDiskSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateDomainEntryRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', 'domainEntry', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'domainEntry' => [ 'shape' => 'DomainEntry', ], ], ], 'CreateDomainEntryResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'CreateDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], ], ], 'CreateDomainResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'CreateInstanceSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'instanceSnapshotName', 'instanceName', ], 'members' => [ 'instanceSnapshotName' => [ 'shape' => 'ResourceName', ], 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateInstanceSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateInstancesFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'instanceNames', 'availabilityZone', 'instanceSnapshotName', 'bundleId', ], 'members' => [ 'instanceNames' => [ 'shape' => 'StringList', ], 'attachedDiskMapping' => [ 'shape' => 'AttachedDiskMap', ], 'availabilityZone' => [ 'shape' => 'string', ], 'instanceSnapshotName' => [ 'shape' => 'ResourceName', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'userData' => [ 'shape' => 'string', ], 'keyPairName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateInstancesFromSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'instanceNames', 'availabilityZone', 'blueprintId', 'bundleId', ], 'members' => [ 'instanceNames' => [ 'shape' => 'StringList', ], 'availabilityZone' => [ 'shape' => 'string', ], 'customImageName' => [ 'shape' => 'ResourceName', 'deprecated' => true, ], 'blueprintId' => [ 'shape' => 'NonEmptyString', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'userData' => [ 'shape' => 'string', ], 'keyPairName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateInstancesResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'keyPairName', ], 'members' => [ 'keyPairName' => [ 'shape' => 'ResourceName', ], ], ], 'CreateKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'keyPair' => [ 'shape' => 'KeyPair', ], 'publicKeyBase64' => [ 'shape' => 'Base64', ], 'privateKeyBase64' => [ 'shape' => 'Base64', ], 'operation' => [ 'shape' => 'Operation', ], ], ], 'CreateLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'instancePort', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'instancePort' => [ 'shape' => 'Port', ], 'healthCheckPath' => [ 'shape' => 'string', ], 'certificateName' => [ 'shape' => 'ResourceName', ], 'certificateDomainName' => [ 'shape' => 'DomainName', ], 'certificateAlternativeNames' => [ 'shape' => 'DomainNameList', ], ], ], 'CreateLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'CreateLoadBalancerTlsCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'certificateName', 'certificateDomainName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'certificateName' => [ 'shape' => 'ResourceName', ], 'certificateDomainName' => [ 'shape' => 'DomainName', ], 'certificateAlternativeNames' => [ 'shape' => 'DomainNameList', ], ], ], 'CreateLoadBalancerTlsCertificateResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteDiskResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteDiskSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'diskSnapshotName', ], 'members' => [ 'diskSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteDiskSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteDomainEntryRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', 'domainEntry', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'domainEntry' => [ 'shape' => 'DomainEntry', ], ], ], 'DeleteDomainEntryResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'DeleteDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], ], ], 'DeleteDomainResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'DeleteInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteInstanceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteInstanceSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'instanceSnapshotName', ], 'members' => [ 'instanceSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteInstanceSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'keyPairName', ], 'members' => [ 'keyPairName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'DeleteLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], ], ], 'DeleteLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DeleteLoadBalancerTlsCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'certificateName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'certificateName' => [ 'shape' => 'ResourceName', ], 'force' => [ 'shape' => 'boolean', ], ], ], 'DeleteLoadBalancerTlsCertificateResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DetachDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], ], ], 'DetachDiskResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DetachInstancesFromLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'instanceNames', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'instanceNames' => [ 'shape' => 'ResourceNameList', ], ], ], 'DetachInstancesFromLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'DetachStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], ], ], 'DetachStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'Disk' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'sizeInGb' => [ 'shape' => 'integer', ], 'isSystemDisk' => [ 'shape' => 'boolean', ], 'iops' => [ 'shape' => 'integer', ], 'path' => [ 'shape' => 'string', ], 'state' => [ 'shape' => 'DiskState', ], 'attachedTo' => [ 'shape' => 'ResourceName', ], 'isAttached' => [ 'shape' => 'boolean', ], 'attachmentState' => [ 'shape' => 'string', 'deprecated' => true, ], 'gbInUse' => [ 'shape' => 'integer', 'deprecated' => true, ], ], ], 'DiskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Disk', ], ], 'DiskMap' => [ 'type' => 'structure', 'members' => [ 'originalDiskPath' => [ 'shape' => 'NonEmptyString', ], 'newDiskName' => [ 'shape' => 'ResourceName', ], ], ], 'DiskMapList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiskMap', ], ], 'DiskSnapshot' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'sizeInGb' => [ 'shape' => 'integer', ], 'state' => [ 'shape' => 'DiskSnapshotState', ], 'progress' => [ 'shape' => 'string', ], 'fromDiskName' => [ 'shape' => 'ResourceName', ], 'fromDiskArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DiskSnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DiskSnapshot', ], ], 'DiskSnapshotState' => [ 'type' => 'string', 'enum' => [ 'pending', 'completed', 'error', 'unknown', ], ], 'DiskState' => [ 'type' => 'string', 'enum' => [ 'pending', 'error', 'available', 'in-use', 'unknown', ], ], 'Domain' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'domainEntries' => [ 'shape' => 'DomainEntryList', ], ], ], 'DomainEntry' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'name' => [ 'shape' => 'DomainName', ], 'target' => [ 'shape' => 'string', ], 'isAlias' => [ 'shape' => 'boolean', ], 'type' => [ 'shape' => 'DomainEntryType', ], 'options' => [ 'shape' => 'DomainEntryOptions', 'deprecated' => true, ], ], ], 'DomainEntryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainEntry', ], ], 'DomainEntryOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'DomainEntryOptionsKeys', ], 'value' => [ 'shape' => 'string', ], ], 'DomainEntryOptionsKeys' => [ 'type' => 'string', ], 'DomainEntryType' => [ 'type' => 'string', ], 'DomainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Domain', ], ], 'DomainName' => [ 'type' => 'string', ], 'DomainNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainName', ], ], 'DownloadDefaultKeyPairRequest' => [ 'type' => 'structure', 'members' => [], ], 'DownloadDefaultKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'publicKeyBase64' => [ 'shape' => 'Base64', ], 'privateKeyBase64' => [ 'shape' => 'Base64', ], ], ], 'GetActiveNamesRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetActiveNamesResult' => [ 'type' => 'structure', 'members' => [ 'activeNames' => [ 'shape' => 'StringList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetBlueprintsRequest' => [ 'type' => 'structure', 'members' => [ 'includeInactive' => [ 'shape' => 'boolean', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetBlueprintsResult' => [ 'type' => 'structure', 'members' => [ 'blueprints' => [ 'shape' => 'BlueprintList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetBundlesRequest' => [ 'type' => 'structure', 'members' => [ 'includeInactive' => [ 'shape' => 'boolean', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetBundlesResult' => [ 'type' => 'structure', 'members' => [ 'bundles' => [ 'shape' => 'BundleList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetDiskRequest' => [ 'type' => 'structure', 'required' => [ 'diskName', ], 'members' => [ 'diskName' => [ 'shape' => 'ResourceName', ], ], ], 'GetDiskResult' => [ 'type' => 'structure', 'members' => [ 'disk' => [ 'shape' => 'Disk', ], ], ], 'GetDiskSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'diskSnapshotName', ], 'members' => [ 'diskSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'GetDiskSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'diskSnapshot' => [ 'shape' => 'DiskSnapshot', ], ], ], 'GetDiskSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetDiskSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'diskSnapshots' => [ 'shape' => 'DiskSnapshotList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetDisksRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetDisksResult' => [ 'type' => 'structure', 'members' => [ 'disks' => [ 'shape' => 'DiskList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetDomainRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], ], ], 'GetDomainResult' => [ 'type' => 'structure', 'members' => [ 'domain' => [ 'shape' => 'Domain', ], ], ], 'GetDomainsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetDomainsResult' => [ 'type' => 'structure', 'members' => [ 'domains' => [ 'shape' => 'DomainList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetInstanceAccessDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'protocol' => [ 'shape' => 'InstanceAccessProtocol', ], ], ], 'GetInstanceAccessDetailsResult' => [ 'type' => 'structure', 'members' => [ 'accessDetails' => [ 'shape' => 'InstanceAccessDetails', ], ], ], 'GetInstanceMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', 'metricName', 'period', 'startTime', 'endTime', 'unit', 'statistics', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'metricName' => [ 'shape' => 'InstanceMetricName', ], 'period' => [ 'shape' => 'MetricPeriod', ], 'startTime' => [ 'shape' => 'timestamp', ], 'endTime' => [ 'shape' => 'timestamp', ], 'unit' => [ 'shape' => 'MetricUnit', ], 'statistics' => [ 'shape' => 'MetricStatisticList', ], ], ], 'GetInstanceMetricDataResult' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'InstanceMetricName', ], 'metricData' => [ 'shape' => 'MetricDatapointList', ], ], ], 'GetInstancePortStatesRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetInstancePortStatesResult' => [ 'type' => 'structure', 'members' => [ 'portStates' => [ 'shape' => 'InstancePortStateList', ], ], ], 'GetInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetInstanceResult' => [ 'type' => 'structure', 'members' => [ 'instance' => [ 'shape' => 'Instance', ], ], ], 'GetInstanceSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'instanceSnapshotName', ], 'members' => [ 'instanceSnapshotName' => [ 'shape' => 'ResourceName', ], ], ], 'GetInstanceSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'instanceSnapshot' => [ 'shape' => 'InstanceSnapshot', ], ], ], 'GetInstanceSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetInstanceSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'instanceSnapshots' => [ 'shape' => 'InstanceSnapshotList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetInstanceStateRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'GetInstanceStateResult' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'InstanceState', ], ], ], 'GetInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetInstancesResult' => [ 'type' => 'structure', 'members' => [ 'instances' => [ 'shape' => 'InstanceList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'keyPairName', ], 'members' => [ 'keyPairName' => [ 'shape' => 'ResourceName', ], ], ], 'GetKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'keyPair' => [ 'shape' => 'KeyPair', ], ], ], 'GetKeyPairsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetKeyPairsResult' => [ 'type' => 'structure', 'members' => [ 'keyPairs' => [ 'shape' => 'KeyPairList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetLoadBalancerMetricDataRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'metricName', 'period', 'startTime', 'endTime', 'unit', 'statistics', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'metricName' => [ 'shape' => 'LoadBalancerMetricName', ], 'period' => [ 'shape' => 'MetricPeriod', ], 'startTime' => [ 'shape' => 'timestamp', ], 'endTime' => [ 'shape' => 'timestamp', ], 'unit' => [ 'shape' => 'MetricUnit', ], 'statistics' => [ 'shape' => 'MetricStatisticList', ], ], ], 'GetLoadBalancerMetricDataResult' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'LoadBalancerMetricName', ], 'metricData' => [ 'shape' => 'MetricDatapointList', ], ], ], 'GetLoadBalancerRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], ], ], 'GetLoadBalancerResult' => [ 'type' => 'structure', 'members' => [ 'loadBalancer' => [ 'shape' => 'LoadBalancer', ], ], ], 'GetLoadBalancerTlsCertificatesRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], ], ], 'GetLoadBalancerTlsCertificatesResult' => [ 'type' => 'structure', 'members' => [ 'tlsCertificates' => [ 'shape' => 'LoadBalancerTlsCertificateList', ], ], ], 'GetLoadBalancersRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetLoadBalancersResult' => [ 'type' => 'structure', 'members' => [ 'loadBalancers' => [ 'shape' => 'LoadBalancerList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetOperationRequest' => [ 'type' => 'structure', 'required' => [ 'operationId', ], 'members' => [ 'operationId' => [ 'shape' => 'NonEmptyString', ], ], ], 'GetOperationResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'GetOperationsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceName', ], 'members' => [ 'resourceName' => [ 'shape' => 'ResourceName', ], 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetOperationsForResourceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], 'nextPageCount' => [ 'shape' => 'string', 'deprecated' => true, ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetOperationsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetOperationsResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'GetRegionsRequest' => [ 'type' => 'structure', 'members' => [ 'includeAvailabilityZones' => [ 'shape' => 'boolean', ], ], ], 'GetRegionsResult' => [ 'type' => 'structure', 'members' => [ 'regions' => [ 'shape' => 'RegionList', ], ], ], 'GetStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], ], ], 'GetStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'staticIp' => [ 'shape' => 'StaticIp', ], ], ], 'GetStaticIpsRequest' => [ 'type' => 'structure', 'members' => [ 'pageToken' => [ 'shape' => 'string', ], ], ], 'GetStaticIpsResult' => [ 'type' => 'structure', 'members' => [ 'staticIps' => [ 'shape' => 'StaticIpList', ], 'nextPageToken' => [ 'shape' => 'string', ], ], ], 'ImportKeyPairRequest' => [ 'type' => 'structure', 'required' => [ 'keyPairName', 'publicKeyBase64', ], 'members' => [ 'keyPairName' => [ 'shape' => 'ResourceName', ], 'publicKeyBase64' => [ 'shape' => 'Base64', ], ], ], 'ImportKeyPairResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'Instance' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'blueprintId' => [ 'shape' => 'NonEmptyString', ], 'blueprintName' => [ 'shape' => 'NonEmptyString', ], 'bundleId' => [ 'shape' => 'NonEmptyString', ], 'isStaticIp' => [ 'shape' => 'boolean', ], 'privateIpAddress' => [ 'shape' => 'IpAddress', ], 'publicIpAddress' => [ 'shape' => 'IpAddress', ], 'ipv6Address' => [ 'shape' => 'IpV6Address', ], 'hardware' => [ 'shape' => 'InstanceHardware', ], 'networking' => [ 'shape' => 'InstanceNetworking', ], 'state' => [ 'shape' => 'InstanceState', ], 'username' => [ 'shape' => 'NonEmptyString', ], 'sshKeyName' => [ 'shape' => 'ResourceName', ], ], ], 'InstanceAccessDetails' => [ 'type' => 'structure', 'members' => [ 'certKey' => [ 'shape' => 'string', ], 'expiresAt' => [ 'shape' => 'IsoDate', ], 'ipAddress' => [ 'shape' => 'IpAddress', ], 'password' => [ 'shape' => 'string', ], 'passwordData' => [ 'shape' => 'PasswordData', ], 'privateKey' => [ 'shape' => 'string', ], 'protocol' => [ 'shape' => 'InstanceAccessProtocol', ], 'instanceName' => [ 'shape' => 'ResourceName', ], 'username' => [ 'shape' => 'string', ], ], ], 'InstanceAccessProtocol' => [ 'type' => 'string', 'enum' => [ 'ssh', 'rdp', ], ], 'InstanceHardware' => [ 'type' => 'structure', 'members' => [ 'cpuCount' => [ 'shape' => 'integer', ], 'disks' => [ 'shape' => 'DiskList', ], 'ramSizeInGb' => [ 'shape' => 'float', ], ], ], 'InstanceHealthReason' => [ 'type' => 'string', 'enum' => [ 'Lb.RegistrationInProgress', 'Lb.InitialHealthChecking', 'Lb.InternalError', 'Instance.ResponseCodeMismatch', 'Instance.Timeout', 'Instance.FailedHealthChecks', 'Instance.NotRegistered', 'Instance.NotInUse', 'Instance.DeregistrationInProgress', 'Instance.InvalidState', 'Instance.IpUnusable', ], ], 'InstanceHealthState' => [ 'type' => 'string', 'enum' => [ 'initial', 'healthy', 'unhealthy', 'unused', 'draining', 'unavailable', ], ], 'InstanceHealthSummary' => [ 'type' => 'structure', 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'instanceHealth' => [ 'shape' => 'InstanceHealthState', ], 'instanceHealthReason' => [ 'shape' => 'InstanceHealthReason', ], ], ], 'InstanceHealthSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceHealthSummary', ], ], 'InstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Instance', ], ], 'InstanceMetricName' => [ 'type' => 'string', 'enum' => [ 'CPUUtilization', 'NetworkIn', 'NetworkOut', 'StatusCheckFailed', 'StatusCheckFailed_Instance', 'StatusCheckFailed_System', ], ], 'InstanceNetworking' => [ 'type' => 'structure', 'members' => [ 'monthlyTransfer' => [ 'shape' => 'MonthlyTransfer', ], 'ports' => [ 'shape' => 'InstancePortInfoList', ], ], ], 'InstancePlatform' => [ 'type' => 'string', 'enum' => [ 'LINUX_UNIX', 'WINDOWS', ], ], 'InstancePlatformList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePlatform', ], ], 'InstancePortInfo' => [ 'type' => 'structure', 'members' => [ 'fromPort' => [ 'shape' => 'Port', ], 'toPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'NetworkProtocol', ], 'accessFrom' => [ 'shape' => 'string', ], 'accessType' => [ 'shape' => 'PortAccessType', ], 'commonName' => [ 'shape' => 'string', ], 'accessDirection' => [ 'shape' => 'AccessDirection', ], ], ], 'InstancePortInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePortInfo', ], ], 'InstancePortState' => [ 'type' => 'structure', 'members' => [ 'fromPort' => [ 'shape' => 'Port', ], 'toPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'NetworkProtocol', ], 'state' => [ 'shape' => 'PortState', ], ], ], 'InstancePortStateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstancePortState', ], ], 'InstanceSnapshot' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'state' => [ 'shape' => 'InstanceSnapshotState', ], 'progress' => [ 'shape' => 'string', ], 'fromAttachedDisks' => [ 'shape' => 'DiskList', ], 'fromInstanceName' => [ 'shape' => 'ResourceName', ], 'fromInstanceArn' => [ 'shape' => 'NonEmptyString', ], 'fromBlueprintId' => [ 'shape' => 'string', ], 'fromBundleId' => [ 'shape' => 'string', ], 'sizeInGb' => [ 'shape' => 'integer', ], ], ], 'InstanceSnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceSnapshot', ], ], 'InstanceSnapshotState' => [ 'type' => 'string', 'enum' => [ 'pending', 'error', 'available', ], ], 'InstanceState' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'integer', ], 'name' => [ 'shape' => 'string', ], ], ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'IpAddress' => [ 'type' => 'string', 'pattern' => '([0-9]{1,3}\\.){3}[0-9]{1,3}', ], 'IpV6Address' => [ 'type' => 'string', 'pattern' => '([A-F0-9]{1,4}:){7}[A-F0-9]{1,4}', ], 'IsVpcPeeredRequest' => [ 'type' => 'structure', 'members' => [], ], 'IsVpcPeeredResult' => [ 'type' => 'structure', 'members' => [ 'isPeered' => [ 'shape' => 'boolean', ], ], ], 'IsoDate' => [ 'type' => 'timestamp', ], 'KeyPair' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'fingerprint' => [ 'shape' => 'Base64', ], ], ], 'KeyPairList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyPair', ], ], 'LoadBalancer' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'dnsName' => [ 'shape' => 'NonEmptyString', ], 'state' => [ 'shape' => 'LoadBalancerState', ], 'protocol' => [ 'shape' => 'LoadBalancerProtocol', ], 'publicPorts' => [ 'shape' => 'PortList', ], 'healthCheckPath' => [ 'shape' => 'NonEmptyString', ], 'instancePort' => [ 'shape' => 'integer', ], 'instanceHealthSummary' => [ 'shape' => 'InstanceHealthSummaryList', ], 'tlsCertificateSummaries' => [ 'shape' => 'LoadBalancerTlsCertificateSummaryList', ], 'configurationOptions' => [ 'shape' => 'LoadBalancerConfigurationOptions', ], ], ], 'LoadBalancerAttributeName' => [ 'type' => 'string', 'enum' => [ 'HealthCheckPath', 'SessionStickinessEnabled', 'SessionStickiness_LB_CookieDurationSeconds', ], ], 'LoadBalancerConfigurationOptions' => [ 'type' => 'map', 'key' => [ 'shape' => 'LoadBalancerAttributeName', ], 'value' => [ 'shape' => 'string', ], ], 'LoadBalancerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancer', ], ], 'LoadBalancerMetricName' => [ 'type' => 'string', 'enum' => [ 'ClientTLSNegotiationErrorCount', 'HealthyHostCount', 'UnhealthyHostCount', 'HTTPCode_LB_4XX_Count', 'HTTPCode_LB_5XX_Count', 'HTTPCode_Instance_2XX_Count', 'HTTPCode_Instance_3XX_Count', 'HTTPCode_Instance_4XX_Count', 'HTTPCode_Instance_5XX_Count', 'InstanceResponseTime', 'RejectedConnectionCount', 'RequestCount', ], ], 'LoadBalancerProtocol' => [ 'type' => 'string', 'enum' => [ 'HTTP_HTTPS', 'HTTP', ], ], 'LoadBalancerState' => [ 'type' => 'string', 'enum' => [ 'active', 'provisioning', 'active_impaired', 'failed', 'unknown', ], ], 'LoadBalancerTlsCertificate' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'isAttached' => [ 'shape' => 'boolean', ], 'status' => [ 'shape' => 'LoadBalancerTlsCertificateStatus', ], 'domainName' => [ 'shape' => 'DomainName', ], 'domainValidationRecords' => [ 'shape' => 'LoadBalancerTlsCertificateDomainValidationRecordList', ], 'failureReason' => [ 'shape' => 'LoadBalancerTlsCertificateFailureReason', ], 'issuedAt' => [ 'shape' => 'IsoDate', ], 'issuer' => [ 'shape' => 'NonEmptyString', ], 'keyAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'notAfter' => [ 'shape' => 'IsoDate', ], 'notBefore' => [ 'shape' => 'IsoDate', ], 'renewalSummary' => [ 'shape' => 'LoadBalancerTlsCertificateRenewalSummary', ], 'revocationReason' => [ 'shape' => 'LoadBalancerTlsCertificateRevocationReason', ], 'revokedAt' => [ 'shape' => 'IsoDate', ], 'serial' => [ 'shape' => 'NonEmptyString', ], 'signatureAlgorithm' => [ 'shape' => 'NonEmptyString', ], 'subject' => [ 'shape' => 'NonEmptyString', ], 'subjectAlternativeNames' => [ 'shape' => 'StringList', ], ], ], 'LoadBalancerTlsCertificateDomainStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_VALIDATION', 'FAILED', 'SUCCESS', ], ], 'LoadBalancerTlsCertificateDomainValidationOption' => [ 'type' => 'structure', 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'validationStatus' => [ 'shape' => 'LoadBalancerTlsCertificateDomainStatus', ], ], ], 'LoadBalancerTlsCertificateDomainValidationOptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTlsCertificateDomainValidationOption', ], ], 'LoadBalancerTlsCertificateDomainValidationRecord' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'NonEmptyString', ], 'validationStatus' => [ 'shape' => 'LoadBalancerTlsCertificateDomainStatus', ], 'domainName' => [ 'shape' => 'DomainName', ], ], ], 'LoadBalancerTlsCertificateDomainValidationRecordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTlsCertificateDomainValidationRecord', ], ], 'LoadBalancerTlsCertificateFailureReason' => [ 'type' => 'string', 'enum' => [ 'NO_AVAILABLE_CONTACTS', 'ADDITIONAL_VERIFICATION_REQUIRED', 'DOMAIN_NOT_ALLOWED', 'INVALID_PUBLIC_DOMAIN', 'OTHER', ], ], 'LoadBalancerTlsCertificateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTlsCertificate', ], ], 'LoadBalancerTlsCertificateRenewalStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_AUTO_RENEWAL', 'PENDING_VALIDATION', 'SUCCESS', 'FAILED', ], ], 'LoadBalancerTlsCertificateRenewalSummary' => [ 'type' => 'structure', 'members' => [ 'renewalStatus' => [ 'shape' => 'LoadBalancerTlsCertificateRenewalStatus', ], 'domainValidationOptions' => [ 'shape' => 'LoadBalancerTlsCertificateDomainValidationOptionList', ], ], ], 'LoadBalancerTlsCertificateRevocationReason' => [ 'type' => 'string', 'enum' => [ 'UNSPECIFIED', 'KEY_COMPROMISE', 'CA_COMPROMISE', 'AFFILIATION_CHANGED', 'SUPERCEDED', 'CESSATION_OF_OPERATION', 'CERTIFICATE_HOLD', 'REMOVE_FROM_CRL', 'PRIVILEGE_WITHDRAWN', 'A_A_COMPROMISE', ], ], 'LoadBalancerTlsCertificateStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING_VALIDATION', 'ISSUED', 'INACTIVE', 'EXPIRED', 'VALIDATION_TIMED_OUT', 'REVOKED', 'FAILED', 'UNKNOWN', ], ], 'LoadBalancerTlsCertificateSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'isAttached' => [ 'shape' => 'boolean', ], ], ], 'LoadBalancerTlsCertificateSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LoadBalancerTlsCertificateSummary', ], ], 'MetricDatapoint' => [ 'type' => 'structure', 'members' => [ 'average' => [ 'shape' => 'double', ], 'maximum' => [ 'shape' => 'double', ], 'minimum' => [ 'shape' => 'double', ], 'sampleCount' => [ 'shape' => 'double', ], 'sum' => [ 'shape' => 'double', ], 'timestamp' => [ 'shape' => 'timestamp', ], 'unit' => [ 'shape' => 'MetricUnit', ], ], ], 'MetricDatapointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDatapoint', ], ], 'MetricPeriod' => [ 'type' => 'integer', 'max' => 86400, 'min' => 60, ], 'MetricStatistic' => [ 'type' => 'string', 'enum' => [ 'Minimum', 'Maximum', 'Sum', 'Average', 'SampleCount', ], ], 'MetricStatisticList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricStatistic', ], ], 'MetricUnit' => [ 'type' => 'string', 'enum' => [ 'Seconds', 'Microseconds', 'Milliseconds', 'Bytes', 'Kilobytes', 'Megabytes', 'Gigabytes', 'Terabytes', 'Bits', 'Kilobits', 'Megabits', 'Gigabits', 'Terabits', 'Percent', 'Count', 'Bytes/Second', 'Kilobytes/Second', 'Megabytes/Second', 'Gigabytes/Second', 'Terabytes/Second', 'Bits/Second', 'Kilobits/Second', 'Megabits/Second', 'Gigabits/Second', 'Terabits/Second', 'Count/Second', 'None', ], ], 'MonthlyTransfer' => [ 'type' => 'structure', 'members' => [ 'gbPerMonthAllocated' => [ 'shape' => 'integer', ], ], ], 'NetworkProtocol' => [ 'type' => 'string', 'enum' => [ 'tcp', 'all', 'udp', ], ], 'NonEmptyString' => [ 'type' => 'string', 'pattern' => '.*\\S.*', ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'OpenInstancePublicPortsRequest' => [ 'type' => 'structure', 'required' => [ 'portInfo', 'instanceName', ], 'members' => [ 'portInfo' => [ 'shape' => 'PortInfo', ], 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'OpenInstancePublicPortsResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'Operation' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'resourceName' => [ 'shape' => 'ResourceName', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'isTerminal' => [ 'shape' => 'boolean', ], 'operationDetails' => [ 'shape' => 'string', ], 'operationType' => [ 'shape' => 'OperationType', ], 'status' => [ 'shape' => 'OperationStatus', ], 'statusChangedAt' => [ 'shape' => 'IsoDate', ], 'errorCode' => [ 'shape' => 'string', ], 'errorDetails' => [ 'shape' => 'string', ], ], ], 'OperationFailureException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'OperationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Operation', ], ], 'OperationStatus' => [ 'type' => 'string', 'enum' => [ 'NotStarted', 'Started', 'Failed', 'Completed', 'Succeeded', ], ], 'OperationType' => [ 'type' => 'string', 'enum' => [ 'DeleteInstance', 'CreateInstance', 'StopInstance', 'StartInstance', 'RebootInstance', 'OpenInstancePublicPorts', 'PutInstancePublicPorts', 'CloseInstancePublicPorts', 'AllocateStaticIp', 'ReleaseStaticIp', 'AttachStaticIp', 'DetachStaticIp', 'UpdateDomainEntry', 'DeleteDomainEntry', 'CreateDomain', 'DeleteDomain', 'CreateInstanceSnapshot', 'DeleteInstanceSnapshot', 'CreateInstancesFromSnapshot', 'CreateLoadBalancer', 'DeleteLoadBalancer', 'AttachInstancesToLoadBalancer', 'DetachInstancesFromLoadBalancer', 'UpdateLoadBalancerAttribute', 'CreateLoadBalancerTlsCertificate', 'DeleteLoadBalancerTlsCertificate', 'AttachLoadBalancerTlsCertificate', 'CreateDisk', 'DeleteDisk', 'AttachDisk', 'DetachDisk', 'CreateDiskSnapshot', 'DeleteDiskSnapshot', 'CreateDiskFromSnapshot', ], ], 'PasswordData' => [ 'type' => 'structure', 'members' => [ 'ciphertext' => [ 'shape' => 'string', ], 'keyPairName' => [ 'shape' => 'ResourceName', ], ], ], 'PeerVpcRequest' => [ 'type' => 'structure', 'members' => [], ], 'PeerVpcResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'Port' => [ 'type' => 'integer', 'max' => 65535, 'min' => 0, ], 'PortAccessType' => [ 'type' => 'string', 'enum' => [ 'Public', 'Private', ], ], 'PortInfo' => [ 'type' => 'structure', 'members' => [ 'fromPort' => [ 'shape' => 'Port', ], 'toPort' => [ 'shape' => 'Port', ], 'protocol' => [ 'shape' => 'NetworkProtocol', ], ], ], 'PortInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PortInfo', ], ], 'PortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Port', ], ], 'PortState' => [ 'type' => 'string', 'enum' => [ 'open', 'closed', ], ], 'PutInstancePublicPortsRequest' => [ 'type' => 'structure', 'required' => [ 'portInfos', 'instanceName', ], 'members' => [ 'portInfos' => [ 'shape' => 'PortInfoList', ], 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'PutInstancePublicPortsResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'RebootInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'RebootInstanceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'Region' => [ 'type' => 'structure', 'members' => [ 'continentCode' => [ 'shape' => 'string', ], 'description' => [ 'shape' => 'string', ], 'displayName' => [ 'shape' => 'string', ], 'name' => [ 'shape' => 'RegionName', ], 'availabilityZones' => [ 'shape' => 'AvailabilityZoneList', ], ], ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'RegionName' => [ 'type' => 'string', 'enum' => [ 'us-east-1', 'us-east-2', 'us-west-1', 'us-west-2', 'eu-central-1', 'eu-west-1', 'eu-west-2', 'ap-south-1', 'ap-southeast-1', 'ap-southeast-2', 'ap-northeast-1', 'ap-northeast-2', ], ], 'ReleaseStaticIpRequest' => [ 'type' => 'structure', 'required' => [ 'staticIpName', ], 'members' => [ 'staticIpName' => [ 'shape' => 'ResourceName', ], ], ], 'ReleaseStaticIpResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'ResourceLocation' => [ 'type' => 'structure', 'members' => [ 'availabilityZone' => [ 'shape' => 'string', ], 'regionName' => [ 'shape' => 'RegionName', ], ], ], 'ResourceName' => [ 'type' => 'string', 'pattern' => '\\w[\\w\\-]*\\w', ], 'ResourceNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceName', ], ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'Instance', 'StaticIp', 'KeyPair', 'InstanceSnapshot', 'Domain', 'PeeredVpc', 'LoadBalancer', 'LoadBalancerTlsCertificate', 'Disk', 'DiskSnapshot', ], ], 'ServiceException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, 'fault' => true, ], 'StartInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], ], ], 'StartInstanceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'StaticIp' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'supportCode' => [ 'shape' => 'string', ], 'createdAt' => [ 'shape' => 'IsoDate', ], 'location' => [ 'shape' => 'ResourceLocation', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'ipAddress' => [ 'shape' => 'IpAddress', ], 'attachedTo' => [ 'shape' => 'ResourceName', ], 'isAttached' => [ 'shape' => 'boolean', ], ], ], 'StaticIpList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StaticIp', ], ], 'StopInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'instanceName', ], 'members' => [ 'instanceName' => [ 'shape' => 'ResourceName', ], 'force' => [ 'shape' => 'boolean', ], ], ], 'StopInstanceResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], ], 'StringMax256' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'UnauthenticatedException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'string', ], 'docs' => [ 'shape' => 'string', ], 'message' => [ 'shape' => 'string', ], 'tip' => [ 'shape' => 'string', ], ], 'exception' => true, ], 'UnpeerVpcRequest' => [ 'type' => 'structure', 'members' => [], ], 'UnpeerVpcResult' => [ 'type' => 'structure', 'members' => [ 'operation' => [ 'shape' => 'Operation', ], ], ], 'UpdateDomainEntryRequest' => [ 'type' => 'structure', 'required' => [ 'domainName', 'domainEntry', ], 'members' => [ 'domainName' => [ 'shape' => 'DomainName', ], 'domainEntry' => [ 'shape' => 'DomainEntry', ], ], ], 'UpdateDomainEntryResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'UpdateLoadBalancerAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'loadBalancerName', 'attributeName', 'attributeValue', ], 'members' => [ 'loadBalancerName' => [ 'shape' => 'ResourceName', ], 'attributeName' => [ 'shape' => 'LoadBalancerAttributeName', ], 'attributeValue' => [ 'shape' => 'StringMax256', ], ], ], 'UpdateLoadBalancerAttributeResult' => [ 'type' => 'structure', 'members' => [ 'operations' => [ 'shape' => 'OperationList', ], ], ], 'boolean' => [ 'type' => 'boolean', ], 'double' => [ 'type' => 'double', ], 'float' => [ 'type' => 'float', ], 'integer' => [ 'type' => 'integer', ], 'string' => [ 'type' => 'string', ], 'timestamp' => [ 'type' => 'timestamp', ], ],];
