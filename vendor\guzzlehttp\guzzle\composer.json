{"name": "guzzlehttp/guzzle", "type": "library", "description": "Guzzle is a PHP HTTP client library", "keywords": ["framework", "http", "rest", "web service", "curl", "client", "HTTP client"], "homepage": "http://guzzlephp.org/", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "require": {"php": ">=5.5", "guzzlehttp/psr7": "^1.4", "guzzlehttp/promises": "^1.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "autoload-dev": {"psr-4": {"GuzzleHttp\\Tests\\": "tests/"}}, "suggest": {"psr/log": "Required for using the Log middleware"}, "extra": {"branch-alias": {"dev-master": "6.3-dev"}}}