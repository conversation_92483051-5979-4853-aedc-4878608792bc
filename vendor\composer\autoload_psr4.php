<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'RedNovaLabs\\Storedge\\' => array($vendorDir . '/rednovalabs/storedge-sdk-php/src/RedNovaLabs/Storedge'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-message/src'),
    'JmesPath\\' => array($vendorDir . '/mtdowling/jmespath.php/src'),
    'GuzzleHttp\\Subscriber\\Oauth\\' => array($vendorDir . '/guzzlehttp/oauth-subscriber/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'Aws\\' => array($vendorDir . '/aws/aws-sdk-php/src'),
);
