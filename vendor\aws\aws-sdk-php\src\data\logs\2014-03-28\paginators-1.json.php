<?php
// This file was auto-generated from sdk-root/src/data/logs/2014-03-28/paginators-1.json
return [ 'pagination' => [ 'DescribeDestinations' => [ 'input_token' => 'nextToken', 'limit_key' => 'limit', 'output_token' => 'nextToken', 'result_key' => 'destinations', ], 'DescribeLogGroups' => [ 'input_token' => 'nextToken', 'limit_key' => 'limit', 'output_token' => 'nextToken', 'result_key' => 'logGroups', ], 'DescribeLogStreams' => [ 'input_token' => 'nextToken', 'limit_key' => 'limit', 'output_token' => 'nextToken', 'result_key' => 'logStreams', ], 'DescribeMetricFilters' => [ 'input_token' => 'nextToken', 'limit_key' => 'limit', 'output_token' => 'nextToken', 'result_key' => 'metricFilters', ], 'DescribeSubscriptionFilters' => [ 'input_token' => 'nextToken', 'limit_key' => 'limit', 'output_token' => 'nextToken', 'result_key' => 'subscriptionFilters', ], 'FilterLogEvents' => [ 'input_token' => 'nextToken', 'limit_key' => 'limit', 'output_token' => 'nextToken', 'result_key' => [ 'events', 'searchedLogStreams', ], ], 'GetLogEvents' => [ 'input_token' => 'nextToken', 'limit_key' => 'limit', 'output_token' => 'nextForwardToken', 'result_key' => 'events', ], ],];
