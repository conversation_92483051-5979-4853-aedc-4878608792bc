<?php
// This file was auto-generated from sdk-root/src/data/cloudformation/2010-05-15/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2010-05-15', 'endpointPrefix' => 'cloudformation', 'protocol' => 'query', 'serviceFullName' => 'AWS CloudFormation', 'serviceId' => 'CloudFormation', 'signatureVersion' => 'v4', 'uid' => 'cloudformation-2010-05-15', 'xmlNamespace' => 'http://cloudformation.amazonaws.com/doc/2010-05-15/', ], 'operations' => [ 'CancelUpdateStack' => [ 'name' => 'CancelUpdateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelUpdateStackInput', ], 'errors' => [ [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'ContinueUpdateRollback' => [ 'name' => 'ContinueUpdateRollback', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ContinueUpdateRollbackInput', ], 'output' => [ 'shape' => 'ContinueUpdateRollbackOutput', 'resultWrapper' => 'ContinueUpdateRollbackResult', ], 'errors' => [ [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'CreateChangeSet' => [ 'name' => 'CreateChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateChangeSetInput', ], 'output' => [ 'shape' => 'CreateChangeSetOutput', 'resultWrapper' => 'CreateChangeSetResult', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'InsufficientCapabilitiesException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateStack' => [ 'name' => 'CreateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStackInput', ], 'output' => [ 'shape' => 'CreateStackOutput', 'resultWrapper' => 'CreateStackResult', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'TokenAlreadyExistsException', ], [ 'shape' => 'InsufficientCapabilitiesException', ], ], ], 'CreateStackInstances' => [ 'name' => 'CreateStackInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStackInstancesInput', ], 'output' => [ 'shape' => 'CreateStackInstancesOutput', 'resultWrapper' => 'CreateStackInstancesResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'OperationIdAlreadyExistsException', ], [ 'shape' => 'StaleRequestException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateStackSet' => [ 'name' => 'CreateStackSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStackSetInput', ], 'output' => [ 'shape' => 'CreateStackSetOutput', 'resultWrapper' => 'CreateStackSetResult', ], 'errors' => [ [ 'shape' => 'NameAlreadyExistsException', ], [ 'shape' => 'CreatedButModifiedException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DeleteChangeSet' => [ 'name' => 'DeleteChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteChangeSetInput', ], 'output' => [ 'shape' => 'DeleteChangeSetOutput', 'resultWrapper' => 'DeleteChangeSetResult', ], 'errors' => [ [ 'shape' => 'InvalidChangeSetStatusException', ], ], ], 'DeleteStack' => [ 'name' => 'DeleteStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStackInput', ], 'errors' => [ [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'DeleteStackInstances' => [ 'name' => 'DeleteStackInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStackInstancesInput', ], 'output' => [ 'shape' => 'DeleteStackInstancesOutput', 'resultWrapper' => 'DeleteStackInstancesResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'OperationIdAlreadyExistsException', ], [ 'shape' => 'StaleRequestException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'DeleteStackSet' => [ 'name' => 'DeleteStackSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteStackSetInput', ], 'output' => [ 'shape' => 'DeleteStackSetOutput', 'resultWrapper' => 'DeleteStackSetResult', ], 'errors' => [ [ 'shape' => 'StackSetNotEmptyException', ], [ 'shape' => 'OperationInProgressException', ], ], ], 'DescribeAccountLimits' => [ 'name' => 'DescribeAccountLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountLimitsInput', ], 'output' => [ 'shape' => 'DescribeAccountLimitsOutput', 'resultWrapper' => 'DescribeAccountLimitsResult', ], ], 'DescribeChangeSet' => [ 'name' => 'DescribeChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeChangeSetInput', ], 'output' => [ 'shape' => 'DescribeChangeSetOutput', 'resultWrapper' => 'DescribeChangeSetResult', ], 'errors' => [ [ 'shape' => 'ChangeSetNotFoundException', ], ], ], 'DescribeStackEvents' => [ 'name' => 'DescribeStackEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackEventsInput', ], 'output' => [ 'shape' => 'DescribeStackEventsOutput', 'resultWrapper' => 'DescribeStackEventsResult', ], ], 'DescribeStackInstance' => [ 'name' => 'DescribeStackInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackInstanceInput', ], 'output' => [ 'shape' => 'DescribeStackInstanceOutput', 'resultWrapper' => 'DescribeStackInstanceResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'StackInstanceNotFoundException', ], ], ], 'DescribeStackResource' => [ 'name' => 'DescribeStackResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackResourceInput', ], 'output' => [ 'shape' => 'DescribeStackResourceOutput', 'resultWrapper' => 'DescribeStackResourceResult', ], ], 'DescribeStackResources' => [ 'name' => 'DescribeStackResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackResourcesInput', ], 'output' => [ 'shape' => 'DescribeStackResourcesOutput', 'resultWrapper' => 'DescribeStackResourcesResult', ], ], 'DescribeStackSet' => [ 'name' => 'DescribeStackSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackSetInput', ], 'output' => [ 'shape' => 'DescribeStackSetOutput', 'resultWrapper' => 'DescribeStackSetResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], ], ], 'DescribeStackSetOperation' => [ 'name' => 'DescribeStackSetOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStackSetOperationInput', ], 'output' => [ 'shape' => 'DescribeStackSetOperationOutput', 'resultWrapper' => 'DescribeStackSetOperationResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationNotFoundException', ], ], ], 'DescribeStacks' => [ 'name' => 'DescribeStacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeStacksInput', ], 'output' => [ 'shape' => 'DescribeStacksOutput', 'resultWrapper' => 'DescribeStacksResult', ], ], 'EstimateTemplateCost' => [ 'name' => 'EstimateTemplateCost', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EstimateTemplateCostInput', ], 'output' => [ 'shape' => 'EstimateTemplateCostOutput', 'resultWrapper' => 'EstimateTemplateCostResult', ], ], 'ExecuteChangeSet' => [ 'name' => 'ExecuteChangeSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExecuteChangeSetInput', ], 'output' => [ 'shape' => 'ExecuteChangeSetOutput', 'resultWrapper' => 'ExecuteChangeSetResult', ], 'errors' => [ [ 'shape' => 'InvalidChangeSetStatusException', ], [ 'shape' => 'ChangeSetNotFoundException', ], [ 'shape' => 'InsufficientCapabilitiesException', ], [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'GetStackPolicy' => [ 'name' => 'GetStackPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetStackPolicyInput', ], 'output' => [ 'shape' => 'GetStackPolicyOutput', 'resultWrapper' => 'GetStackPolicyResult', ], ], 'GetTemplate' => [ 'name' => 'GetTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTemplateInput', ], 'output' => [ 'shape' => 'GetTemplateOutput', 'resultWrapper' => 'GetTemplateResult', ], 'errors' => [ [ 'shape' => 'ChangeSetNotFoundException', ], ], ], 'GetTemplateSummary' => [ 'name' => 'GetTemplateSummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTemplateSummaryInput', ], 'output' => [ 'shape' => 'GetTemplateSummaryOutput', 'resultWrapper' => 'GetTemplateSummaryResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], ], ], 'ListChangeSets' => [ 'name' => 'ListChangeSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListChangeSetsInput', ], 'output' => [ 'shape' => 'ListChangeSetsOutput', 'resultWrapper' => 'ListChangeSetsResult', ], ], 'ListExports' => [ 'name' => 'ListExports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListExportsInput', ], 'output' => [ 'shape' => 'ListExportsOutput', 'resultWrapper' => 'ListExportsResult', ], ], 'ListImports' => [ 'name' => 'ListImports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListImportsInput', ], 'output' => [ 'shape' => 'ListImportsOutput', 'resultWrapper' => 'ListImportsResult', ], ], 'ListStackInstances' => [ 'name' => 'ListStackInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackInstancesInput', ], 'output' => [ 'shape' => 'ListStackInstancesOutput', 'resultWrapper' => 'ListStackInstancesResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], ], ], 'ListStackResources' => [ 'name' => 'ListStackResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackResourcesInput', ], 'output' => [ 'shape' => 'ListStackResourcesOutput', 'resultWrapper' => 'ListStackResourcesResult', ], ], 'ListStackSetOperationResults' => [ 'name' => 'ListStackSetOperationResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackSetOperationResultsInput', ], 'output' => [ 'shape' => 'ListStackSetOperationResultsOutput', 'resultWrapper' => 'ListStackSetOperationResultsResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationNotFoundException', ], ], ], 'ListStackSetOperations' => [ 'name' => 'ListStackSetOperations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackSetOperationsInput', ], 'output' => [ 'shape' => 'ListStackSetOperationsOutput', 'resultWrapper' => 'ListStackSetOperationsResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], ], ], 'ListStackSets' => [ 'name' => 'ListStackSets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStackSetsInput', ], 'output' => [ 'shape' => 'ListStackSetsOutput', 'resultWrapper' => 'ListStackSetsResult', ], ], 'ListStacks' => [ 'name' => 'ListStacks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListStacksInput', ], 'output' => [ 'shape' => 'ListStacksOutput', 'resultWrapper' => 'ListStacksResult', ], ], 'SetStackPolicy' => [ 'name' => 'SetStackPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetStackPolicyInput', ], ], 'SignalResource' => [ 'name' => 'SignalResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SignalResourceInput', ], ], 'StopStackSetOperation' => [ 'name' => 'StopStackSetOperation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopStackSetOperationInput', ], 'output' => [ 'shape' => 'StopStackSetOperationOutput', 'resultWrapper' => 'StopStackSetOperationResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationNotFoundException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'UpdateStack' => [ 'name' => 'UpdateStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStackInput', ], 'output' => [ 'shape' => 'UpdateStackOutput', 'resultWrapper' => 'UpdateStackResult', ], 'errors' => [ [ 'shape' => 'InsufficientCapabilitiesException', ], [ 'shape' => 'TokenAlreadyExistsException', ], ], ], 'UpdateStackInstances' => [ 'name' => 'UpdateStackInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStackInstancesInput', ], 'output' => [ 'shape' => 'UpdateStackInstancesOutput', 'resultWrapper' => 'UpdateStackInstancesResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'StackInstanceNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'OperationIdAlreadyExistsException', ], [ 'shape' => 'StaleRequestException', ], [ 'shape' => 'InvalidOperationException', ], ], ], 'UpdateStackSet' => [ 'name' => 'UpdateStackSet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateStackSetInput', ], 'output' => [ 'shape' => 'UpdateStackSetOutput', 'resultWrapper' => 'UpdateStackSetResult', ], 'errors' => [ [ 'shape' => 'StackSetNotFoundException', ], [ 'shape' => 'OperationInProgressException', ], [ 'shape' => 'OperationIdAlreadyExistsException', ], [ 'shape' => 'StaleRequestException', ], [ 'shape' => 'InvalidOperationException', ], [ 'shape' => 'StackInstanceNotFoundException', ], ], ], 'UpdateTerminationProtection' => [ 'name' => 'UpdateTerminationProtection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTerminationProtectionInput', ], 'output' => [ 'shape' => 'UpdateTerminationProtectionOutput', 'resultWrapper' => 'UpdateTerminationProtectionResult', ], ], 'ValidateTemplate' => [ 'name' => 'ValidateTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ValidateTemplateInput', ], 'output' => [ 'shape' => 'ValidateTemplateOutput', 'resultWrapper' => 'ValidateTemplateResult', ], ], ], 'shapes' => [ 'Account' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'AccountGateResult' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'AccountGateStatus', ], 'StatusReason' => [ 'shape' => 'AccountGateStatusReason', ], ], ], 'AccountGateStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'FAILED', 'SKIPPED', ], ], 'AccountGateStatusReason' => [ 'type' => 'string', ], 'AccountLimit' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'LimitName', ], 'Value' => [ 'shape' => 'LimitValue', ], ], ], 'AccountLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountLimit', ], ], 'AccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], ], 'AllowedValue' => [ 'type' => 'string', ], 'AllowedValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'AllowedValue', ], ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AlreadyExistsException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Arn' => [ 'type' => 'string', ], 'CancelUpdateStackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'Capabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Capability', ], ], 'CapabilitiesReason' => [ 'type' => 'string', ], 'Capability' => [ 'type' => 'string', 'enum' => [ 'CAPABILITY_IAM', 'CAPABILITY_NAMED_IAM', ], ], 'CausingEntity' => [ 'type' => 'string', ], 'Change' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ChangeType', ], 'ResourceChange' => [ 'shape' => 'ResourceChange', ], ], ], 'ChangeAction' => [ 'type' => 'string', 'enum' => [ 'Add', 'Modify', 'Remove', ], ], 'ChangeSetId' => [ 'type' => 'string', 'min' => 1, 'pattern' => 'arn:[-a-zA-Z0-9:/]*', ], 'ChangeSetName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z][-a-zA-Z0-9]*', ], 'ChangeSetNameOrId' => [ 'type' => 'string', 'max' => 1600, 'min' => 1, 'pattern' => '[a-zA-Z][-a-zA-Z0-9]*|arn:[-a-zA-Z0-9:/]*', ], 'ChangeSetNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ChangeSetNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ChangeSetStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_COMPLETE', 'DELETE_COMPLETE', 'FAILED', ], ], 'ChangeSetStatusReason' => [ 'type' => 'string', ], 'ChangeSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChangeSetSummary', ], ], 'ChangeSetSummary' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'StackName' => [ 'shape' => 'StackName', ], 'ChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'ExecutionStatus' => [ 'shape' => 'ExecutionStatus', ], 'Status' => [ 'shape' => 'ChangeSetStatus', ], 'StatusReason' => [ 'shape' => 'ChangeSetStatusReason', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'ChangeSetType' => [ 'type' => 'string', 'enum' => [ 'CREATE', 'UPDATE', ], ], 'ChangeSource' => [ 'type' => 'string', 'enum' => [ 'ResourceReference', 'ParameterReference', 'ResourceAttribute', 'DirectModification', 'Automatic', ], ], 'ChangeType' => [ 'type' => 'string', 'enum' => [ 'Resource', ], ], 'Changes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Change', ], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9][-a-zA-Z0-9]*', ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ContinueUpdateRollbackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'ResourcesToSkip' => [ 'shape' => 'ResourcesToSkip', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'ContinueUpdateRollbackOutput' => [ 'type' => 'structure', 'members' => [], ], 'CreateChangeSetInput' => [ 'type' => 'structure', 'required' => [ 'StackName', 'ChangeSetName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'UsePreviousTemplate' => [ 'shape' => 'UsePreviousTemplate', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'Tags' => [ 'shape' => 'Tags', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'ClientToken' => [ 'shape' => 'ClientToken', ], 'Description' => [ 'shape' => 'Description', ], 'ChangeSetType' => [ 'shape' => 'ChangeSetType', ], ], ], 'CreateChangeSetOutput' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'ChangeSetId', ], 'StackId' => [ 'shape' => 'StackId', ], ], ], 'CreateStackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'DisableRollback' => [ 'shape' => 'DisableRollback', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'TimeoutInMinutes' => [ 'shape' => 'TimeoutMinutes', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'OnFailure' => [ 'shape' => 'OnFailure', ], 'StackPolicyBody' => [ 'shape' => 'StackPolicyBody', ], 'StackPolicyURL' => [ 'shape' => 'StackPolicyURL', ], 'Tags' => [ 'shape' => 'Tags', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], 'EnableTerminationProtection' => [ 'shape' => 'EnableTerminationProtection', ], ], ], 'CreateStackInstancesInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'Accounts', 'Regions', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'Accounts' => [ 'shape' => 'AccountList', ], 'Regions' => [ 'shape' => 'RegionList', ], 'ParameterOverrides' => [ 'shape' => 'Parameters', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateStackInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'CreateStackOutput' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], ], ], 'CreateStackSetInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'Description' => [ 'shape' => 'Description', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Tags' => [ 'shape' => 'Tags', ], 'AdministrationRoleARN' => [ 'shape' => 'RoleARN', ], 'ExecutionRoleName' => [ 'shape' => 'ExecutionRoleName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateStackSetOutput' => [ 'type' => 'structure', 'members' => [ 'StackSetId' => [ 'shape' => 'StackSetId', ], ], ], 'CreatedButModifiedException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CreatedButModifiedException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreationTime' => [ 'type' => 'timestamp', ], 'DeleteChangeSetInput' => [ 'type' => 'structure', 'required' => [ 'ChangeSetName', ], 'members' => [ 'ChangeSetName' => [ 'shape' => 'ChangeSetNameOrId', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], ], ], 'DeleteChangeSetOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'RetainResources' => [ 'shape' => 'RetainResources', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'DeleteStackInstancesInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'Accounts', 'Regions', 'RetainStacks', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'Accounts' => [ 'shape' => 'AccountList', ], 'Regions' => [ 'shape' => 'RegionList', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'RetainStacks' => [ 'shape' => 'RetainStacks', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'DeleteStackInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'DeleteStackSetInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], ], ], 'DeleteStackSetOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeletionTime' => [ 'type' => 'timestamp', ], 'DescribeAccountLimitsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeAccountLimitsOutput' => [ 'type' => 'structure', 'members' => [ 'AccountLimits' => [ 'shape' => 'AccountLimitList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeChangeSetInput' => [ 'type' => 'structure', 'required' => [ 'ChangeSetName', ], 'members' => [ 'ChangeSetName' => [ 'shape' => 'ChangeSetNameOrId', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeChangeSetOutput' => [ 'type' => 'structure', 'members' => [ 'ChangeSetName' => [ 'shape' => 'ChangeSetName', ], 'ChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'StackId' => [ 'shape' => 'StackId', ], 'StackName' => [ 'shape' => 'StackName', ], 'Description' => [ 'shape' => 'Description', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'ExecutionStatus' => [ 'shape' => 'ExecutionStatus', ], 'Status' => [ 'shape' => 'ChangeSetStatus', ], 'StatusReason' => [ 'shape' => 'ChangeSetStatusReason', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Tags' => [ 'shape' => 'Tags', ], 'Changes' => [ 'shape' => 'Changes', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStackEventsInput' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStackEventsOutput' => [ 'type' => 'structure', 'members' => [ 'StackEvents' => [ 'shape' => 'StackEvents', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStackInstanceInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'StackInstanceAccount', 'StackInstanceRegion', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'StackInstanceAccount' => [ 'shape' => 'Account', ], 'StackInstanceRegion' => [ 'shape' => 'Region', ], ], ], 'DescribeStackInstanceOutput' => [ 'type' => 'structure', 'members' => [ 'StackInstance' => [ 'shape' => 'StackInstance', ], ], ], 'DescribeStackResourceInput' => [ 'type' => 'structure', 'required' => [ 'StackName', 'LogicalResourceId', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], ], ], 'DescribeStackResourceOutput' => [ 'type' => 'structure', 'members' => [ 'StackResourceDetail' => [ 'shape' => 'StackResourceDetail', ], ], ], 'DescribeStackResourcesInput' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], ], ], 'DescribeStackResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'StackResources' => [ 'shape' => 'StackResources', ], ], ], 'DescribeStackSetInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], ], ], 'DescribeStackSetOperationInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'OperationId', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'DescribeStackSetOperationOutput' => [ 'type' => 'structure', 'members' => [ 'StackSetOperation' => [ 'shape' => 'StackSetOperation', ], ], ], 'DescribeStackSetOutput' => [ 'type' => 'structure', 'members' => [ 'StackSet' => [ 'shape' => 'StackSet', ], ], ], 'DescribeStacksInput' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeStacksOutput' => [ 'type' => 'structure', 'members' => [ 'Stacks' => [ 'shape' => 'Stacks', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'DisableRollback' => [ 'type' => 'boolean', ], 'EnableTerminationProtection' => [ 'type' => 'boolean', ], 'EstimateTemplateCostInput' => [ 'type' => 'structure', 'members' => [ 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'Parameters' => [ 'shape' => 'Parameters', ], ], ], 'EstimateTemplateCostOutput' => [ 'type' => 'structure', 'members' => [ 'Url' => [ 'shape' => 'Url', ], ], ], 'EvaluationType' => [ 'type' => 'string', 'enum' => [ 'Static', 'Dynamic', ], ], 'EventId' => [ 'type' => 'string', ], 'ExecuteChangeSetInput' => [ 'type' => 'structure', 'required' => [ 'ChangeSetName', ], 'members' => [ 'ChangeSetName' => [ 'shape' => 'ChangeSetNameOrId', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'ExecuteChangeSetOutput' => [ 'type' => 'structure', 'members' => [], ], 'ExecutionRoleName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z_0-9+=,.@-]+', ], 'ExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'UNAVAILABLE', 'AVAILABLE', 'EXECUTE_IN_PROGRESS', 'EXECUTE_COMPLETE', 'EXECUTE_FAILED', 'OBSOLETE', ], ], 'Export' => [ 'type' => 'structure', 'members' => [ 'ExportingStackId' => [ 'shape' => 'StackId', ], 'Name' => [ 'shape' => 'ExportName', ], 'Value' => [ 'shape' => 'ExportValue', ], ], ], 'ExportName' => [ 'type' => 'string', ], 'ExportValue' => [ 'type' => 'string', ], 'Exports' => [ 'type' => 'list', 'member' => [ 'shape' => 'Export', ], ], 'FailureToleranceCount' => [ 'type' => 'integer', 'min' => 0, ], 'FailureTolerancePercentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 0, ], 'GetStackPolicyInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], ], ], 'GetStackPolicyOutput' => [ 'type' => 'structure', 'members' => [ 'StackPolicyBody' => [ 'shape' => 'StackPolicyBody', ], ], ], 'GetTemplateInput' => [ 'type' => 'structure', 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'ChangeSetName' => [ 'shape' => 'ChangeSetNameOrId', ], 'TemplateStage' => [ 'shape' => 'TemplateStage', ], ], ], 'GetTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'StagesAvailable' => [ 'shape' => 'StageList', ], ], ], 'GetTemplateSummaryInput' => [ 'type' => 'structure', 'members' => [ 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], 'StackSetName' => [ 'shape' => 'StackSetNameOrId', ], ], ], 'GetTemplateSummaryOutput' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParameterDeclarations', ], 'Description' => [ 'shape' => 'Description', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'CapabilitiesReason' => [ 'shape' => 'CapabilitiesReason', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'Version' => [ 'shape' => 'Version', ], 'Metadata' => [ 'shape' => 'Metadata', ], 'DeclaredTransforms' => [ 'shape' => 'TransformsList', ], ], ], 'Imports' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackName', ], ], 'InsufficientCapabilitiesException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientCapabilitiesException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidChangeSetStatusException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidChangeSetStatus', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidOperationException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidOperationException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LastUpdatedTime' => [ 'type' => 'timestamp', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'LimitExceededException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LimitName' => [ 'type' => 'string', ], 'LimitValue' => [ 'type' => 'integer', ], 'ListChangeSetsInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListChangeSetsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'ChangeSetSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExportsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListExportsOutput' => [ 'type' => 'structure', 'members' => [ 'Exports' => [ 'shape' => 'Exports', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImportsInput' => [ 'type' => 'structure', 'required' => [ 'ExportName', ], 'members' => [ 'ExportName' => [ 'shape' => 'ExportName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListImportsOutput' => [ 'type' => 'structure', 'members' => [ 'Imports' => [ 'shape' => 'Imports', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackInstancesInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'StackInstanceAccount' => [ 'shape' => 'Account', ], 'StackInstanceRegion' => [ 'shape' => 'Region', ], ], ], 'ListStackInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackInstanceSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackResourcesInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'StackResourceSummaries' => [ 'shape' => 'StackResourceSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackSetOperationResultsInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'OperationId', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListStackSetOperationResultsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackSetOperationResultSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackSetOperationsInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListStackSetOperationsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackSetOperationSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStackSetsInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Status' => [ 'shape' => 'StackSetStatus', ], ], ], 'ListStackSetsOutput' => [ 'type' => 'structure', 'members' => [ 'Summaries' => [ 'shape' => 'StackSetSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListStacksInput' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'StackStatusFilter' => [ 'shape' => 'StackStatusFilter', ], ], ], 'ListStacksOutput' => [ 'type' => 'structure', 'members' => [ 'StackSummaries' => [ 'shape' => 'StackSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'LogicalResourceId' => [ 'type' => 'string', ], 'MaxConcurrentCount' => [ 'type' => 'integer', 'min' => 1, ], 'MaxConcurrentPercentage' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'MaxResults' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Metadata' => [ 'type' => 'string', ], 'MonitoringTimeInMinutes' => [ 'type' => 'integer', 'max' => 180, 'min' => 0, ], 'NameAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'NameAlreadyExistsException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'NoEcho' => [ 'type' => 'boolean', ], 'NotificationARN' => [ 'type' => 'string', ], 'NotificationARNs' => [ 'type' => 'list', 'member' => [ 'shape' => 'NotificationARN', ], 'max' => 5, ], 'OnFailure' => [ 'type' => 'string', 'enum' => [ 'DO_NOTHING', 'ROLLBACK', 'DELETE', ], ], 'OperationIdAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OperationIdAlreadyExistsException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'OperationInProgressException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OperationInProgressException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'OperationNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OperationNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'Output' => [ 'type' => 'structure', 'members' => [ 'OutputKey' => [ 'shape' => 'OutputKey', ], 'OutputValue' => [ 'shape' => 'OutputValue', ], 'Description' => [ 'shape' => 'Description', ], 'ExportName' => [ 'shape' => 'ExportName', ], ], ], 'OutputKey' => [ 'type' => 'string', ], 'OutputValue' => [ 'type' => 'string', ], 'Outputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'Output', ], ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'ParameterKey' => [ 'shape' => 'ParameterKey', ], 'ParameterValue' => [ 'shape' => 'ParameterValue', ], 'UsePreviousValue' => [ 'shape' => 'UsePreviousValue', ], 'ResolvedValue' => [ 'shape' => 'ParameterValue', ], ], ], 'ParameterConstraints' => [ 'type' => 'structure', 'members' => [ 'AllowedValues' => [ 'shape' => 'AllowedValues', ], ], ], 'ParameterDeclaration' => [ 'type' => 'structure', 'members' => [ 'ParameterKey' => [ 'shape' => 'ParameterKey', ], 'DefaultValue' => [ 'shape' => 'ParameterValue', ], 'ParameterType' => [ 'shape' => 'ParameterType', ], 'NoEcho' => [ 'shape' => 'NoEcho', ], 'Description' => [ 'shape' => 'Description', ], 'ParameterConstraints' => [ 'shape' => 'ParameterConstraints', ], ], ], 'ParameterDeclarations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterDeclaration', ], ], 'ParameterKey' => [ 'type' => 'string', ], 'ParameterType' => [ 'type' => 'string', ], 'ParameterValue' => [ 'type' => 'string', ], 'Parameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'PhysicalResourceId' => [ 'type' => 'string', ], 'PropertyName' => [ 'type' => 'string', ], 'Reason' => [ 'type' => 'string', ], 'Region' => [ 'type' => 'string', ], 'RegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'Replacement' => [ 'type' => 'string', 'enum' => [ 'True', 'False', 'Conditional', ], ], 'RequiresRecreation' => [ 'type' => 'string', 'enum' => [ 'Never', 'Conditionally', 'Always', ], ], 'ResourceAttribute' => [ 'type' => 'string', 'enum' => [ 'Properties', 'Metadata', 'CreationPolicy', 'UpdatePolicy', 'DeletionPolicy', 'Tags', ], ], 'ResourceChange' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'ChangeAction', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Replacement' => [ 'shape' => 'Replacement', ], 'Scope' => [ 'shape' => 'Scope', ], 'Details' => [ 'shape' => 'ResourceChangeDetails', ], ], ], 'ResourceChangeDetail' => [ 'type' => 'structure', 'members' => [ 'Target' => [ 'shape' => 'ResourceTargetDefinition', ], 'Evaluation' => [ 'shape' => 'EvaluationType', ], 'ChangeSource' => [ 'shape' => 'ChangeSource', ], 'CausingEntity' => [ 'shape' => 'CausingEntity', ], ], ], 'ResourceChangeDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceChangeDetail', ], ], 'ResourceProperties' => [ 'type' => 'string', ], 'ResourceSignalStatus' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'FAILURE', ], ], 'ResourceSignalUniqueId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'ResourceStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'CREATE_COMPLETE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETE_COMPLETE', 'DELETE_SKIPPED', 'UPDATE_IN_PROGRESS', 'UPDATE_FAILED', 'UPDATE_COMPLETE', ], ], 'ResourceStatusReason' => [ 'type' => 'string', ], 'ResourceTargetDefinition' => [ 'type' => 'structure', 'members' => [ 'Attribute' => [ 'shape' => 'ResourceAttribute', ], 'Name' => [ 'shape' => 'PropertyName', ], 'RequiresRecreation' => [ 'shape' => 'RequiresRecreation', ], ], ], 'ResourceToSkip' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]+|[a-zA-Z][-a-zA-Z0-9]*\\.[a-zA-Z0-9]+', ], 'ResourceType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ResourceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceType', ], ], 'ResourcesToSkip' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceToSkip', ], ], 'RetainResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogicalResourceId', ], ], 'RetainStacks' => [ 'type' => 'boolean', ], 'RetainStacksNullable' => [ 'type' => 'boolean', ], 'RoleARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'RollbackConfiguration' => [ 'type' => 'structure', 'members' => [ 'RollbackTriggers' => [ 'shape' => 'RollbackTriggers', ], 'MonitoringTimeInMinutes' => [ 'shape' => 'MonitoringTimeInMinutes', ], ], ], 'RollbackTrigger' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Type', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Type' => [ 'shape' => 'Type', ], ], ], 'RollbackTriggers' => [ 'type' => 'list', 'member' => [ 'shape' => 'RollbackTrigger', ], 'max' => 5, ], 'Scope' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceAttribute', ], ], 'SetStackPolicyInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'StackPolicyBody' => [ 'shape' => 'StackPolicyBody', ], 'StackPolicyURL' => [ 'shape' => 'StackPolicyURL', ], ], ], 'SignalResourceInput' => [ 'type' => 'structure', 'required' => [ 'StackName', 'LogicalResourceId', 'UniqueId', 'Status', ], 'members' => [ 'StackName' => [ 'shape' => 'StackNameOrId', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'UniqueId' => [ 'shape' => 'ResourceSignalUniqueId', ], 'Status' => [ 'shape' => 'ResourceSignalStatus', ], ], ], 'Stack' => [ 'type' => 'structure', 'required' => [ 'StackName', 'CreationTime', 'StackStatus', ], 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'StackName' => [ 'shape' => 'StackName', ], 'ChangeSetId' => [ 'shape' => 'ChangeSetId', ], 'Description' => [ 'shape' => 'Description', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'DeletionTime' => [ 'shape' => 'DeletionTime', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'StackStatus' => [ 'shape' => 'StackStatus', ], 'StackStatusReason' => [ 'shape' => 'StackStatusReason', ], 'DisableRollback' => [ 'shape' => 'DisableRollback', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'TimeoutInMinutes' => [ 'shape' => 'TimeoutMinutes', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Outputs' => [ 'shape' => 'Outputs', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'Tags' => [ 'shape' => 'Tags', ], 'EnableTerminationProtection' => [ 'shape' => 'EnableTerminationProtection', ], 'ParentId' => [ 'shape' => 'StackId', ], 'RootId' => [ 'shape' => 'StackId', ], ], ], 'StackEvent' => [ 'type' => 'structure', 'required' => [ 'StackId', 'EventId', 'StackName', 'Timestamp', ], 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'EventId' => [ 'shape' => 'EventId', ], 'StackName' => [ 'shape' => 'StackName', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'ResourceStatus' => [ 'shape' => 'ResourceStatus', ], 'ResourceStatusReason' => [ 'shape' => 'ResourceStatusReason', ], 'ResourceProperties' => [ 'shape' => 'ResourceProperties', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'StackEvents' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackEvent', ], ], 'StackId' => [ 'type' => 'string', ], 'StackInstance' => [ 'type' => 'structure', 'members' => [ 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Region' => [ 'shape' => 'Region', ], 'Account' => [ 'shape' => 'Account', ], 'StackId' => [ 'shape' => 'StackId', ], 'ParameterOverrides' => [ 'shape' => 'Parameters', ], 'Status' => [ 'shape' => 'StackInstanceStatus', ], 'StatusReason' => [ 'shape' => 'Reason', ], ], ], 'StackInstanceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StackInstanceNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'StackInstanceStatus' => [ 'type' => 'string', 'enum' => [ 'CURRENT', 'OUTDATED', 'INOPERABLE', ], ], 'StackInstanceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackInstanceSummary', ], ], 'StackInstanceSummary' => [ 'type' => 'structure', 'members' => [ 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Region' => [ 'shape' => 'Region', ], 'Account' => [ 'shape' => 'Account', ], 'StackId' => [ 'shape' => 'StackId', ], 'Status' => [ 'shape' => 'StackInstanceStatus', ], 'StatusReason' => [ 'shape' => 'Reason', ], ], ], 'StackName' => [ 'type' => 'string', ], 'StackNameOrId' => [ 'type' => 'string', 'min' => 1, 'pattern' => '([a-zA-Z][-a-zA-Z0-9]*)|(arn:\\b(aws|aws-us-gov|aws-cn)\\b:[-a-zA-Z0-9:/._+]*)', ], 'StackPolicyBody' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, ], 'StackPolicyDuringUpdateBody' => [ 'type' => 'string', 'max' => 16384, 'min' => 1, ], 'StackPolicyDuringUpdateURL' => [ 'type' => 'string', 'max' => 1350, 'min' => 1, ], 'StackPolicyURL' => [ 'type' => 'string', 'max' => 1350, 'min' => 1, ], 'StackResource' => [ 'type' => 'structure', 'required' => [ 'LogicalResourceId', 'ResourceType', 'Timestamp', 'ResourceStatus', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'StackId' => [ 'shape' => 'StackId', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], 'ResourceStatus' => [ 'shape' => 'ResourceStatus', ], 'ResourceStatusReason' => [ 'shape' => 'ResourceStatusReason', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'StackResourceDetail' => [ 'type' => 'structure', 'required' => [ 'LogicalResourceId', 'ResourceType', 'LastUpdatedTimestamp', 'ResourceStatus', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'StackId' => [ 'shape' => 'StackId', ], 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'ResourceStatus' => [ 'shape' => 'ResourceStatus', ], 'ResourceStatusReason' => [ 'shape' => 'ResourceStatusReason', ], 'Description' => [ 'shape' => 'Description', ], 'Metadata' => [ 'shape' => 'Metadata', ], ], ], 'StackResourceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackResourceSummary', ], ], 'StackResourceSummary' => [ 'type' => 'structure', 'required' => [ 'LogicalResourceId', 'ResourceType', 'LastUpdatedTimestamp', 'ResourceStatus', ], 'members' => [ 'LogicalResourceId' => [ 'shape' => 'LogicalResourceId', ], 'PhysicalResourceId' => [ 'shape' => 'PhysicalResourceId', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'LastUpdatedTimestamp' => [ 'shape' => 'Timestamp', ], 'ResourceStatus' => [ 'shape' => 'ResourceStatus', ], 'ResourceStatusReason' => [ 'shape' => 'ResourceStatusReason', ], ], ], 'StackResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackResource', ], ], 'StackSet' => [ 'type' => 'structure', 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'StackSetStatus', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Tags' => [ 'shape' => 'Tags', ], 'StackSetARN' => [ 'shape' => 'StackSetARN', ], 'AdministrationRoleARN' => [ 'shape' => 'RoleARN', ], 'ExecutionRoleName' => [ 'shape' => 'ExecutionRoleName', ], ], ], 'StackSetARN' => [ 'type' => 'string', ], 'StackSetId' => [ 'type' => 'string', ], 'StackSetName' => [ 'type' => 'string', ], 'StackSetNameOrId' => [ 'type' => 'string', 'pattern' => '[a-zA-Z][-a-zA-Z0-9]*(?::[a-zA-Z0-9]{8}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{4}-[a-zA-Z0-9]{12})?', ], 'StackSetNotEmptyException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StackSetNotEmptyException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'StackSetNotFoundException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StackSetNotFoundException', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'StackSetOperation' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Action' => [ 'shape' => 'StackSetOperationAction', ], 'Status' => [ 'shape' => 'StackSetOperationStatus', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'RetainStacks' => [ 'shape' => 'RetainStacksNullable', ], 'AdministrationRoleARN' => [ 'shape' => 'RoleARN', ], 'ExecutionRoleName' => [ 'shape' => 'ExecutionRoleName', ], 'CreationTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'StackSetOperationAction' => [ 'type' => 'string', 'enum' => [ 'CREATE', 'UPDATE', 'DELETE', ], ], 'StackSetOperationPreferences' => [ 'type' => 'structure', 'members' => [ 'RegionOrder' => [ 'shape' => 'RegionList', ], 'FailureToleranceCount' => [ 'shape' => 'FailureToleranceCount', ], 'FailureTolerancePercentage' => [ 'shape' => 'FailureTolerancePercentage', ], 'MaxConcurrentCount' => [ 'shape' => 'MaxConcurrentCount', ], 'MaxConcurrentPercentage' => [ 'shape' => 'MaxConcurrentPercentage', ], ], ], 'StackSetOperationResultStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'SUCCEEDED', 'FAILED', 'CANCELLED', ], ], 'StackSetOperationResultSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackSetOperationResultSummary', ], ], 'StackSetOperationResultSummary' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], 'Region' => [ 'shape' => 'Region', ], 'Status' => [ 'shape' => 'StackSetOperationResultStatus', ], 'StatusReason' => [ 'shape' => 'Reason', ], 'AccountGateResult' => [ 'shape' => 'AccountGateResult', ], ], ], 'StackSetOperationStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', 'STOPPING', 'STOPPED', ], ], 'StackSetOperationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackSetOperationSummary', ], ], 'StackSetOperationSummary' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], 'Action' => [ 'shape' => 'StackSetOperationAction', ], 'Status' => [ 'shape' => 'StackSetOperationStatus', ], 'CreationTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'StackSetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DELETED', ], ], 'StackSetSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackSetSummary', ], ], 'StackSetSummary' => [ 'type' => 'structure', 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'StackSetId' => [ 'shape' => 'StackSetId', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'StackSetStatus', ], ], ], 'StackStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'CREATE_COMPLETE', 'ROLLBACK_IN_PROGRESS', 'ROLLBACK_FAILED', 'ROLLBACK_COMPLETE', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'DELETE_COMPLETE', 'UPDATE_IN_PROGRESS', 'UPDATE_COMPLETE_CLEANUP_IN_PROGRESS', 'UPDATE_COMPLETE', 'UPDATE_ROLLBACK_IN_PROGRESS', 'UPDATE_ROLLBACK_FAILED', 'UPDATE_ROLLBACK_COMPLETE_CLEANUP_IN_PROGRESS', 'UPDATE_ROLLBACK_COMPLETE', 'REVIEW_IN_PROGRESS', ], ], 'StackStatusFilter' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackStatus', ], ], 'StackStatusReason' => [ 'type' => 'string', ], 'StackSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StackSummary', ], ], 'StackSummary' => [ 'type' => 'structure', 'required' => [ 'StackName', 'CreationTime', 'StackStatus', ], 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], 'StackName' => [ 'shape' => 'StackName', ], 'TemplateDescription' => [ 'shape' => 'TemplateDescription', ], 'CreationTime' => [ 'shape' => 'CreationTime', ], 'LastUpdatedTime' => [ 'shape' => 'LastUpdatedTime', ], 'DeletionTime' => [ 'shape' => 'DeletionTime', ], 'StackStatus' => [ 'shape' => 'StackStatus', ], 'StackStatusReason' => [ 'shape' => 'StackStatusReason', ], 'ParentId' => [ 'shape' => 'StackId', ], 'RootId' => [ 'shape' => 'StackId', ], ], ], 'Stacks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Stack', ], ], 'StageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateStage', ], ], 'StaleRequestException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StaleRequestException', 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'StopStackSetOperationInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'OperationId', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'StopStackSetOperationOutput' => [ 'type' => 'structure', 'members' => [], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, ], 'TemplateBody' => [ 'type' => 'string', 'min' => 1, ], 'TemplateDescription' => [ 'type' => 'string', ], 'TemplateParameter' => [ 'type' => 'structure', 'members' => [ 'ParameterKey' => [ 'shape' => 'ParameterKey', ], 'DefaultValue' => [ 'shape' => 'ParameterValue', ], 'NoEcho' => [ 'shape' => 'NoEcho', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'TemplateParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'TemplateParameter', ], ], 'TemplateStage' => [ 'type' => 'string', 'enum' => [ 'Original', 'Processed', ], ], 'TemplateURL' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'TimeoutMinutes' => [ 'type' => 'integer', 'min' => 1, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TokenAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TokenAlreadyExistsException', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TransformName' => [ 'type' => 'string', ], 'TransformsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransformName', ], ], 'Type' => [ 'type' => 'string', ], 'UpdateStackInput' => [ 'type' => 'structure', 'required' => [ 'StackName', ], 'members' => [ 'StackName' => [ 'shape' => 'StackName', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'UsePreviousTemplate' => [ 'shape' => 'UsePreviousTemplate', ], 'StackPolicyDuringUpdateBody' => [ 'shape' => 'StackPolicyDuringUpdateBody', ], 'StackPolicyDuringUpdateURL' => [ 'shape' => 'StackPolicyDuringUpdateURL', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'ResourceTypes' => [ 'shape' => 'ResourceTypes', ], 'RoleARN' => [ 'shape' => 'RoleARN', ], 'RollbackConfiguration' => [ 'shape' => 'RollbackConfiguration', ], 'StackPolicyBody' => [ 'shape' => 'StackPolicyBody', ], 'StackPolicyURL' => [ 'shape' => 'StackPolicyURL', ], 'NotificationARNs' => [ 'shape' => 'NotificationARNs', ], 'Tags' => [ 'shape' => 'Tags', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], ], 'UpdateStackInstancesInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', 'Accounts', 'Regions', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetNameOrId', ], 'Accounts' => [ 'shape' => 'AccountList', ], 'Regions' => [ 'shape' => 'RegionList', ], 'ParameterOverrides' => [ 'shape' => 'Parameters', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'UpdateStackInstancesOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'UpdateStackOutput' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], ], ], 'UpdateStackSetInput' => [ 'type' => 'structure', 'required' => [ 'StackSetName', ], 'members' => [ 'StackSetName' => [ 'shape' => 'StackSetName', ], 'Description' => [ 'shape' => 'Description', ], 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], 'UsePreviousTemplate' => [ 'shape' => 'UsePreviousTemplate', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'Tags' => [ 'shape' => 'Tags', ], 'OperationPreferences' => [ 'shape' => 'StackSetOperationPreferences', ], 'AdministrationRoleARN' => [ 'shape' => 'RoleARN', ], 'ExecutionRoleName' => [ 'shape' => 'ExecutionRoleName', ], 'OperationId' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Accounts' => [ 'shape' => 'AccountList', ], 'Regions' => [ 'shape' => 'RegionList', ], ], ], 'UpdateStackSetOutput' => [ 'type' => 'structure', 'members' => [ 'OperationId' => [ 'shape' => 'ClientRequestToken', ], ], ], 'UpdateTerminationProtectionInput' => [ 'type' => 'structure', 'required' => [ 'EnableTerminationProtection', 'StackName', ], 'members' => [ 'EnableTerminationProtection' => [ 'shape' => 'EnableTerminationProtection', ], 'StackName' => [ 'shape' => 'StackNameOrId', ], ], ], 'UpdateTerminationProtectionOutput' => [ 'type' => 'structure', 'members' => [ 'StackId' => [ 'shape' => 'StackId', ], ], ], 'Url' => [ 'type' => 'string', ], 'UsePreviousTemplate' => [ 'type' => 'boolean', ], 'UsePreviousValue' => [ 'type' => 'boolean', ], 'ValidateTemplateInput' => [ 'type' => 'structure', 'members' => [ 'TemplateBody' => [ 'shape' => 'TemplateBody', ], 'TemplateURL' => [ 'shape' => 'TemplateURL', ], ], ], 'ValidateTemplateOutput' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'TemplateParameters', ], 'Description' => [ 'shape' => 'Description', ], 'Capabilities' => [ 'shape' => 'Capabilities', ], 'CapabilitiesReason' => [ 'shape' => 'CapabilitiesReason', ], 'DeclaredTransforms' => [ 'shape' => 'TransformsList', ], ], ], 'Version' => [ 'type' => 'string', ], ],];
