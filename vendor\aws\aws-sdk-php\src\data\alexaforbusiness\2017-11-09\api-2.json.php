<?php
// This file was auto-generated from sdk-root/src/data/alexaforbusiness/2017-11-09/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-11-09', 'endpointPrefix' => 'a4b', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'Alexa For Business', 'signatureVersion' => 'v4', 'targetPrefix' => 'AlexaForBusiness', 'uid' => 'alexaforbusiness-2017-11-09', ], 'operations' => [ 'AssociateContactWithAddressBook' => [ 'name' => 'AssociateContactWithAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateContactWithAddressBookRequest', ], 'output' => [ 'shape' => 'AssociateContactWithAddressBookResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], ], ], 'AssociateDeviceWithRoom' => [ 'name' => 'AssociateDeviceWithRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateDeviceWithRoomRequest', ], 'output' => [ 'shape' => 'AssociateDeviceWithRoomResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'DeviceNotRegisteredException', ], ], ], 'AssociateSkillGroupWithRoom' => [ 'name' => 'AssociateSkillGroupWithRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateSkillGroupWithRoomRequest', ], 'output' => [ 'shape' => 'AssociateSkillGroupWithRoomResponse', ], ], 'CreateAddressBook' => [ 'name' => 'CreateAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAddressBookRequest', ], 'output' => [ 'shape' => 'CreateAddressBookResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateContact' => [ 'name' => 'CreateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateContactRequest', ], 'output' => [ 'shape' => 'CreateContactResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateProfile' => [ 'name' => 'CreateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProfileRequest', ], 'output' => [ 'shape' => 'CreateProfileResponse', ], 'errors' => [ [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'AlreadyExistsException', ], ], ], 'CreateRoom' => [ 'name' => 'CreateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateRoomRequest', ], 'output' => [ 'shape' => 'CreateRoomResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateSkillGroup' => [ 'name' => 'CreateSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSkillGroupRequest', ], 'output' => [ 'shape' => 'CreateSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'CreateUser' => [ 'name' => 'CreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserRequest', ], 'output' => [ 'shape' => 'CreateUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'DeleteAddressBook' => [ 'name' => 'DeleteAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteAddressBookRequest', ], 'output' => [ 'shape' => 'DeleteAddressBookResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'DeleteContact' => [ 'name' => 'DeleteContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteContactRequest', ], 'output' => [ 'shape' => 'DeleteContactResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'DeleteProfile' => [ 'name' => 'DeleteProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProfileRequest', ], 'output' => [ 'shape' => 'DeleteProfileResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'DeleteRoom' => [ 'name' => 'DeleteRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRoomRequest', ], 'output' => [ 'shape' => 'DeleteRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'DeleteRoomSkillParameter' => [ 'name' => 'DeleteRoomSkillParameter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRoomSkillParameterRequest', ], 'output' => [ 'shape' => 'DeleteRoomSkillParameterResponse', ], ], 'DeleteSkillGroup' => [ 'name' => 'DeleteSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSkillGroupRequest', ], 'output' => [ 'shape' => 'DeleteSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'output' => [ 'shape' => 'DeleteUserResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'DisassociateContactFromAddressBook' => [ 'name' => 'DisassociateContactFromAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateContactFromAddressBookRequest', ], 'output' => [ 'shape' => 'DisassociateContactFromAddressBookResponse', ], ], 'DisassociateDeviceFromRoom' => [ 'name' => 'DisassociateDeviceFromRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateDeviceFromRoomRequest', ], 'output' => [ 'shape' => 'DisassociateDeviceFromRoomResponse', ], 'errors' => [ [ 'shape' => 'DeviceNotRegisteredException', ], ], ], 'DisassociateSkillGroupFromRoom' => [ 'name' => 'DisassociateSkillGroupFromRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateSkillGroupFromRoomRequest', ], 'output' => [ 'shape' => 'DisassociateSkillGroupFromRoomResponse', ], ], 'GetAddressBook' => [ 'name' => 'GetAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAddressBookRequest', ], 'output' => [ 'shape' => 'GetAddressBookResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'GetContact' => [ 'name' => 'GetContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetContactRequest', ], 'output' => [ 'shape' => 'GetContactResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'GetDevice' => [ 'name' => 'GetDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeviceRequest', ], 'output' => [ 'shape' => 'GetDeviceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'GetProfile' => [ 'name' => 'GetProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetProfileRequest', ], 'output' => [ 'shape' => 'GetProfileResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'GetRoom' => [ 'name' => 'GetRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRoomRequest', ], 'output' => [ 'shape' => 'GetRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'GetRoomSkillParameter' => [ 'name' => 'GetRoomSkillParameter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRoomSkillParameterRequest', ], 'output' => [ 'shape' => 'GetRoomSkillParameterResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'GetSkillGroup' => [ 'name' => 'GetSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSkillGroupRequest', ], 'output' => [ 'shape' => 'GetSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'ListDeviceEvents' => [ 'name' => 'ListDeviceEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDeviceEventsRequest', ], 'output' => [ 'shape' => 'ListDeviceEventsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'ListSkills' => [ 'name' => 'ListSkills', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSkillsRequest', ], 'output' => [ 'shape' => 'ListSkillsResponse', ], ], 'ListTags' => [ 'name' => 'ListTags', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsRequest', ], 'output' => [ 'shape' => 'ListTagsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'PutRoomSkillParameter' => [ 'name' => 'PutRoomSkillParameter', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRoomSkillParameterRequest', ], 'output' => [ 'shape' => 'PutRoomSkillParameterResponse', ], ], 'ResolveRoom' => [ 'name' => 'ResolveRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResolveRoomRequest', ], 'output' => [ 'shape' => 'ResolveRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'RevokeInvitation' => [ 'name' => 'RevokeInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RevokeInvitationRequest', ], 'output' => [ 'shape' => 'RevokeInvitationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'SearchAddressBooks' => [ 'name' => 'SearchAddressBooks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchAddressBooksRequest', ], 'output' => [ 'shape' => 'SearchAddressBooksResponse', ], ], 'SearchContacts' => [ 'name' => 'SearchContacts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchContactsRequest', ], 'output' => [ 'shape' => 'SearchContactsResponse', ], ], 'SearchDevices' => [ 'name' => 'SearchDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchDevicesRequest', ], 'output' => [ 'shape' => 'SearchDevicesResponse', ], ], 'SearchProfiles' => [ 'name' => 'SearchProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchProfilesRequest', ], 'output' => [ 'shape' => 'SearchProfilesResponse', ], ], 'SearchRooms' => [ 'name' => 'SearchRooms', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchRoomsRequest', ], 'output' => [ 'shape' => 'SearchRoomsResponse', ], ], 'SearchSkillGroups' => [ 'name' => 'SearchSkillGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchSkillGroupsRequest', ], 'output' => [ 'shape' => 'SearchSkillGroupsResponse', ], ], 'SearchUsers' => [ 'name' => 'SearchUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SearchUsersRequest', ], 'output' => [ 'shape' => 'SearchUsersResponse', ], ], 'SendInvitation' => [ 'name' => 'SendInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SendInvitationRequest', ], 'output' => [ 'shape' => 'SendInvitationResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'InvalidUserStatusException', ], ], ], 'StartDeviceSync' => [ 'name' => 'StartDeviceSync', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDeviceSyncRequest', ], 'output' => [ 'shape' => 'StartDeviceSyncResponse', ], 'errors' => [ [ 'shape' => 'DeviceNotRegisteredException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'UpdateAddressBook' => [ 'name' => 'UpdateAddressBook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAddressBookRequest', ], 'output' => [ 'shape' => 'UpdateAddressBookResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], ], ], 'UpdateContact' => [ 'name' => 'UpdateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateContactRequest', ], 'output' => [ 'shape' => 'UpdateContactResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], ], ], 'UpdateDevice' => [ 'name' => 'UpdateDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDeviceRequest', ], 'output' => [ 'shape' => 'UpdateDeviceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'DeviceNotRegisteredException', ], ], ], 'UpdateProfile' => [ 'name' => 'UpdateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProfileRequest', ], 'output' => [ 'shape' => 'UpdateProfileResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], ], ], 'UpdateRoom' => [ 'name' => 'UpdateRoom', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRoomRequest', ], 'output' => [ 'shape' => 'UpdateRoomResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], ], ], 'UpdateSkillGroup' => [ 'name' => 'UpdateSkillGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateSkillGroupRequest', ], 'output' => [ 'shape' => 'UpdateSkillGroupResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'NameInUseException', ], ], ], ], 'shapes' => [ 'Address' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'AddressBook' => [ 'type' => 'structure', 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'AddressBookName', ], 'Description' => [ 'shape' => 'AddressBookDescription', ], ], ], 'AddressBookData' => [ 'type' => 'structure', 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'AddressBookName', ], 'Description' => [ 'shape' => 'AddressBookDescription', ], ], ], 'AddressBookDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddressBookData', ], ], 'AddressBookDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'AddressBookName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Arn' => [ 'type' => 'string', 'pattern' => 'arn:[a-z0-9-\\.]{1,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[a-z0-9-\\.]{0,63}:[^/].{0,1023}', ], 'AssociateContactWithAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', 'AddressBookArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'AssociateContactWithAddressBookResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateDeviceWithRoomRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'AssociateDeviceWithRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'AssociateSkillGroupWithRoomRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'AssociateSkillGroupWithRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'Boolean' => [ 'type' => 'boolean', ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 150, 'min' => 10, 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9_-]*', ], 'ConnectionStatus' => [ 'type' => 'string', 'enum' => [ 'ONLINE', 'OFFLINE', ], ], 'Contact' => [ 'type' => 'structure', 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'DisplayName' => [ 'shape' => 'ContactName', ], 'FirstName' => [ 'shape' => 'ContactName', ], 'LastName' => [ 'shape' => 'ContactName', ], 'PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], ], ], 'ContactData' => [ 'type' => 'structure', 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'DisplayName' => [ 'shape' => 'ContactName', ], 'FirstName' => [ 'shape' => 'ContactName', ], 'LastName' => [ 'shape' => 'ContactName', ], 'PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], ], ], 'ContactDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContactData', ], ], 'ContactName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'CreateAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'AddressBookName', ], 'Description' => [ 'shape' => 'AddressBookDescription', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateAddressBookResponse' => [ 'type' => 'structure', 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'CreateContactRequest' => [ 'type' => 'structure', 'required' => [ 'FirstName', 'PhoneNumber', ], 'members' => [ 'DisplayName' => [ 'shape' => 'ContactName', ], 'FirstName' => [ 'shape' => 'ContactName', ], 'LastName' => [ 'shape' => 'ContactName', ], 'PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateContactResponse' => [ 'type' => 'structure', 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], ], ], 'CreateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'ProfileName', 'Timezone', 'Address', 'DistanceUnit', 'TemperatureUnit', 'WakeWord', ], 'members' => [ 'ProfileName' => [ 'shape' => 'ProfileName', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'Address' => [ 'shape' => 'Address', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'TemperatureUnit' => [ 'shape' => 'TemperatureUnit', ], 'WakeWord' => [ 'shape' => 'WakeWord', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'SetupModeDisabled' => [ 'shape' => 'Boolean', ], 'MaxVolumeLimit' => [ 'shape' => 'MaxVolumeLimit', ], 'PSTNEnabled' => [ 'shape' => 'Boolean', ], ], ], 'CreateProfileResponse' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'CreateRoomRequest' => [ 'type' => 'structure', 'required' => [ 'RoomName', ], 'members' => [ 'RoomName' => [ 'shape' => 'RoomName', ], 'Description' => [ 'shape' => 'RoomDescription', ], 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProviderCalendarId' => [ 'shape' => 'ProviderCalendarId', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateRoomResponse' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'CreateSkillGroupRequest' => [ 'type' => 'structure', 'required' => [ 'SkillGroupName', ], 'members' => [ 'SkillGroupName' => [ 'shape' => 'SkillGroupName', ], 'Description' => [ 'shape' => 'SkillGroupDescription', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateSkillGroupResponse' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], ], ], 'CreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', ], 'members' => [ 'UserId' => [ 'shape' => 'user_UserId', ], 'FirstName' => [ 'shape' => 'user_FirstName', ], 'LastName' => [ 'shape' => 'user_LastName', ], 'Email' => [ 'shape' => 'Email', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'AddressBookArn', ], 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteAddressBookResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProfileRequest' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRoomRequest' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRoomSkillParameterRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', 'ParameterKey', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'SkillId' => [ 'shape' => 'SkillId', ], 'ParameterKey' => [ 'shape' => 'RoomSkillParameterKey', ], ], ], 'DeleteRoomSkillParameterResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSkillGroupRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], ], ], 'DeleteSkillGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'EnrollmentId', ], 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], 'EnrollmentId' => [ 'shape' => 'EnrollmentId', ], ], ], 'DeleteUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'Device' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'DeviceSerialNumber' => [ 'shape' => 'DeviceSerialNumber', ], 'DeviceType' => [ 'shape' => 'DeviceType', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'SoftwareVersion' => [ 'shape' => 'SoftwareVersion', ], 'MacAddress' => [ 'shape' => 'MacAddress', ], 'RoomArn' => [ 'shape' => 'Arn', ], 'DeviceStatus' => [ 'shape' => 'DeviceStatus', ], 'DeviceStatusInfo' => [ 'shape' => 'DeviceStatusInfo', ], ], ], 'DeviceData' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'DeviceSerialNumber' => [ 'shape' => 'DeviceSerialNumber', ], 'DeviceType' => [ 'shape' => 'DeviceType', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], 'SoftwareVersion' => [ 'shape' => 'SoftwareVersion', ], 'MacAddress' => [ 'shape' => 'MacAddress', ], 'DeviceStatus' => [ 'shape' => 'DeviceStatus', ], 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'RoomName', ], 'DeviceStatusInfo' => [ 'shape' => 'DeviceStatusInfo', ], ], ], 'DeviceDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceData', ], ], 'DeviceEvent' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'DeviceEventType', ], 'Value' => [ 'shape' => 'DeviceEventValue', ], 'Timestamp' => [ 'shape' => 'Timestamp', ], ], ], 'DeviceEventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceEvent', ], ], 'DeviceEventType' => [ 'type' => 'string', 'enum' => [ 'CONNECTION_STATUS', 'DEVICE_STATUS', ], ], 'DeviceEventValue' => [ 'type' => 'string', ], 'DeviceName' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'DeviceNotRegisteredException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'DeviceSerialNumber' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]{1,200}', ], 'DeviceStatus' => [ 'type' => 'string', 'enum' => [ 'READY', 'PENDING', 'WAS_OFFLINE', 'DEREGISTERED', ], ], 'DeviceStatusDetail' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'DeviceStatusDetailCode', ], ], ], 'DeviceStatusDetailCode' => [ 'type' => 'string', 'enum' => [ 'DEVICE_SOFTWARE_UPDATE_NEEDED', 'DEVICE_WAS_OFFLINE', ], ], 'DeviceStatusDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceStatusDetail', ], ], 'DeviceStatusInfo' => [ 'type' => 'structure', 'members' => [ 'DeviceStatusDetails' => [ 'shape' => 'DeviceStatusDetails', ], 'ConnectionStatus' => [ 'shape' => 'ConnectionStatus', ], ], ], 'DeviceType' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9]{1,200}', ], 'DisassociateContactFromAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', 'AddressBookArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'DisassociateContactFromAddressBookResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateDeviceFromRoomRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], ], ], 'DisassociateDeviceFromRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateSkillGroupFromRoomRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'DisassociateSkillGroupFromRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'DistanceUnit' => [ 'type' => 'string', 'enum' => [ 'METRIC', 'IMPERIAL', ], ], 'E164PhoneNumber' => [ 'type' => 'string', 'pattern' => '^\\+\\d{8,}$', ], 'Email' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([0-9a-zA-Z]([+-.\\w]*[0-9a-zA-Z])*@([0-9a-zA-Z][-\\w]*[0-9a-zA-Z]\\.)+[a-zA-Z]{2,9})', ], 'EnrollmentId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'EnrollmentStatus' => [ 'type' => 'string', 'enum' => [ 'INITIALIZED', 'PENDING', 'REGISTERED', 'DISASSOCIATING', 'DEREGISTERING', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'Feature' => [ 'type' => 'string', 'enum' => [ 'BLUETOOTH', 'VOLUME', 'NOTIFICATIONS', 'LISTS', 'SKILLS', 'ALL', ], ], 'Features' => [ 'type' => 'list', 'member' => [ 'shape' => 'Feature', ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Key', 'Values', ], 'members' => [ 'Key' => [ 'shape' => 'FilterKey', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterKey' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], 'max' => 25, ], 'FilterValue' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], 'max' => 5, ], 'GetAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'AddressBookArn', ], 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], ], ], 'GetAddressBookResponse' => [ 'type' => 'structure', 'members' => [ 'AddressBook' => [ 'shape' => 'AddressBook', ], ], ], 'GetContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], ], ], 'GetContactResponse' => [ 'type' => 'structure', 'members' => [ 'Contact' => [ 'shape' => 'Contact', ], ], ], 'GetDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], ], ], 'GetDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'Device' => [ 'shape' => 'Device', ], ], ], 'GetProfileRequest' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'GetProfileResponse' => [ 'type' => 'structure', 'members' => [ 'Profile' => [ 'shape' => 'Profile', ], ], ], 'GetRoomRequest' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], ], ], 'GetRoomResponse' => [ 'type' => 'structure', 'members' => [ 'Room' => [ 'shape' => 'Room', ], ], ], 'GetRoomSkillParameterRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', 'ParameterKey', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'SkillId' => [ 'shape' => 'SkillId', ], 'ParameterKey' => [ 'shape' => 'RoomSkillParameterKey', ], ], ], 'GetRoomSkillParameterResponse' => [ 'type' => 'structure', 'members' => [ 'RoomSkillParameter' => [ 'shape' => 'RoomSkillParameter', ], ], ], 'GetSkillGroupRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], ], ], 'GetSkillGroupResponse' => [ 'type' => 'structure', 'members' => [ 'SkillGroup' => [ 'shape' => 'SkillGroup', ], ], ], 'InvalidUserStatusException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListDeviceEventsRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceArn', ], 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'EventType' => [ 'shape' => 'DeviceEventType', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListDeviceEventsResponse' => [ 'type' => 'structure', 'members' => [ 'DeviceEvents' => [ 'shape' => 'DeviceEventList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSkillsRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'SkillListMaxResults', ], ], ], 'ListSkillsResponse' => [ 'type' => 'structure', 'members' => [ 'SkillSummaries' => [ 'shape' => 'SkillSummaryList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListTagsResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'MacAddress' => [ 'type' => 'string', ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'MaxVolumeLimit' => [ 'type' => 'integer', ], 'NameInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Profile' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProfileName' => [ 'shape' => 'ProfileName', ], 'Address' => [ 'shape' => 'Address', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'TemperatureUnit' => [ 'shape' => 'TemperatureUnit', ], 'WakeWord' => [ 'shape' => 'WakeWord', ], 'SetupModeDisabled' => [ 'shape' => 'Boolean', ], 'MaxVolumeLimit' => [ 'shape' => 'MaxVolumeLimit', ], 'PSTNEnabled' => [ 'shape' => 'Boolean', ], ], ], 'ProfileData' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProfileName' => [ 'shape' => 'ProfileName', ], 'Address' => [ 'shape' => 'Address', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'TemperatureUnit' => [ 'shape' => 'TemperatureUnit', ], 'WakeWord' => [ 'shape' => 'WakeWord', ], ], ], 'ProfileDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProfileData', ], ], 'ProfileName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'ProviderCalendarId' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'PutRoomSkillParameterRequest' => [ 'type' => 'structure', 'required' => [ 'SkillId', 'RoomSkillParameter', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'SkillId' => [ 'shape' => 'SkillId', ], 'RoomSkillParameter' => [ 'shape' => 'RoomSkillParameter', ], ], ], 'PutRoomSkillParameterResponse' => [ 'type' => 'structure', 'members' => [], ], 'ResolveRoomRequest' => [ 'type' => 'structure', 'required' => [ 'UserId', 'SkillId', ], 'members' => [ 'UserId' => [ 'shape' => 'UserId', ], 'SkillId' => [ 'shape' => 'SkillId', ], ], ], 'ResolveRoomResponse' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'RoomName', ], 'RoomSkillParameters' => [ 'shape' => 'RoomSkillParameters', ], ], ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', ], ], 'exception' => true, ], 'RevokeInvitationRequest' => [ 'type' => 'structure', 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], 'EnrollmentId' => [ 'shape' => 'EnrollmentId', ], ], ], 'RevokeInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Room' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'RoomName', ], 'Description' => [ 'shape' => 'RoomDescription', ], 'ProviderCalendarId' => [ 'shape' => 'ProviderCalendarId', ], 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'RoomData' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'RoomName', ], 'Description' => [ 'shape' => 'RoomDescription', ], 'ProviderCalendarId' => [ 'shape' => 'ProviderCalendarId', ], 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProfileName' => [ 'shape' => 'ProfileName', ], ], ], 'RoomDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoomData', ], ], 'RoomDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'RoomName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'RoomSkillParameter' => [ 'type' => 'structure', 'required' => [ 'ParameterKey', 'ParameterValue', ], 'members' => [ 'ParameterKey' => [ 'shape' => 'RoomSkillParameterKey', ], 'ParameterValue' => [ 'shape' => 'RoomSkillParameterValue', ], ], ], 'RoomSkillParameterKey' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'RoomSkillParameterValue' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'RoomSkillParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'RoomSkillParameter', ], ], 'SearchAddressBooksRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'SearchAddressBooksResponse' => [ 'type' => 'structure', 'members' => [ 'AddressBooks' => [ 'shape' => 'AddressBookDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchContactsRequest' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'SearchContactsResponse' => [ 'type' => 'structure', 'members' => [ 'Contacts' => [ 'shape' => 'ContactDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchDevicesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchDevicesResponse' => [ 'type' => 'structure', 'members' => [ 'Devices' => [ 'shape' => 'DeviceDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'Profiles' => [ 'shape' => 'ProfileDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchRoomsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchRoomsResponse' => [ 'type' => 'structure', 'members' => [ 'Rooms' => [ 'shape' => 'RoomDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchSkillGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchSkillGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'SkillGroups' => [ 'shape' => 'SkillGroupDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SearchUsersRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'Filters' => [ 'shape' => 'FilterList', ], 'SortCriteria' => [ 'shape' => 'SortList', ], ], ], 'SearchUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UserDataList', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'TotalCount' => [ 'shape' => 'TotalCount', ], ], ], 'SendInvitationRequest' => [ 'type' => 'structure', 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], ], ], 'SendInvitationResponse' => [ 'type' => 'structure', 'members' => [], ], 'SkillGroup' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'SkillGroupName' => [ 'shape' => 'SkillGroupName', ], 'Description' => [ 'shape' => 'SkillGroupDescription', ], ], ], 'SkillGroupData' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'SkillGroupName' => [ 'shape' => 'SkillGroupName', ], 'Description' => [ 'shape' => 'SkillGroupDescription', ], ], ], 'SkillGroupDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SkillGroupData', ], ], 'SkillGroupDescription' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'SkillGroupName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'SkillId' => [ 'type' => 'string', 'pattern' => '(^amzn1\\.ask\\.skill\\.[0-9a-f\\-]{1,200})|(^amzn1\\.echo-sdk-ams\\.app\\.[0-9a-f\\-]{1,200})', ], 'SkillListMaxResults' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'SkillName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\u0009\\u000A\\u000D\\u0020-\\u007E\\u0085\\u00A0-\\uD7FF\\uE000-\\uFFFD\\u10000-\\u10FFFF]*', ], 'SkillSummary' => [ 'type' => 'structure', 'members' => [ 'SkillId' => [ 'shape' => 'SkillId', ], 'SkillName' => [ 'shape' => 'SkillName', ], 'SupportsLinking' => [ 'shape' => 'boolean', ], ], ], 'SkillSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SkillSummary', ], ], 'SoftwareVersion' => [ 'type' => 'string', ], 'Sort' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'SortKey', ], 'Value' => [ 'shape' => 'SortValue', ], ], ], 'SortKey' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'SortList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Sort', ], 'max' => 25, ], 'SortValue' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'StartDeviceSyncRequest' => [ 'type' => 'structure', 'required' => [ 'Features', ], 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'DeviceArn' => [ 'shape' => 'Arn', ], 'Features' => [ 'shape' => 'Features', ], ], ], 'StartDeviceSyncResponse' => [ 'type' => 'structure', 'members' => [], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Tags', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TemperatureUnit' => [ 'type' => 'string', 'enum' => [ 'FAHRENHEIT', 'CELSIUS', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Timezone' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'TotalCount' => [ 'type' => 'integer', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', 'TagKeys', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAddressBookRequest' => [ 'type' => 'structure', 'required' => [ 'AddressBookArn', ], 'members' => [ 'AddressBookArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'AddressBookName', ], 'Description' => [ 'shape' => 'AddressBookDescription', ], ], ], 'UpdateAddressBookResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateContactRequest' => [ 'type' => 'structure', 'required' => [ 'ContactArn', ], 'members' => [ 'ContactArn' => [ 'shape' => 'Arn', ], 'DisplayName' => [ 'shape' => 'ContactName', ], 'FirstName' => [ 'shape' => 'ContactName', ], 'LastName' => [ 'shape' => 'ContactName', ], 'PhoneNumber' => [ 'shape' => 'E164PhoneNumber', ], ], ], 'UpdateContactResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDeviceRequest' => [ 'type' => 'structure', 'members' => [ 'DeviceArn' => [ 'shape' => 'Arn', ], 'DeviceName' => [ 'shape' => 'DeviceName', ], ], ], 'UpdateDeviceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateProfileRequest' => [ 'type' => 'structure', 'members' => [ 'ProfileArn' => [ 'shape' => 'Arn', ], 'ProfileName' => [ 'shape' => 'ProfileName', ], 'Timezone' => [ 'shape' => 'Timezone', ], 'Address' => [ 'shape' => 'Address', ], 'DistanceUnit' => [ 'shape' => 'DistanceUnit', ], 'TemperatureUnit' => [ 'shape' => 'TemperatureUnit', ], 'WakeWord' => [ 'shape' => 'WakeWord', ], 'SetupModeDisabled' => [ 'shape' => 'Boolean', ], 'MaxVolumeLimit' => [ 'shape' => 'MaxVolumeLimit', ], 'PSTNEnabled' => [ 'shape' => 'Boolean', ], ], ], 'UpdateProfileResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRoomRequest' => [ 'type' => 'structure', 'members' => [ 'RoomArn' => [ 'shape' => 'Arn', ], 'RoomName' => [ 'shape' => 'RoomName', ], 'Description' => [ 'shape' => 'RoomDescription', ], 'ProviderCalendarId' => [ 'shape' => 'ProviderCalendarId', ], 'ProfileArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateRoomResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSkillGroupRequest' => [ 'type' => 'structure', 'members' => [ 'SkillGroupArn' => [ 'shape' => 'Arn', ], 'SkillGroupName' => [ 'shape' => 'SkillGroupName', ], 'Description' => [ 'shape' => 'SkillGroupDescription', ], ], ], 'UpdateSkillGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'UserData' => [ 'type' => 'structure', 'members' => [ 'UserArn' => [ 'shape' => 'Arn', ], 'FirstName' => [ 'shape' => 'user_FirstName', ], 'LastName' => [ 'shape' => 'user_LastName', ], 'Email' => [ 'shape' => 'Email', ], 'EnrollmentStatus' => [ 'shape' => 'EnrollmentStatus', ], 'EnrollmentId' => [ 'shape' => 'EnrollmentId', ], ], ], 'UserDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserData', ], ], 'UserId' => [ 'type' => 'string', 'pattern' => 'amzn1\\.[A-Za-z0-9+-\\/=.]{1,300}', ], 'WakeWord' => [ 'type' => 'string', 'enum' => [ 'ALEXA', 'AMAZON', 'ECHO', 'COMPUTER', ], ], 'boolean' => [ 'type' => 'boolean', ], 'user_FirstName' => [ 'type' => 'string', 'max' => 30, 'min' => 0, 'pattern' => '([A-Za-z\\-\' 0-9._]|\\p{IsLetter})*', ], 'user_LastName' => [ 'type' => 'string', 'max' => 30, 'min' => 0, 'pattern' => '([A-Za-z\\-\' 0-9._]|\\p{IsLetter})*', ], 'user_UserId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9@_+.-]*', ], ],];
