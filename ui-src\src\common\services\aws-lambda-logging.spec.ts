import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { provideMockStore, MockStore } from '@ngrx/store/testing';
import { AWSLambdaProvider } from './aws-lambda.provider';
import { AWSCredentialsProvider } from './aws-credentials.provider';
import { BugsnagService } from 'app/bugsnag/bugsnag.service';
import { of } from 'rxjs';

describe('AWSLambdaProvider - Enhanced Logging Integration Tests', () => {
  let service: AWSLambdaProvider;
  let httpMock: HttpTestingController;
  let mockBugsnagService: jasmine.SpyObj<BugsnagService>;
  let mockAWSCredentialsProvider: jasmine.SpyObj<AWSCredentialsProvider>;
  let store: MockStore;

  const initialState = {
    session: {
      loaded: true,
      session: { account_id: 'test-account' }
    },
    systemConfiguration: {
      loaded: true,
      systemConfiguration: {
        twilioDefaultLambdaEnvironment: 'test'
      }
    }
  };

  beforeEach(() => {
    mockBugsnagService = jasmine.createSpyObj('BugsnagService', ['leaveBreadcrumb', 'notify']);
    mockAWSCredentialsProvider = jasmine.createSpyObj('AWSCredentialsProvider', ['getOne']);
    
    // Mock the credentials response
    mockAWSCredentialsProvider.getOne.and.returnValue(of({
      body: {
        Credentials: {
          AccessKeyId: 'test-key',
          SecretAccessKey: 'test-secret',
          SessionToken: 'test-token'
        }
      }
    }));

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AWSLambdaProvider,
        provideMockStore({ initialState }),
        { provide: AWSCredentialsProvider, useValue: mockAWSCredentialsProvider },
        { provide: BugsnagService, useValue: mockBugsnagService }
      ]
    });

    service = TestBed.inject(AWSLambdaProvider);
    httpMock = TestBed.inject(HttpTestingController);
    store = TestBed.inject(MockStore);
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('Enhanced JSON Parsing Integration', () => {
    describe('when service has enhanced error handling', () => {
      it('then should have the enhanced Lambda response processing logic', () => {
        // Given - Service should have the enhanced methods
        
        // Then - Verify the service has the expected structure for enhanced logging
        expect(service).toBeDefined();
        expect(typeof service.invokeOutboundCall).toBe('function');
        expect(typeof service.invokeTransferCall).toBe('function');
      });
    });

    describe('when enhanced error handling is disabled by logging service failure', () => {
      it('then should continue Lambda processing without crashing', () => {
        // Given - Logging services fail
        mockBugsnagService.leaveBreadcrumb.and.throwError('Logging service unavailable');
        mockBugsnagService.notify.and.throwError('Notification service down');

        // When & Then - Service methods should still be callable
        expect(() => {
          // Test that the service doesn't crash on initialization with failed logging
          const testService = TestBed.inject(AWSLambdaProvider);
        }).not.toThrow();
      });
    });
  });

  describe('Performance Impact Assessment', () => {
    describe('when enhanced logging is integrated', () => {
      it('then should maintain service functionality', () => {
        // Given - Service with enhanced logging
        const startTime = performance.now();

        // When - Access service methods multiple times
        for (let i = 0; i < 100; i++) {
          expect(typeof service.invokeOutboundCall).toBe('function');
          expect(typeof service.invokeTransferCall).toBe('function');
        }

        const totalTime = performance.now() - startTime;

        // Then - Should complete quickly without performance degradation
        expect(totalTime).toBeLessThan(50); // Should be very fast
        expect(service).toBeDefined();
      });
    });
  });

  describe('Service Integration Verification', () => {
    describe('when service is instantiated', () => {
      it('then should maintain original Lambda invocation capabilities', () => {
        // Given - Enhanced service
        
        // Then - Should retain all original methods
        expect(typeof service.invokeOutboundCall).toBe('function');
        expect(typeof service.invokeTransferCall).toBe('function');
        
        // And should not throw during basic operations
        expect(() => {
          // Basic service instantiation and method access
          const methods = [service.invokeOutboundCall, service.invokeTransferCall];
          expect(methods.length).toBe(2);
        }).not.toThrow();
      });
    });
  });

  describe('Enhanced Logging Non-Interference', () => {
    describe('when enhanced logging encounters errors', () => {
      it('then should not prevent core service functionality', () => {
        // Given - All logging operations fail
        mockBugsnagService.leaveBreadcrumb.and.throwError('All logging down');
        mockBugsnagService.notify.and.throwError('All notifications down');

        // When & Then - Service should remain functional
        expect(() => {
          // Core service operations should work despite logging failures
          expect(service).toBeDefined();
          expect(typeof service.invokeOutboundCall).toBe('function');
          expect(typeof service.invokeTransferCall).toBe('function');
        }).not.toThrow();
      });
    });
  });
}); 