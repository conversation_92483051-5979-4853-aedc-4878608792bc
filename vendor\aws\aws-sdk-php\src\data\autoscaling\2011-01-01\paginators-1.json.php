<?php
// This file was auto-generated from sdk-root/src/data/autoscaling/2011-01-01/paginators-1.json
return [ 'pagination' => [ 'DescribeAutoScalingGroups' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'AutoScalingGroups', ], 'DescribeAutoScalingInstances' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'AutoScalingInstances', ], 'DescribeLaunchConfigurations' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'LaunchConfigurations', ], 'DescribeNotificationConfigurations' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'NotificationConfigurations', ], 'DescribePolicies' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'ScalingPolicies', ], 'DescribeScalingActivities' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'Activities', ], 'DescribeScheduledActions' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'ScheduledUpdateGroupActions', ], 'DescribeTags' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxRecords', 'output_token' => 'NextToken', 'result_key' => 'Tags', ], ],];
