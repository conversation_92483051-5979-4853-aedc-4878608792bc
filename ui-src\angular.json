{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ui-src": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"preserveSymlinks": true, "outputPath": "../ui", "index": "src/index.html", "main": "src/main.browser.ts", "polyfills": "src/polyfills.browser.ts", "tsConfig": "tsconfig.doc.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/assets/scss/core.scss"], "scripts": [], "allowedCommonJsDependencies": ["@twilio/conversations", "moment", "dragula", "aws-sdk", "@twilio/voice-sdk", "twilio-sync", "amazon-quicksight-embedding-sdk", "clone", "moment-timezone", "@ringcentral/sdk", "plupload", "lodash", "cookie", "json-stringify-safe", "@aws-crypto/sha256-js", "crypto-js", "js-cookie", "optimism", "uuid", "ulid", "highcharts", "isomorphic-unfetch", "fast-xml-parser", "aws-appsync", "faker", "url", "paho-mqtt", "@aws-crypto/crc32", "@aws-crypto/crc32c", "@aws-crypto/sha256-browser", "@aws-crypto/sha1-browser", "moment-duration-format", "events"]}, "configurations": {"production": {"optimization": true, "sourceMap": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "progress": true, "budgets": [{"type": "initial", "maximumWarning": "15mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "25kb", "maximumError": "35kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}], "outputHashing": "all", "baseHref": "/ui/", "stylePreprocessorOptions": {"includePaths": ["src/assets/scss", "node_modules/foundation-sites/scss", "src/assets/scss-variables/production"]}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "baseHref": "/ui", "stylePreprocessorOptions": {"includePaths": ["src/assets/scss", "node_modules/foundation-sites/scss", "src/assets/scss-variables/development"]}}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "ui-src:build:production"}, "development": {"browserTarget": "ui-src:build:development"}}, "options": {"port": 3000, "ssl": true, "disableHostCheck": true}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "ui-src:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"preserveSymlinks": true, "main": "src/test.browser.ts", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "./karma.conf.js", "codeCoverageExclude": ["src/common/swagger-providers/**/*", "src/common/mocks/**/*"], "watch": false, "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/assets/scss/core.scss"], "codeCoverage": true, "progress": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "stylePreprocessorOptions": {"includePaths": ["src/assets/scss", "node_modules/foundation-sites/scss", "src/assets/scss-variables/development"]}, "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["**/*.js", "src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"], "cache": {"enabled": false}}}