language: php

sudo: false

cache:
    directories:
        - $HOME/.composer/cache/files

php:
    - 5.5
    - 5.6
    - 7.0
    - 7.1
    - 7.2
    - hhvm

env:
    global:
        - TEST_COMMAND="composer test"

matrix:
    allow_failures:
        - php: hhvm
    fast_finish: true
    include:
        - php: 5.5
          env: COMPOSER_FLAGS="--prefer-stable --prefer-lowest" COVERAGE=true TEST_COMMAND="composer test-ci"

before_install:
    - if [[ $COVERAGE != true ]]; then phpenv config-rm xdebug.ini || true; fi

install:
    # To be removed when this issue will be resolved: https://github.com/composer/composer/issues/5355
    - if [[ "$COMPOSER_FLAGS" == *"--prefer-lowest"* ]]; then travis_retry composer update --prefer-dist --no-interaction --prefer-stable --quiet; fi
    - travis_retry composer update ${COMPOSER_FLAGS} --prefer-dist --no-interaction

script:
    - $TEST_COMMAND
