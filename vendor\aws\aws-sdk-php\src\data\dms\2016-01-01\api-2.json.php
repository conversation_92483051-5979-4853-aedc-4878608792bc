<?php
// This file was auto-generated from sdk-root/src/data/dms/2016-01-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-01-01', 'endpointPrefix' => 'dms', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceFullName' => 'AWS Database Migration Service', 'serviceId' => 'Database Migration Service', 'signatureVersion' => 'v4', 'targetPrefix' => 'AmazonDMSv20160101', 'uid' => 'dms-2016-01-01', ], 'operations' => [ 'AddTagsToResource' => [ 'name' => 'AddTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToResourceMessage', ], 'output' => [ 'shape' => 'AddTagsToResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'CreateEndpoint' => [ 'name' => 'CreateEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEndpointMessage', ], 'output' => [ 'shape' => 'CreateEndpointResponse', ], 'errors' => [ [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'CreateEventSubscription' => [ 'name' => 'CreateEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEventSubscriptionMessage', ], 'output' => [ 'shape' => 'CreateEventSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'SNSInvalidTopicFault', ], [ 'shape' => 'SNSNoAuthorizationFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'CreateReplicationInstance' => [ 'name' => 'CreateReplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReplicationInstanceMessage', ], 'output' => [ 'shape' => 'CreateReplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'InsufficientResourceCapacityFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ReplicationSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'CreateReplicationSubnetGroup' => [ 'name' => 'CreateReplicationSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReplicationSubnetGroupMessage', ], 'output' => [ 'shape' => 'CreateReplicationSubnetGroupResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'ReplicationSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], ], ], 'CreateReplicationTask' => [ 'name' => 'CreateReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReplicationTaskMessage', ], 'output' => [ 'shape' => 'CreateReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], 'DeleteCertificate' => [ 'name' => 'DeleteCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCertificateMessage', ], 'output' => [ 'shape' => 'DeleteCertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteEndpoint' => [ 'name' => 'DeleteEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEndpointMessage', ], 'output' => [ 'shape' => 'DeleteEndpointResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteEventSubscription' => [ 'name' => 'DeleteEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventSubscriptionMessage', ], 'output' => [ 'shape' => 'DeleteEventSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DeleteReplicationInstance' => [ 'name' => 'DeleteReplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationInstanceMessage', ], 'output' => [ 'shape' => 'DeleteReplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DeleteReplicationSubnetGroup' => [ 'name' => 'DeleteReplicationSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationSubnetGroupMessage', ], 'output' => [ 'shape' => 'DeleteReplicationSubnetGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DeleteReplicationTask' => [ 'name' => 'DeleteReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReplicationTaskMessage', ], 'output' => [ 'shape' => 'DeleteReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeAccountAttributes' => [ 'name' => 'DescribeAccountAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountAttributesMessage', ], 'output' => [ 'shape' => 'DescribeAccountAttributesResponse', ], ], 'DescribeCertificates' => [ 'name' => 'DescribeCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCertificatesMessage', ], 'output' => [ 'shape' => 'DescribeCertificatesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeConnections' => [ 'name' => 'DescribeConnections', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConnectionsMessage', ], 'output' => [ 'shape' => 'DescribeConnectionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeEndpointTypes' => [ 'name' => 'DescribeEndpointTypes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointTypesMessage', ], 'output' => [ 'shape' => 'DescribeEndpointTypesResponse', ], ], 'DescribeEndpoints' => [ 'name' => 'DescribeEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEndpointsMessage', ], 'output' => [ 'shape' => 'DescribeEndpointsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeEventCategories' => [ 'name' => 'DescribeEventCategories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventCategoriesMessage', ], 'output' => [ 'shape' => 'DescribeEventCategoriesResponse', ], ], 'DescribeEventSubscriptions' => [ 'name' => 'DescribeEventSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventSubscriptionsMessage', ], 'output' => [ 'shape' => 'DescribeEventSubscriptionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeEvents' => [ 'name' => 'DescribeEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventsMessage', ], 'output' => [ 'shape' => 'DescribeEventsResponse', ], ], 'DescribeOrderableReplicationInstances' => [ 'name' => 'DescribeOrderableReplicationInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrderableReplicationInstancesMessage', ], 'output' => [ 'shape' => 'DescribeOrderableReplicationInstancesResponse', ], ], 'DescribeRefreshSchemasStatus' => [ 'name' => 'DescribeRefreshSchemasStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRefreshSchemasStatusMessage', ], 'output' => [ 'shape' => 'DescribeRefreshSchemasStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationInstanceTaskLogs' => [ 'name' => 'DescribeReplicationInstanceTaskLogs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationInstanceTaskLogsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationInstanceTaskLogsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DescribeReplicationInstances' => [ 'name' => 'DescribeReplicationInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationInstancesMessage', ], 'output' => [ 'shape' => 'DescribeReplicationInstancesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationSubnetGroups' => [ 'name' => 'DescribeReplicationSubnetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationSubnetGroupsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationSubnetGroupsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationTaskAssessmentResults' => [ 'name' => 'DescribeReplicationTaskAssessmentResults', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationTaskAssessmentResultsMessage', ], 'output' => [ 'shape' => 'DescribeReplicationTaskAssessmentResultsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReplicationTasks' => [ 'name' => 'DescribeReplicationTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReplicationTasksMessage', ], 'output' => [ 'shape' => 'DescribeReplicationTasksResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeSchemas' => [ 'name' => 'DescribeSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSchemasMessage', ], 'output' => [ 'shape' => 'DescribeSchemasResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeTableStatistics' => [ 'name' => 'DescribeTableStatistics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTableStatisticsMessage', ], 'output' => [ 'shape' => 'DescribeTableStatisticsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'ImportCertificate' => [ 'name' => 'ImportCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportCertificateMessage', ], 'output' => [ 'shape' => 'ImportCertificateResponse', ], 'errors' => [ [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'InvalidCertificateFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceMessage', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'ModifyEndpoint' => [ 'name' => 'ModifyEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyEndpointMessage', ], 'output' => [ 'shape' => 'ModifyEndpointResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'ModifyEventSubscription' => [ 'name' => 'ModifyEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyEventSubscriptionMessage', ], 'output' => [ 'shape' => 'ModifyEventSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'SNSInvalidTopicFault', ], [ 'shape' => 'SNSNoAuthorizationFault', ], ], ], 'ModifyReplicationInstance' => [ 'name' => 'ModifyReplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReplicationInstanceMessage', ], 'output' => [ 'shape' => 'ModifyReplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InsufficientResourceCapacityFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'UpgradeDependencyFailureFault', ], ], ], 'ModifyReplicationSubnetGroup' => [ 'name' => 'ModifyReplicationSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReplicationSubnetGroupMessage', ], 'output' => [ 'shape' => 'ModifyReplicationSubnetGroupResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], [ 'shape' => 'SubnetAlreadyInUse', ], [ 'shape' => 'ReplicationSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], ], ], 'ModifyReplicationTask' => [ 'name' => 'ModifyReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyReplicationTaskMessage', ], 'output' => [ 'shape' => 'ModifyReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'ResourceAlreadyExistsFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'RebootReplicationInstance' => [ 'name' => 'RebootReplicationInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootReplicationInstanceMessage', ], 'output' => [ 'shape' => 'RebootReplicationInstanceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'RefreshSchemas' => [ 'name' => 'RefreshSchemas', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RefreshSchemasMessage', ], 'output' => [ 'shape' => 'RefreshSchemasResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], 'ReloadTables' => [ 'name' => 'ReloadTables', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ReloadTablesMessage', ], 'output' => [ 'shape' => 'ReloadTablesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'RemoveTagsFromResource' => [ 'name' => 'RemoveTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromResourceMessage', ], 'output' => [ 'shape' => 'RemoveTagsFromResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'StartReplicationTask' => [ 'name' => 'StartReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartReplicationTaskMessage', ], 'output' => [ 'shape' => 'StartReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'AccessDeniedFault', ], ], ], 'StartReplicationTaskAssessment' => [ 'name' => 'StartReplicationTaskAssessment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartReplicationTaskAssessmentMessage', ], 'output' => [ 'shape' => 'StartReplicationTaskAssessmentResponse', ], 'errors' => [ [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'StopReplicationTask' => [ 'name' => 'StopReplicationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopReplicationTaskMessage', ], 'output' => [ 'shape' => 'StopReplicationTaskResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'TestConnection' => [ 'name' => 'TestConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestConnectionMessage', ], 'output' => [ 'shape' => 'TestConnectionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'ResourceQuotaExceededFault', ], ], ], ], 'shapes' => [ 'AccessDeniedFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'AccountQuota' => [ 'type' => 'structure', 'members' => [ 'AccountQuotaName' => [ 'shape' => 'String', ], 'Used' => [ 'shape' => 'Long', ], 'Max' => [ 'shape' => 'Long', ], ], ], 'AccountQuotaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountQuota', ], ], 'AddTagsToResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'AddTagsToResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'AuthMechanismValue' => [ 'type' => 'string', 'enum' => [ 'default', 'mongodb_cr', 'scram_sha_1', ], ], 'AuthTypeValue' => [ 'type' => 'string', 'enum' => [ 'no', 'password', ], ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanOptional' => [ 'type' => 'boolean', ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'CertificateIdentifier' => [ 'shape' => 'String', ], 'CertificateCreationDate' => [ 'shape' => 'TStamp', ], 'CertificatePem' => [ 'shape' => 'String', ], 'CertificateWallet' => [ 'shape' => 'CertificateWallet', ], 'CertificateArn' => [ 'shape' => 'String', ], 'CertificateOwner' => [ 'shape' => 'String', ], 'ValidFromDate' => [ 'shape' => 'TStamp', ], 'ValidToDate' => [ 'shape' => 'TStamp', ], 'SigningAlgorithm' => [ 'shape' => 'String', ], 'KeyLength' => [ 'shape' => 'IntegerOptional', ], ], ], 'CertificateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Certificate', ], ], 'CertificateWallet' => [ 'type' => 'blob', ], 'CompressionTypeValue' => [ 'type' => 'string', 'enum' => [ 'none', 'gzip', ], ], 'Connection' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'EndpointArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'LastFailureMessage' => [ 'shape' => 'String', ], 'EndpointIdentifier' => [ 'shape' => 'String', ], 'ReplicationInstanceIdentifier' => [ 'shape' => 'String', ], ], ], 'ConnectionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Connection', ], ], 'CreateEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointIdentifier', 'EndpointType', 'EngineName', ], 'members' => [ 'EndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'ReplicationEndpointTypeValue', ], 'EngineName' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'ExtraConnectionAttributes' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'CertificateArn' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ExternalTableDefinition' => [ 'shape' => 'String', ], 'DynamoDbSettings' => [ 'shape' => 'DynamoDbSettings', ], 'S3Settings' => [ 'shape' => 'S3Settings', ], 'DmsTransferSettings' => [ 'shape' => 'DmsTransferSettings', ], 'MongoDbSettings' => [ 'shape' => 'MongoDbSettings', ], ], ], 'CreateEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'CreateEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', 'SnsTopicArn', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'SourceIds' => [ 'shape' => 'SourceIdsList', ], 'Enabled' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateEventSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'CreateReplicationInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceIdentifier', 'ReplicationInstanceClass', ], 'members' => [ 'ReplicationInstanceIdentifier' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], ], ], 'CreateReplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstance' => [ 'shape' => 'ReplicationInstance', ], ], ], 'CreateReplicationSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationSubnetGroupIdentifier', 'ReplicationSubnetGroupDescription', 'SubnetIds', ], 'members' => [ 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], 'ReplicationSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateReplicationSubnetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationSubnetGroup' => [ 'shape' => 'ReplicationSubnetGroup', ], ], ], 'CreateReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskIdentifier', 'SourceEndpointArn', 'TargetEndpointArn', 'ReplicationInstanceArn', 'MigrationType', 'TableMappings', ], 'members' => [ 'ReplicationTaskIdentifier' => [ 'shape' => 'String', ], 'SourceEndpointArn' => [ 'shape' => 'String', ], 'TargetEndpointArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'MigrationType' => [ 'shape' => 'MigrationTypeValue', ], 'TableMappings' => [ 'shape' => 'String', ], 'ReplicationTaskSettings' => [ 'shape' => 'String', ], 'CdcStartTime' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'DeleteCertificateMessage' => [ 'type' => 'structure', 'required' => [ 'CertificateArn', ], 'members' => [ 'CertificateArn' => [ 'shape' => 'String', ], ], ], 'DeleteCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'Certificate', ], ], ], 'DeleteEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], ], ], 'DeleteEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'DeleteEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], ], ], 'DeleteEventSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'DeleteReplicationInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], ], ], 'DeleteReplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstance' => [ 'shape' => 'ReplicationInstance', ], ], ], 'DeleteReplicationSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationSubnetGroupIdentifier', ], 'members' => [ 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteReplicationSubnetGroupResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], ], ], 'DeleteReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'DescribeAccountAttributesMessage' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'AccountQuotas' => [ 'shape' => 'AccountQuotaList', ], ], ], 'DescribeCertificatesMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeCertificatesResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Certificates' => [ 'shape' => 'CertificateList', ], ], ], 'DescribeConnectionsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeConnectionsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Connections' => [ 'shape' => 'ConnectionList', ], ], ], 'DescribeEndpointTypesMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEndpointTypesResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'SupportedEndpointTypes' => [ 'shape' => 'SupportedEndpointTypeList', ], ], ], 'DescribeEndpointsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Endpoints' => [ 'shape' => 'EndpointList', ], ], ], 'DescribeEventCategoriesMessage' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'DescribeEventCategoriesResponse' => [ 'type' => 'structure', 'members' => [ 'EventCategoryGroupList' => [ 'shape' => 'EventCategoryGroupList', ], ], ], 'DescribeEventSubscriptionsMessage' => [ 'type' => 'structure', 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEventSubscriptionsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'EventSubscriptionsList' => [ 'shape' => 'EventSubscriptionsList', ], ], ], 'DescribeEventsMessage' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'StartTime' => [ 'shape' => 'TStamp', ], 'EndTime' => [ 'shape' => 'TStamp', ], 'Duration' => [ 'shape' => 'IntegerOptional', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEventsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Events' => [ 'shape' => 'EventList', ], ], ], 'DescribeOrderableReplicationInstancesMessage' => [ 'type' => 'structure', 'members' => [ 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeOrderableReplicationInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'OrderableReplicationInstances' => [ 'shape' => 'OrderableReplicationInstanceList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeRefreshSchemasStatusMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], ], ], 'DescribeRefreshSchemasStatusResponse' => [ 'type' => 'structure', 'members' => [ 'RefreshSchemasStatus' => [ 'shape' => 'RefreshSchemasStatus', ], ], ], 'DescribeReplicationInstanceTaskLogsMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationInstanceTaskLogsResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'ReplicationInstanceTaskLogs' => [ 'shape' => 'ReplicationInstanceTaskLogsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationInstancesMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationInstances' => [ 'shape' => 'ReplicationInstanceList', ], ], ], 'DescribeReplicationSubnetGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationSubnetGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationSubnetGroups' => [ 'shape' => 'ReplicationSubnetGroups', ], ], ], 'DescribeReplicationTaskAssessmentResultsMessage' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationTaskAssessmentResultsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'BucketName' => [ 'shape' => 'String', ], 'ReplicationTaskAssessmentResults' => [ 'shape' => 'ReplicationTaskAssessmentResultList', ], ], ], 'DescribeReplicationTasksMessage' => [ 'type' => 'structure', 'members' => [ 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReplicationTasksResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReplicationTasks' => [ 'shape' => 'ReplicationTaskList', ], ], ], 'DescribeSchemasMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Schemas' => [ 'shape' => 'SchemaList', ], ], ], 'DescribeTableStatisticsMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'DescribeTableStatisticsResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'TableStatistics' => [ 'shape' => 'TableStatisticsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DmsSslModeValue' => [ 'type' => 'string', 'enum' => [ 'none', 'require', 'verify-ca', 'verify-full', ], ], 'DmsTransferSettings' => [ 'type' => 'structure', 'members' => [ 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'BucketName' => [ 'shape' => 'String', ], ], ], 'DynamoDbSettings' => [ 'type' => 'structure', 'required' => [ 'ServiceAccessRoleArn', ], 'members' => [ 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], ], ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'EndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'ReplicationEndpointTypeValue', ], 'EngineName' => [ 'shape' => 'String', ], 'EngineDisplayName' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'ExtraConnectionAttributes' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'EndpointArn' => [ 'shape' => 'String', ], 'CertificateArn' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ExternalTableDefinition' => [ 'shape' => 'String', ], 'ExternalId' => [ 'shape' => 'String', ], 'DynamoDbSettings' => [ 'shape' => 'DynamoDbSettings', ], 'S3Settings' => [ 'shape' => 'S3Settings', ], 'DmsTransferSettings' => [ 'shape' => 'DmsTransferSettings', ], 'MongoDbSettings' => [ 'shape' => 'MongoDbSettings', ], ], ], 'EndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Endpoint', ], ], 'Event' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'Message' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Date' => [ 'shape' => 'TStamp', ], ], ], 'EventCategoriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'EventCategoryGroup' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], ], ], 'EventCategoryGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventCategoryGroup', ], ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], ], 'EventSubscription' => [ 'type' => 'structure', 'members' => [ 'CustomerAwsId' => [ 'shape' => 'String', ], 'CustSubscriptionId' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'SubscriptionCreationTime' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'SourceIdsList' => [ 'shape' => 'SourceIdsList', ], 'EventCategoriesList' => [ 'shape' => 'EventCategoriesList', ], 'Enabled' => [ 'shape' => 'Boolean', ], ], ], 'EventSubscriptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSubscription', ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ImportCertificateMessage' => [ 'type' => 'structure', 'required' => [ 'CertificateIdentifier', ], 'members' => [ 'CertificateIdentifier' => [ 'shape' => 'String', ], 'CertificatePem' => [ 'shape' => 'String', ], 'CertificateWallet' => [ 'shape' => 'CertificateWallet', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ImportCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'Certificate', ], ], ], 'InsufficientResourceCapacityFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', ], 'IntegerOptional' => [ 'type' => 'integer', ], 'InvalidCertificateFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidResourceStateFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'InvalidSubnet' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KMSKeyNotAccessibleFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListTagsForResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'Long' => [ 'type' => 'long', ], 'MigrationTypeValue' => [ 'type' => 'string', 'enum' => [ 'full-load', 'cdc', 'full-load-and-cdc', ], ], 'ModifyEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'EndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'ReplicationEndpointTypeValue', ], 'EngineName' => [ 'shape' => 'String', ], 'Username' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'ExtraConnectionAttributes' => [ 'shape' => 'String', ], 'CertificateArn' => [ 'shape' => 'String', ], 'SslMode' => [ 'shape' => 'DmsSslModeValue', ], 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ExternalTableDefinition' => [ 'shape' => 'String', ], 'DynamoDbSettings' => [ 'shape' => 'DynamoDbSettings', ], 'S3Settings' => [ 'shape' => 'S3Settings', ], 'DmsTransferSettings' => [ 'shape' => 'DmsTransferSettings', ], 'MongoDbSettings' => [ 'shape' => 'MongoDbSettings', ], ], ], 'ModifyEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'Endpoint' => [ 'shape' => 'Endpoint', ], ], ], 'ModifyEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Enabled' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyEventSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'ModifyReplicationInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AllowMajorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'ReplicationInstanceIdentifier' => [ 'shape' => 'String', ], ], ], 'ModifyReplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstance' => [ 'shape' => 'ReplicationInstance', ], ], ], 'ModifyReplicationSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationSubnetGroupIdentifier', 'SubnetIds', ], 'members' => [ 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], 'ReplicationSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], ], ], 'ModifyReplicationSubnetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationSubnetGroup' => [ 'shape' => 'ReplicationSubnetGroup', ], ], ], 'ModifyReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ReplicationTaskIdentifier' => [ 'shape' => 'String', ], 'MigrationType' => [ 'shape' => 'MigrationTypeValue', ], 'TableMappings' => [ 'shape' => 'String', ], 'ReplicationTaskSettings' => [ 'shape' => 'String', ], 'CdcStartTime' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], ], ], 'ModifyReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'MongoDbSettings' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'String', ], 'Password' => [ 'shape' => 'SecretString', ], 'ServerName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'AuthType' => [ 'shape' => 'AuthTypeValue', ], 'AuthMechanism' => [ 'shape' => 'AuthMechanismValue', ], 'NestingLevel' => [ 'shape' => 'NestingLevelValue', ], 'ExtractDocId' => [ 'shape' => 'String', ], 'DocsToInvestigate' => [ 'shape' => 'String', ], 'AuthSource' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], ], ], 'NestingLevelValue' => [ 'type' => 'string', 'enum' => [ 'none', 'one', ], ], 'OrderableReplicationInstance' => [ 'type' => 'structure', 'members' => [ 'EngineVersion' => [ 'shape' => 'String', ], 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'MinAllocatedStorage' => [ 'shape' => 'Integer', ], 'MaxAllocatedStorage' => [ 'shape' => 'Integer', ], 'DefaultAllocatedStorage' => [ 'shape' => 'Integer', ], 'IncludedAllocatedStorage' => [ 'shape' => 'Integer', ], ], ], 'OrderableReplicationInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrderableReplicationInstance', ], ], 'RebootReplicationInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'ForceFailover' => [ 'shape' => 'BooleanOptional', ], ], ], 'RebootReplicationInstanceResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstance' => [ 'shape' => 'ReplicationInstance', ], ], ], 'RefreshSchemasMessage' => [ 'type' => 'structure', 'required' => [ 'EndpointArn', 'ReplicationInstanceArn', ], 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], ], ], 'RefreshSchemasResponse' => [ 'type' => 'structure', 'members' => [ 'RefreshSchemasStatus' => [ 'shape' => 'RefreshSchemasStatus', ], ], ], 'RefreshSchemasStatus' => [ 'type' => 'structure', 'members' => [ 'EndpointArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'RefreshSchemasStatusTypeValue', ], 'LastRefreshDate' => [ 'shape' => 'TStamp', ], 'LastFailureMessage' => [ 'shape' => 'String', ], ], ], 'RefreshSchemasStatusTypeValue' => [ 'type' => 'string', 'enum' => [ 'successful', 'failed', 'refreshing', ], ], 'ReloadOptionValue' => [ 'type' => 'string', 'enum' => [ 'data-reload', 'validate-only', ], ], 'ReloadTablesMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', 'TablesToReload', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'TablesToReload' => [ 'shape' => 'TableListToReload', ], 'ReloadOption' => [ 'shape' => 'ReloadOptionValue', ], ], ], 'ReloadTablesResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], ], ], 'RemoveTagsFromResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'TagKeys' => [ 'shape' => 'KeyList', ], ], ], 'RemoveTagsFromResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ReplicationEndpointTypeValue' => [ 'type' => 'string', 'enum' => [ 'source', 'target', ], ], 'ReplicationInstance' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstanceIdentifier' => [ 'shape' => 'String', ], 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'ReplicationInstanceStatus' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'InstanceCreateTime' => [ 'shape' => 'TStamp', ], 'VpcSecurityGroups' => [ 'shape' => 'VpcSecurityGroupMembershipList', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'ReplicationSubnetGroup' => [ 'shape' => 'ReplicationSubnetGroup', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'PendingModifiedValues' => [ 'shape' => 'ReplicationPendingModifiedValues', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'ReplicationInstancePublicIpAddress' => [ 'shape' => 'String', 'deprecated' => true, ], 'ReplicationInstancePrivateIpAddress' => [ 'shape' => 'String', 'deprecated' => true, ], 'ReplicationInstancePublicIpAddresses' => [ 'shape' => 'ReplicationInstancePublicIpAddressList', ], 'ReplicationInstancePrivateIpAddresses' => [ 'shape' => 'ReplicationInstancePrivateIpAddressList', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], 'SecondaryAvailabilityZone' => [ 'shape' => 'String', ], 'FreeUntil' => [ 'shape' => 'TStamp', ], ], ], 'ReplicationInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationInstance', ], ], 'ReplicationInstancePrivateIpAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ReplicationInstancePublicIpAddressList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ReplicationInstanceTaskLog' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskName' => [ 'shape' => 'String', ], 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ReplicationInstanceTaskLogSize' => [ 'shape' => 'Long', ], ], ], 'ReplicationInstanceTaskLogsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationInstanceTaskLog', ], ], 'ReplicationPendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'ReplicationInstanceClass' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], ], ], 'ReplicationSubnetGroup' => [ 'type' => 'structure', 'members' => [ 'ReplicationSubnetGroupIdentifier' => [ 'shape' => 'String', ], 'ReplicationSubnetGroupDescription' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'SubnetGroupStatus' => [ 'shape' => 'String', ], 'Subnets' => [ 'shape' => 'SubnetList', ], ], ], 'ReplicationSubnetGroupDoesNotCoverEnoughAZs' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ReplicationSubnetGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationSubnetGroup', ], ], 'ReplicationTask' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskIdentifier' => [ 'shape' => 'String', ], 'SourceEndpointArn' => [ 'shape' => 'String', ], 'TargetEndpointArn' => [ 'shape' => 'String', ], 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'MigrationType' => [ 'shape' => 'MigrationTypeValue', ], 'TableMappings' => [ 'shape' => 'String', ], 'ReplicationTaskSettings' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'LastFailureMessage' => [ 'shape' => 'String', ], 'StopReason' => [ 'shape' => 'String', ], 'ReplicationTaskCreationDate' => [ 'shape' => 'TStamp', ], 'ReplicationTaskStartDate' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], 'RecoveryCheckpoint' => [ 'shape' => 'String', ], 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ReplicationTaskStats' => [ 'shape' => 'ReplicationTaskStats', ], ], ], 'ReplicationTaskAssessmentResult' => [ 'type' => 'structure', 'members' => [ 'ReplicationTaskIdentifier' => [ 'shape' => 'String', ], 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'ReplicationTaskLastAssessmentDate' => [ 'shape' => 'TStamp', ], 'AssessmentStatus' => [ 'shape' => 'String', ], 'AssessmentResultsFile' => [ 'shape' => 'String', ], 'AssessmentResults' => [ 'shape' => 'String', ], 'S3ObjectUrl' => [ 'shape' => 'String', ], ], ], 'ReplicationTaskAssessmentResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationTaskAssessmentResult', ], ], 'ReplicationTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationTask', ], ], 'ReplicationTaskStats' => [ 'type' => 'structure', 'members' => [ 'FullLoadProgressPercent' => [ 'shape' => 'Integer', ], 'ElapsedTimeMillis' => [ 'shape' => 'Long', ], 'TablesLoaded' => [ 'shape' => 'Integer', ], 'TablesLoading' => [ 'shape' => 'Integer', ], 'TablesQueued' => [ 'shape' => 'Integer', ], 'TablesErrored' => [ 'shape' => 'Integer', ], ], ], 'ResourceAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ResourceNotFoundFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'ResourceQuotaExceededFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'S3Settings' => [ 'type' => 'structure', 'members' => [ 'ServiceAccessRoleArn' => [ 'shape' => 'String', ], 'ExternalTableDefinition' => [ 'shape' => 'String', ], 'CsvRowDelimiter' => [ 'shape' => 'String', ], 'CsvDelimiter' => [ 'shape' => 'String', ], 'BucketFolder' => [ 'shape' => 'String', ], 'BucketName' => [ 'shape' => 'String', ], 'CompressionType' => [ 'shape' => 'CompressionTypeValue', ], ], ], 'SNSInvalidTopicFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'SNSNoAuthorizationFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'SchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SecretString' => [ 'type' => 'string', 'sensitive' => true, ], 'SourceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'replication-instance', ], ], 'StartReplicationTaskAssessmentMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], ], ], 'StartReplicationTaskAssessmentResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'StartReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', 'StartReplicationTaskType', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], 'StartReplicationTaskType' => [ 'shape' => 'StartReplicationTaskTypeValue', ], 'CdcStartTime' => [ 'shape' => 'TStamp', ], 'CdcStartPosition' => [ 'shape' => 'String', ], 'CdcStopPosition' => [ 'shape' => 'String', ], ], ], 'StartReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'StartReplicationTaskTypeValue' => [ 'type' => 'string', 'enum' => [ 'start-replication', 'resume-processing', 'reload-target', ], ], 'StopReplicationTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationTaskArn', ], 'members' => [ 'ReplicationTaskArn' => [ 'shape' => 'String', ], ], ], 'StopReplicationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'ReplicationTask' => [ 'shape' => 'ReplicationTask', ], ], ], 'StorageQuotaExceededFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'Subnet' => [ 'type' => 'structure', 'members' => [ 'SubnetIdentifier' => [ 'shape' => 'String', ], 'SubnetAvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'SubnetStatus' => [ 'shape' => 'String', ], ], ], 'SubnetAlreadyInUse' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'SubnetIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SubnetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', ], ], 'SupportedEndpointType' => [ 'type' => 'structure', 'members' => [ 'EngineName' => [ 'shape' => 'String', ], 'SupportsCDC' => [ 'shape' => 'Boolean', ], 'EndpointType' => [ 'shape' => 'ReplicationEndpointTypeValue', ], 'EngineDisplayName' => [ 'shape' => 'String', ], ], ], 'SupportedEndpointTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedEndpointType', ], ], 'TStamp' => [ 'type' => 'timestamp', ], 'TableListToReload' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableToReload', ], ], 'TableStatistics' => [ 'type' => 'structure', 'members' => [ 'SchemaName' => [ 'shape' => 'String', ], 'TableName' => [ 'shape' => 'String', ], 'Inserts' => [ 'shape' => 'Long', ], 'Deletes' => [ 'shape' => 'Long', ], 'Updates' => [ 'shape' => 'Long', ], 'Ddls' => [ 'shape' => 'Long', ], 'FullLoadRows' => [ 'shape' => 'Long', ], 'FullLoadCondtnlChkFailedRows' => [ 'shape' => 'Long', ], 'FullLoadErrorRows' => [ 'shape' => 'Long', ], 'LastUpdateTime' => [ 'shape' => 'TStamp', ], 'TableState' => [ 'shape' => 'String', ], 'ValidationPendingRecords' => [ 'shape' => 'Long', ], 'ValidationFailedRecords' => [ 'shape' => 'Long', ], 'ValidationSuspendedRecords' => [ 'shape' => 'Long', ], 'ValidationState' => [ 'shape' => 'String', ], 'ValidationStateDetails' => [ 'shape' => 'String', ], ], ], 'TableStatisticsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TableStatistics', ], ], 'TableToReload' => [ 'type' => 'structure', 'members' => [ 'SchemaName' => [ 'shape' => 'String', ], 'TableName' => [ 'shape' => 'String', ], ], ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TestConnectionMessage' => [ 'type' => 'structure', 'required' => [ 'ReplicationInstanceArn', 'EndpointArn', ], 'members' => [ 'ReplicationInstanceArn' => [ 'shape' => 'String', ], 'EndpointArn' => [ 'shape' => 'String', ], ], ], 'TestConnectionResponse' => [ 'type' => 'structure', 'members' => [ 'Connection' => [ 'shape' => 'Connection', ], ], ], 'UpgradeDependencyFailureFault' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ExceptionMessage', ], ], 'exception' => true, ], 'VpcSecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'VpcSecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'VpcSecurityGroupId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'VpcSecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcSecurityGroupMembership', ], ], ],];
