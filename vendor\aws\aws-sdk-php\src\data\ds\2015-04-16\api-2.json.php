<?php
// This file was auto-generated from sdk-root/src/data/ds/2015-04-16/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2015-04-16', 'endpointPrefix' => 'ds', 'jsonVersion' => '1.1', 'protocol' => 'json', 'serviceAbbreviation' => 'Directory Service', 'serviceFullName' => 'AWS Directory Service', 'serviceId' => 'Directory Service', 'signatureVersion' => 'v4', 'targetPrefix' => 'DirectoryService_20150416', 'uid' => 'ds-2015-04-16', ], 'operations' => [ 'AddIpRoutes' => [ 'name' => 'AddIpRoutes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddIpRoutesRequest', ], 'output' => [ 'shape' => 'AddIpRoutesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'IpRouteLimitExceededException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'AddTagsToResource' => [ 'name' => 'AddTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToResourceRequest', ], 'output' => [ 'shape' => 'AddTagsToResourceResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TagLimitExceededException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CancelSchemaExtension' => [ 'name' => 'CancelSchemaExtension', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelSchemaExtensionRequest', ], 'output' => [ 'shape' => 'CancelSchemaExtensionResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ConnectDirectory' => [ 'name' => 'ConnectDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ConnectDirectoryRequest', ], 'output' => [ 'shape' => 'ConnectDirectoryResult', ], 'errors' => [ [ 'shape' => 'DirectoryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateAlias' => [ 'name' => 'CreateAlias', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateAliasRequest', ], 'output' => [ 'shape' => 'CreateAliasResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateComputer' => [ 'name' => 'CreateComputer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateComputerRequest', ], 'output' => [ 'shape' => 'CreateComputerResult', ], 'errors' => [ [ 'shape' => 'AuthenticationFailedException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateConditionalForwarder' => [ 'name' => 'CreateConditionalForwarder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateConditionalForwarderRequest', ], 'output' => [ 'shape' => 'CreateConditionalForwarderResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateDirectory' => [ 'name' => 'CreateDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDirectoryRequest', ], 'output' => [ 'shape' => 'CreateDirectoryResult', ], 'errors' => [ [ 'shape' => 'DirectoryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateMicrosoftAD' => [ 'name' => 'CreateMicrosoftAD', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateMicrosoftADRequest', ], 'output' => [ 'shape' => 'CreateMicrosoftADResult', ], 'errors' => [ [ 'shape' => 'DirectoryLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'CreateSnapshot' => [ 'name' => 'CreateSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateSnapshotRequest', ], 'output' => [ 'shape' => 'CreateSnapshotResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'SnapshotLimitExceededException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'CreateTrust' => [ 'name' => 'CreateTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTrustRequest', ], 'output' => [ 'shape' => 'CreateTrustResult', ], 'errors' => [ [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DeleteConditionalForwarder' => [ 'name' => 'DeleteConditionalForwarder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteConditionalForwarderRequest', ], 'output' => [ 'shape' => 'DeleteConditionalForwarderResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteDirectory' => [ 'name' => 'DeleteDirectory', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDirectoryRequest', ], 'output' => [ 'shape' => 'DeleteDirectoryResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteSnapshot' => [ 'name' => 'DeleteSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSnapshotRequest', ], 'output' => [ 'shape' => 'DeleteSnapshotResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DeleteTrust' => [ 'name' => 'DeleteTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTrustRequest', ], 'output' => [ 'shape' => 'DeleteTrustResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DeregisterEventTopic' => [ 'name' => 'DeregisterEventTopic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterEventTopicRequest', ], 'output' => [ 'shape' => 'DeregisterEventTopicResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeConditionalForwarders' => [ 'name' => 'DescribeConditionalForwarders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeConditionalForwardersRequest', ], 'output' => [ 'shape' => 'DescribeConditionalForwardersResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeDirectories' => [ 'name' => 'DescribeDirectories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDirectoriesRequest', ], 'output' => [ 'shape' => 'DescribeDirectoriesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeDomainControllers' => [ 'name' => 'DescribeDomainControllers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDomainControllersRequest', ], 'output' => [ 'shape' => 'DescribeDomainControllersResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DescribeEventTopics' => [ 'name' => 'DescribeEventTopics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventTopicsRequest', ], 'output' => [ 'shape' => 'DescribeEventTopicsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeSnapshots' => [ 'name' => 'DescribeSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSnapshotsRequest', ], 'output' => [ 'shape' => 'DescribeSnapshotsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DescribeTrusts' => [ 'name' => 'DescribeTrusts', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTrustsRequest', ], 'output' => [ 'shape' => 'DescribeTrustsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'DisableRadius' => [ 'name' => 'DisableRadius', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableRadiusRequest', ], 'output' => [ 'shape' => 'DisableRadiusResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'DisableSso' => [ 'name' => 'DisableSso', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableSsoRequest', ], 'output' => [ 'shape' => 'DisableSsoResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'AuthenticationFailedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'EnableRadius' => [ 'name' => 'EnableRadius', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableRadiusRequest', ], 'output' => [ 'shape' => 'EnableRadiusResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityAlreadyExistsException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'EnableSso' => [ 'name' => 'EnableSso', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableSsoRequest', ], 'output' => [ 'shape' => 'EnableSsoResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InsufficientPermissionsException', ], [ 'shape' => 'AuthenticationFailedException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetDirectoryLimits' => [ 'name' => 'GetDirectoryLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDirectoryLimitsRequest', ], 'output' => [ 'shape' => 'GetDirectoryLimitsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'GetSnapshotLimits' => [ 'name' => 'GetSnapshotLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSnapshotLimitsRequest', ], 'output' => [ 'shape' => 'GetSnapshotLimitsResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListIpRoutes' => [ 'name' => 'ListIpRoutes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListIpRoutesRequest', ], 'output' => [ 'shape' => 'ListIpRoutesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListSchemaExtensions' => [ 'name' => 'ListSchemaExtensions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSchemaExtensionsRequest', ], 'output' => [ 'shape' => 'ListSchemaExtensionsResult', ], 'errors' => [ [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidNextTokenException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RegisterEventTopic' => [ 'name' => 'RegisterEventTopic', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterEventTopicRequest', ], 'output' => [ 'shape' => 'RegisterEventTopicResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RemoveIpRoutes' => [ 'name' => 'RemoveIpRoutes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveIpRoutesRequest', ], 'output' => [ 'shape' => 'RemoveIpRoutesResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RemoveTagsFromResource' => [ 'name' => 'RemoveTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromResourceRequest', ], 'output' => [ 'shape' => 'RemoveTagsFromResourceResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'ResetUserPassword' => [ 'name' => 'ResetUserPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetUserPasswordRequest', ], 'output' => [ 'shape' => 'ResetUserPasswordResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'UserDoesNotExistException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'RestoreFromSnapshot' => [ 'name' => 'RestoreFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreFromSnapshotRequest', ], 'output' => [ 'shape' => 'RestoreFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'StartSchemaExtension' => [ 'name' => 'StartSchemaExtension', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartSchemaExtensionRequest', ], 'output' => [ 'shape' => 'StartSchemaExtensionResult', ], 'errors' => [ [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'SnapshotLimitExceededException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateConditionalForwarder' => [ 'name' => 'UpdateConditionalForwarder', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateConditionalForwarderRequest', ], 'output' => [ 'shape' => 'UpdateConditionalForwarderResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateNumberOfDomainControllers' => [ 'name' => 'UpdateNumberOfDomainControllers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateNumberOfDomainControllersRequest', ], 'output' => [ 'shape' => 'UpdateNumberOfDomainControllersResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'DirectoryUnavailableException', ], [ 'shape' => 'DomainControllerLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'UpdateRadius' => [ 'name' => 'UpdateRadius', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateRadiusRequest', ], 'output' => [ 'shape' => 'UpdateRadiusResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], ], ], 'VerifyTrust' => [ 'name' => 'VerifyTrust', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'VerifyTrustRequest', ], 'output' => [ 'shape' => 'VerifyTrustResult', ], 'errors' => [ [ 'shape' => 'EntityDoesNotExistException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServiceException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], ], 'shapes' => [ 'AccessUrl' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'AddIpRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'IpRoutes', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'IpRoutes' => [ 'shape' => 'IpRoutes', ], 'UpdateSecurityGroupForDirectoryControllers' => [ 'shape' => 'UpdateSecurityGroupForDirectoryControllers', ], ], ], 'AddIpRoutesResult' => [ 'type' => 'structure', 'members' => [], ], 'AddTagsToResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'Tags', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'AddTagsToResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'AddedDateTime' => [ 'type' => 'timestamp', ], 'AliasName' => [ 'type' => 'string', 'max' => 62, 'min' => 1, 'pattern' => '^(?!d-)([\\da-zA-Z]+)([-]*[\\da-zA-Z])*', ], 'Attribute' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'AttributeName', ], 'Value' => [ 'shape' => 'AttributeValue', ], ], ], 'AttributeName' => [ 'type' => 'string', 'min' => 1, ], 'AttributeValue' => [ 'type' => 'string', ], 'Attributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Attribute', ], ], 'AuthenticationFailedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'AvailabilityZone' => [ 'type' => 'string', ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'CancelSchemaExtensionRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'SchemaExtensionId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SchemaExtensionId' => [ 'shape' => 'SchemaExtensionId', ], ], ], 'CancelSchemaExtensionResult' => [ 'type' => 'structure', 'members' => [], ], 'CidrIp' => [ 'type' => 'string', 'pattern' => '^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\\/([1-9]|[1-2][0-9]|3[0-2]))$', ], 'CidrIps' => [ 'type' => 'list', 'member' => [ 'shape' => 'CidrIp', ], ], 'ClientException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'CloudOnlyDirectoriesLimitReached' => [ 'type' => 'boolean', ], 'Computer' => [ 'type' => 'structure', 'members' => [ 'ComputerId' => [ 'shape' => 'SID', ], 'ComputerName' => [ 'shape' => 'ComputerName', ], 'ComputerAttributes' => [ 'shape' => 'Attributes', ], ], ], 'ComputerName' => [ 'type' => 'string', 'max' => 15, 'min' => 1, ], 'ComputerPassword' => [ 'type' => 'string', 'max' => 64, 'min' => 8, 'pattern' => '[\\u0020-\\u00FF]+', 'sensitive' => true, ], 'ConditionalForwarder' => [ 'type' => 'structure', 'members' => [ 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], 'ReplicationScope' => [ 'shape' => 'ReplicationScope', ], ], ], 'ConditionalForwarders' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConditionalForwarder', ], ], 'ConnectDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Password', 'Size', 'ConnectSettings', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Password' => [ 'shape' => 'ConnectPassword', ], 'Description' => [ 'shape' => 'Description', ], 'Size' => [ 'shape' => 'DirectorySize', ], 'ConnectSettings' => [ 'shape' => 'DirectoryConnectSettings', ], ], ], 'ConnectDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'ConnectPassword' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'ConnectedDirectoriesLimitReached' => [ 'type' => 'boolean', ], 'CreateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'Alias', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Alias' => [ 'shape' => 'AliasName', ], ], ], 'CreateAliasResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Alias' => [ 'shape' => 'AliasName', ], ], ], 'CreateComputerRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'ComputerName', 'Password', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'ComputerName' => [ 'shape' => 'ComputerName', ], 'Password' => [ 'shape' => 'ComputerPassword', ], 'OrganizationalUnitDistinguishedName' => [ 'shape' => 'OrganizationalUnitDN', ], 'ComputerAttributes' => [ 'shape' => 'Attributes', ], ], ], 'CreateComputerResult' => [ 'type' => 'structure', 'members' => [ 'Computer' => [ 'shape' => 'Computer', ], ], ], 'CreateConditionalForwarderRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', 'DnsIpAddrs', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], ], ], 'CreateConditionalForwarderResult' => [ 'type' => 'structure', 'members' => [], ], 'CreateDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Password', 'Size', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Password' => [ 'shape' => 'Password', ], 'Description' => [ 'shape' => 'Description', ], 'Size' => [ 'shape' => 'DirectorySize', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettings', ], ], ], 'CreateDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'CreateMicrosoftADRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Password', 'VpcSettings', ], 'members' => [ 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Password' => [ 'shape' => 'Password', ], 'Description' => [ 'shape' => 'Description', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettings', ], 'Edition' => [ 'shape' => 'DirectoryEdition', ], ], ], 'CreateMicrosoftADResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'CreateSnapshotBeforeSchemaExtension' => [ 'type' => 'boolean', ], 'CreateSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Name' => [ 'shape' => 'SnapshotName', ], ], ], 'CreateSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'CreateTrustRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', 'TrustPassword', 'TrustDirection', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'TrustPassword' => [ 'shape' => 'TrustPassword', ], 'TrustDirection' => [ 'shape' => 'TrustDirection', ], 'TrustType' => [ 'shape' => 'TrustType', ], 'ConditionalForwarderIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], ], ], 'CreateTrustResult' => [ 'type' => 'structure', 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'CreatedDateTime' => [ 'type' => 'timestamp', ], 'CustomerUserName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^(?!.*\\\\|.*"|.*\\/|.*\\[|.*\\]|.*:|.*;|.*\\||.*=|.*,|.*\\+|.*\\*|.*\\?|.*<|.*>|.*@).*$', ], 'DeleteAssociatedConditionalForwarder' => [ 'type' => 'boolean', ], 'DeleteConditionalForwarderRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], ], ], 'DeleteConditionalForwarderResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDirectoryRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DeleteDirectoryResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DeleteSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'SnapshotId', ], 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'DeleteSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'DeleteTrustRequest' => [ 'type' => 'structure', 'required' => [ 'TrustId', ], 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], 'DeleteAssociatedConditionalForwarder' => [ 'shape' => 'DeleteAssociatedConditionalForwarder', ], ], ], 'DeleteTrustResult' => [ 'type' => 'structure', 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'DeregisterEventTopicRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'TopicName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicName' => [ 'shape' => 'TopicName', ], ], ], 'DeregisterEventTopicResult' => [ 'type' => 'structure', 'members' => [], ], 'DescribeConditionalForwardersRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainNames' => [ 'shape' => 'RemoteDomainNames', ], ], ], 'DescribeConditionalForwardersResult' => [ 'type' => 'structure', 'members' => [ 'ConditionalForwarders' => [ 'shape' => 'ConditionalForwarders', ], ], ], 'DescribeDirectoriesRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryIds' => [ 'shape' => 'DirectoryIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeDirectoriesResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryDescriptions' => [ 'shape' => 'DirectoryDescriptions', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeDomainControllersRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'DomainControllerIds' => [ 'shape' => 'DomainControllerIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeDomainControllersResult' => [ 'type' => 'structure', 'members' => [ 'DomainControllers' => [ 'shape' => 'DomainControllers', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeEventTopicsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicNames' => [ 'shape' => 'TopicNames', ], ], ], 'DescribeEventTopicsResult' => [ 'type' => 'structure', 'members' => [ 'EventTopics' => [ 'shape' => 'EventTopics', ], ], ], 'DescribeSnapshotsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SnapshotIds' => [ 'shape' => 'SnapshotIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeSnapshotsResult' => [ 'type' => 'structure', 'members' => [ 'Snapshots' => [ 'shape' => 'Snapshots', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'DescribeTrustsRequest' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TrustIds' => [ 'shape' => 'TrustIds', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'DescribeTrustsResult' => [ 'type' => 'structure', 'members' => [ 'Trusts' => [ 'shape' => 'Trusts', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^([a-zA-Z0-9_])[\\\\a-zA-Z0-9_@#%*+=:?./!\\s-]*$', ], 'DesiredNumberOfDomainControllers' => [ 'type' => 'integer', 'min' => 2, ], 'DirectoryConnectSettings' => [ 'type' => 'structure', 'required' => [ 'VpcId', 'SubnetIds', 'CustomerDnsIps', 'CustomerUserName', ], 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'CustomerDnsIps' => [ 'shape' => 'DnsIpAddrs', ], 'CustomerUserName' => [ 'shape' => 'UserName', ], ], ], 'DirectoryConnectSettingsDescription' => [ 'type' => 'structure', 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'CustomerUserName' => [ 'shape' => 'UserName', ], 'SecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'ConnectIps' => [ 'shape' => 'IpAddrs', ], ], ], 'DirectoryDescription' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'Name' => [ 'shape' => 'DirectoryName', ], 'ShortName' => [ 'shape' => 'DirectoryShortName', ], 'Size' => [ 'shape' => 'DirectorySize', ], 'Edition' => [ 'shape' => 'DirectoryEdition', ], 'Alias' => [ 'shape' => 'AliasName', ], 'AccessUrl' => [ 'shape' => 'AccessUrl', ], 'Description' => [ 'shape' => 'Description', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], 'Stage' => [ 'shape' => 'DirectoryStage', ], 'LaunchTime' => [ 'shape' => 'LaunchTime', ], 'StageLastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], 'Type' => [ 'shape' => 'DirectoryType', ], 'VpcSettings' => [ 'shape' => 'DirectoryVpcSettingsDescription', ], 'ConnectSettings' => [ 'shape' => 'DirectoryConnectSettingsDescription', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], 'RadiusStatus' => [ 'shape' => 'RadiusStatus', ], 'StageReason' => [ 'shape' => 'StageReason', ], 'SsoEnabled' => [ 'shape' => 'SsoEnabled', ], 'DesiredNumberOfDomainControllers' => [ 'shape' => 'DesiredNumberOfDomainControllers', ], ], ], 'DirectoryDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryDescription', ], ], 'DirectoryEdition' => [ 'type' => 'string', 'enum' => [ 'Enterprise', 'Standard', ], ], 'DirectoryId' => [ 'type' => 'string', 'pattern' => '^d-[0-9a-f]{10}$', ], 'DirectoryIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DirectoryId', ], ], 'DirectoryLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryLimits' => [ 'type' => 'structure', 'members' => [ 'CloudOnlyDirectoriesLimit' => [ 'shape' => 'Limit', ], 'CloudOnlyDirectoriesCurrentCount' => [ 'shape' => 'Limit', ], 'CloudOnlyDirectoriesLimitReached' => [ 'shape' => 'CloudOnlyDirectoriesLimitReached', ], 'CloudOnlyMicrosoftADLimit' => [ 'shape' => 'Limit', ], 'CloudOnlyMicrosoftADCurrentCount' => [ 'shape' => 'Limit', ], 'CloudOnlyMicrosoftADLimitReached' => [ 'shape' => 'CloudOnlyDirectoriesLimitReached', ], 'ConnectedDirectoriesLimit' => [ 'shape' => 'Limit', ], 'ConnectedDirectoriesCurrentCount' => [ 'shape' => 'Limit', ], 'ConnectedDirectoriesLimitReached' => [ 'shape' => 'ConnectedDirectoriesLimitReached', ], ], ], 'DirectoryName' => [ 'type' => 'string', 'pattern' => '^([a-zA-Z0-9]+[\\\\.-])+([a-zA-Z0-9])+$', ], 'DirectoryShortName' => [ 'type' => 'string', 'pattern' => '^[^\\\\/:*?\\"\\<\\>|.]+[^\\\\/:*?\\"<>|]*$', ], 'DirectorySize' => [ 'type' => 'string', 'enum' => [ 'Small', 'Large', ], ], 'DirectoryStage' => [ 'type' => 'string', 'enum' => [ 'Requested', 'Creating', 'Created', 'Active', 'Inoperable', 'Impaired', 'Restoring', 'RestoreFailed', 'Deleting', 'Deleted', 'Failed', ], ], 'DirectoryType' => [ 'type' => 'string', 'enum' => [ 'SimpleAD', 'ADConnector', 'MicrosoftAD', ], ], 'DirectoryUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DirectoryVpcSettings' => [ 'type' => 'structure', 'required' => [ 'VpcId', 'SubnetIds', ], 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], ], ], 'DirectoryVpcSettingsDescription' => [ 'type' => 'structure', 'members' => [ 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetIds' => [ 'shape' => 'SubnetIds', ], 'SecurityGroupId' => [ 'shape' => 'SecurityGroupId', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], ], ], 'DisableRadiusRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'DisableRadiusResult' => [ 'type' => 'structure', 'members' => [], ], 'DisableSsoRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'UserName', ], 'Password' => [ 'shape' => 'ConnectPassword', ], ], ], 'DisableSsoResult' => [ 'type' => 'structure', 'members' => [], ], 'DnsIpAddrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddr', ], ], 'DomainController' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'DomainControllerId' => [ 'shape' => 'DomainControllerId', ], 'DnsIpAddr' => [ 'shape' => 'IpAddr', ], 'VpcId' => [ 'shape' => 'VpcId', ], 'SubnetId' => [ 'shape' => 'SubnetId', ], 'AvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'Status' => [ 'shape' => 'DomainControllerStatus', ], 'StatusReason' => [ 'shape' => 'DomainControllerStatusReason', ], 'LaunchTime' => [ 'shape' => 'LaunchTime', ], 'StatusLastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], ], ], 'DomainControllerId' => [ 'type' => 'string', 'pattern' => '^dc-[0-9a-f]{10}$', ], 'DomainControllerIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainControllerId', ], ], 'DomainControllerLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'DomainControllerStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Active', 'Impaired', 'Restoring', 'Deleting', 'Deleted', 'Failed', ], ], 'DomainControllerStatusReason' => [ 'type' => 'string', ], 'DomainControllers' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainController', ], ], 'EnableRadiusRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RadiusSettings', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], ], ], 'EnableRadiusResult' => [ 'type' => 'structure', 'members' => [], ], 'EnableSsoRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'UserName', ], 'Password' => [ 'shape' => 'ConnectPassword', ], ], ], 'EnableSsoResult' => [ 'type' => 'structure', 'members' => [], ], 'EndDateTime' => [ 'type' => 'timestamp', ], 'EntityAlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'EntityDoesNotExistException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'EventTopic' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicName' => [ 'shape' => 'TopicName', ], 'TopicArn' => [ 'shape' => 'TopicArn', ], 'CreatedDateTime' => [ 'shape' => 'CreatedDateTime', ], 'Status' => [ 'shape' => 'TopicStatus', ], ], ], 'EventTopics' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventTopic', ], ], 'ExceptionMessage' => [ 'type' => 'string', ], 'GetDirectoryLimitsRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetDirectoryLimitsResult' => [ 'type' => 'structure', 'members' => [ 'DirectoryLimits' => [ 'shape' => 'DirectoryLimits', ], ], ], 'GetSnapshotLimitsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], ], ], 'GetSnapshotLimitsResult' => [ 'type' => 'structure', 'members' => [ 'SnapshotLimits' => [ 'shape' => 'SnapshotLimits', ], ], ], 'InsufficientPermissionsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidNextTokenException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'InvalidPasswordException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'IpAddr' => [ 'type' => 'string', 'pattern' => '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$', ], 'IpAddrs' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpAddr', ], ], 'IpRoute' => [ 'type' => 'structure', 'members' => [ 'CidrIp' => [ 'shape' => 'CidrIp', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'IpRouteInfo' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'CidrIp' => [ 'shape' => 'CidrIp', ], 'IpRouteStatusMsg' => [ 'shape' => 'IpRouteStatusMsg', ], 'AddedDateTime' => [ 'shape' => 'AddedDateTime', ], 'IpRouteStatusReason' => [ 'shape' => 'IpRouteStatusReason', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'IpRouteLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'IpRouteStatusMsg' => [ 'type' => 'string', 'enum' => [ 'Adding', 'Added', 'Removing', 'Removed', 'AddFailed', 'RemoveFailed', ], ], 'IpRouteStatusReason' => [ 'type' => 'string', ], 'IpRoutes' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpRoute', ], ], 'IpRoutesInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'IpRouteInfo', ], ], 'LastUpdatedDateTime' => [ 'type' => 'timestamp', ], 'LaunchTime' => [ 'type' => 'timestamp', ], 'LdifContent' => [ 'type' => 'string', 'max' => 500000, 'min' => 1, ], 'Limit' => [ 'type' => 'integer', 'min' => 0, ], 'ListIpRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'ListIpRoutesResult' => [ 'type' => 'structure', 'members' => [ 'IpRoutesInfo' => [ 'shape' => 'IpRoutesInfo', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSchemaExtensionsRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'ListSchemaExtensionsResult' => [ 'type' => 'structure', 'members' => [ 'SchemaExtensionsInfo' => [ 'shape' => 'SchemaExtensionsInfo', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'Limit' => [ 'shape' => 'Limit', ], ], ], 'ListTagsForResourceResult' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ManualSnapshotsLimitReached' => [ 'type' => 'boolean', ], 'NextToken' => [ 'type' => 'string', ], 'OrganizationalUnitDN' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, ], 'Password' => [ 'type' => 'string', 'pattern' => '(?=^.{8,64}$)((?=.*\\d)(?=.*[A-Z])(?=.*[a-z])|(?=.*\\d)(?=.*[^A-Za-z0-9\\s])(?=.*[a-z])|(?=.*[^A-Za-z0-9\\s])(?=.*[A-Z])(?=.*[a-z])|(?=.*\\d)(?=.*[A-Z])(?=.*[^A-Za-z0-9\\s]))^.*', 'sensitive' => true, ], 'PortNumber' => [ 'type' => 'integer', 'max' => 65535, 'min' => 1025, ], 'RadiusAuthenticationProtocol' => [ 'type' => 'string', 'enum' => [ 'PAP', 'CHAP', 'MS-CHAPv1', 'MS-CHAPv2', ], ], 'RadiusDisplayLabel' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'RadiusRetries' => [ 'type' => 'integer', 'max' => 10, 'min' => 0, ], 'RadiusSettings' => [ 'type' => 'structure', 'members' => [ 'RadiusServers' => [ 'shape' => 'Servers', ], 'RadiusPort' => [ 'shape' => 'PortNumber', ], 'RadiusTimeout' => [ 'shape' => 'RadiusTimeout', ], 'RadiusRetries' => [ 'shape' => 'RadiusRetries', ], 'SharedSecret' => [ 'shape' => 'RadiusSharedSecret', ], 'AuthenticationProtocol' => [ 'shape' => 'RadiusAuthenticationProtocol', ], 'DisplayLabel' => [ 'shape' => 'RadiusDisplayLabel', ], 'UseSameUsername' => [ 'shape' => 'UseSameUsername', ], ], ], 'RadiusSharedSecret' => [ 'type' => 'string', 'max' => 512, 'min' => 8, 'sensitive' => true, ], 'RadiusStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Completed', 'Failed', ], ], 'RadiusTimeout' => [ 'type' => 'integer', 'max' => 20, 'min' => 1, ], 'RegisterEventTopicRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'TopicName', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TopicName' => [ 'shape' => 'TopicName', ], ], ], 'RegisterEventTopicResult' => [ 'type' => 'structure', 'members' => [], ], 'RemoteDomainName' => [ 'type' => 'string', 'pattern' => '^([a-zA-Z0-9]+[\\\\.-])+([a-zA-Z0-9])+[.]?$', ], 'RemoteDomainNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemoteDomainName', ], ], 'RemoveIpRoutesRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'CidrIps', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'CidrIps' => [ 'shape' => 'CidrIps', ], ], ], 'RemoveIpRoutesResult' => [ 'type' => 'structure', 'members' => [], ], 'RemoveTagsFromResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceId', 'TagKeys', ], 'members' => [ 'ResourceId' => [ 'shape' => 'ResourceId', ], 'TagKeys' => [ 'shape' => 'TagKeys', ], ], ], 'RemoveTagsFromResourceResult' => [ 'type' => 'structure', 'members' => [], ], 'ReplicationScope' => [ 'type' => 'string', 'enum' => [ 'Domain', ], ], 'RequestId' => [ 'type' => 'string', 'pattern' => '^([A-Fa-f0-9]{8}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{4}-[A-Fa-f0-9]{12})$', ], 'ResetUserPasswordRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'UserName', 'NewPassword', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'UserName' => [ 'shape' => 'CustomerUserName', ], 'NewPassword' => [ 'shape' => 'UserPassword', ], ], ], 'ResetUserPasswordResult' => [ 'type' => 'structure', 'members' => [], ], 'ResourceId' => [ 'type' => 'string', 'pattern' => '^[d]-[0-9a-f]{10}$', ], 'RestoreFromSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'SnapshotId', ], 'members' => [ 'SnapshotId' => [ 'shape' => 'SnapshotId', ], ], ], 'RestoreFromSnapshotResult' => [ 'type' => 'structure', 'members' => [], ], 'SID' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[&\\w+-.@]+', ], 'SchemaExtensionId' => [ 'type' => 'string', 'pattern' => '^e-[0-9a-f]{10}$', ], 'SchemaExtensionInfo' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SchemaExtensionId' => [ 'shape' => 'SchemaExtensionId', ], 'Description' => [ 'shape' => 'Description', ], 'SchemaExtensionStatus' => [ 'shape' => 'SchemaExtensionStatus', ], 'SchemaExtensionStatusReason' => [ 'shape' => 'SchemaExtensionStatusReason', ], 'StartDateTime' => [ 'shape' => 'StartDateTime', ], 'EndDateTime' => [ 'shape' => 'EndDateTime', ], ], ], 'SchemaExtensionStatus' => [ 'type' => 'string', 'enum' => [ 'Initializing', 'CreatingSnapshot', 'UpdatingSchema', 'Replicating', 'CancelInProgress', 'RollbackInProgress', 'Cancelled', 'Failed', 'Completed', ], ], 'SchemaExtensionStatusReason' => [ 'type' => 'string', ], 'SchemaExtensionsInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaExtensionInfo', ], ], 'SecurityGroupId' => [ 'type' => 'string', 'pattern' => '^(sg-[0-9a-f]{8}|sg-[0-9a-f]{17})$', ], 'Server' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Servers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Server', ], ], 'ServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, 'fault' => true, ], 'Snapshot' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'SnapshotId' => [ 'shape' => 'SnapshotId', ], 'Type' => [ 'shape' => 'SnapshotType', ], 'Name' => [ 'shape' => 'SnapshotName', ], 'Status' => [ 'shape' => 'SnapshotStatus', ], 'StartTime' => [ 'shape' => 'StartTime', ], ], ], 'SnapshotId' => [ 'type' => 'string', 'pattern' => '^s-[0-9a-f]{10}$', ], 'SnapshotIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SnapshotId', ], ], 'SnapshotLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'SnapshotLimits' => [ 'type' => 'structure', 'members' => [ 'ManualSnapshotsLimit' => [ 'shape' => 'Limit', ], 'ManualSnapshotsCurrentCount' => [ 'shape' => 'Limit', ], 'ManualSnapshotsLimitReached' => [ 'shape' => 'ManualSnapshotsLimitReached', ], ], ], 'SnapshotName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^([a-zA-Z0-9_])[\\\\a-zA-Z0-9_@#%*+=:?./!\\s-]*$', ], 'SnapshotStatus' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Completed', 'Failed', ], ], 'SnapshotType' => [ 'type' => 'string', 'enum' => [ 'Auto', 'Manual', ], ], 'Snapshots' => [ 'type' => 'list', 'member' => [ 'shape' => 'Snapshot', ], ], 'SsoEnabled' => [ 'type' => 'boolean', ], 'StageReason' => [ 'type' => 'string', ], 'StartDateTime' => [ 'type' => 'timestamp', ], 'StartSchemaExtensionRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'CreateSnapshotBeforeSchemaExtension', 'LdifContent', 'Description', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'CreateSnapshotBeforeSchemaExtension' => [ 'shape' => 'CreateSnapshotBeforeSchemaExtension', ], 'LdifContent' => [ 'shape' => 'LdifContent', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'StartSchemaExtensionResult' => [ 'type' => 'structure', 'members' => [ 'SchemaExtensionId' => [ 'shape' => 'SchemaExtensionId', ], ], ], 'StartTime' => [ 'type' => 'timestamp', ], 'StateLastUpdatedDateTime' => [ 'type' => 'timestamp', ], 'SubnetId' => [ 'type' => 'string', 'pattern' => '^(subnet-[0-9a-f]{8}|subnet-[0-9a-f]{17})$', ], 'SubnetIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'SubnetId', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TopicArn' => [ 'type' => 'string', ], 'TopicName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TopicNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'TopicName', ], ], 'TopicStatus' => [ 'type' => 'string', 'enum' => [ 'Registered', 'Topic not found', 'Failed', 'Deleted', ], ], 'Trust' => [ 'type' => 'structure', 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'TrustId' => [ 'shape' => 'TrustId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'TrustType' => [ 'shape' => 'TrustType', ], 'TrustDirection' => [ 'shape' => 'TrustDirection', ], 'TrustState' => [ 'shape' => 'TrustState', ], 'CreatedDateTime' => [ 'shape' => 'CreatedDateTime', ], 'LastUpdatedDateTime' => [ 'shape' => 'LastUpdatedDateTime', ], 'StateLastUpdatedDateTime' => [ 'shape' => 'StateLastUpdatedDateTime', ], 'TrustStateReason' => [ 'shape' => 'TrustStateReason', ], ], ], 'TrustDirection' => [ 'type' => 'string', 'enum' => [ 'One-Way: Outgoing', 'One-Way: Incoming', 'Two-Way', ], ], 'TrustId' => [ 'type' => 'string', 'pattern' => '^t-[0-9a-f]{10}$', ], 'TrustIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrustId', ], ], 'TrustPassword' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'sensitive' => true, ], 'TrustState' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Created', 'Verifying', 'VerifyFailed', 'Verified', 'Deleting', 'Deleted', 'Failed', ], ], 'TrustStateReason' => [ 'type' => 'string', ], 'TrustType' => [ 'type' => 'string', 'enum' => [ 'Forest', ], ], 'Trusts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Trust', ], ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'UpdateConditionalForwarderRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RemoteDomainName', 'DnsIpAddrs', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RemoteDomainName' => [ 'shape' => 'RemoteDomainName', ], 'DnsIpAddrs' => [ 'shape' => 'DnsIpAddrs', ], ], ], 'UpdateConditionalForwarderResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateNumberOfDomainControllersRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'DesiredNumber', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'DesiredNumber' => [ 'shape' => 'DesiredNumberOfDomainControllers', ], ], ], 'UpdateNumberOfDomainControllersResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateRadiusRequest' => [ 'type' => 'structure', 'required' => [ 'DirectoryId', 'RadiusSettings', ], 'members' => [ 'DirectoryId' => [ 'shape' => 'DirectoryId', ], 'RadiusSettings' => [ 'shape' => 'RadiusSettings', ], ], ], 'UpdateRadiusResult' => [ 'type' => 'structure', 'members' => [], ], 'UpdateSecurityGroupForDirectoryControllers' => [ 'type' => 'boolean', ], 'UseSameUsername' => [ 'type' => 'boolean', ], 'UserDoesNotExistException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ExceptionMessage', ], 'RequestId' => [ 'shape' => 'RequestId', ], ], 'exception' => true, ], 'UserName' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[a-zA-Z0-9._-]+', ], 'UserPassword' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'sensitive' => true, ], 'VerifyTrustRequest' => [ 'type' => 'structure', 'required' => [ 'TrustId', ], 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'VerifyTrustResult' => [ 'type' => 'structure', 'members' => [ 'TrustId' => [ 'shape' => 'TrustId', ], ], ], 'VpcId' => [ 'type' => 'string', 'pattern' => '^(vpc-[0-9a-f]{8}|vpc-[0-9a-f]{17})$', ], ],];
