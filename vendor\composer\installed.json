[{"name": "ralouphie/getallheaders", "version": "2.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "reference": "5601c8a83fbba7ef674a7369456d12f1e0d0eafa", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": ">=1.0"}, "time": "2016-02-11T07:05:27+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders."}, {"name": "psr/http-message", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"]}, {"name": "guzzlehttp/psr7", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "58f6e264ef0e4107c8b3d15a9c2e653b2bce0b4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/58f6e264ef0e4107c8b3d15a9c2e653b2bce0b4d", "reference": "58f6e264ef0e4107c8b3d15a9c2e653b2bce0b4d", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "time": "2018-06-08T08:55:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "request", "response", "stream", "uri", "url"]}, {"name": "guzzlehttp/promises", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "136531aa4e42f9b1971a47fb0faf60da00d2fefa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/136531aa4e42f9b1971a47fb0faf60da00d2fefa", "reference": "136531aa4e42f9b1971a47fb0faf60da00d2fefa", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.36"}, "time": "2018-03-25T01:26:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"]}, {"name": "guzzlehttp/guzzle", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7bc46be28e4b01b96d0dfda8c58169072e626558"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7bc46be28e4b01b96d0dfda8c58169072e626558", "reference": "7bc46be28e4b01b96d0dfda8c58169072e626558", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "time": "2018-06-25T23:51:40+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "installation-source": "source", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"]}, {"name": "guzzlehttp/oauth-subscriber", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "https://github.com/guzzle/oauth-subscriber.git", "reference": "fbd22b4967985509f0f9f878196fcaa961916dfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/oauth-subscriber/zipball/fbd22b4967985509f0f9f878196fcaa961916dfa", "reference": "fbd22b4967985509f0f9f878196fcaa961916dfa", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~6.0", "php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "time": "2018-02-26T22:08:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "0.3-dev"}}, "installation-source": "source", "autoload": {"psr-4": {"GuzzleHttp\\Subscriber\\Oauth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle OAuth 1.0 subscriber", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "o<PERSON>h"]}, {"name": "rednovalabs/storedge-sdk-php", "version": "dev-master", "version_normalized": "9999999-dev", "source": {"type": "git", "url": "**************:rednovalabs/storedge-api-client-php.git", "reference": "d3cdc3e5b6040a38c3c9dce70a63b48fb8e74e2d"}, "require": {"guzzlehttp/oauth-subscriber": "0.3.*"}, "time": "2017-07-10T16:06:28+00:00", "type": "library", "installation-source": "source", "autoload": {"psr-4": {"RedNovaLabs\\Storedge\\": "./src/RedNovaLabs/Storedge"}}, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Guzzle client wrapper for Storedge API"}]