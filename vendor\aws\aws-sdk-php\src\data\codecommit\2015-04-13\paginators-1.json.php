<?php
// This file was auto-generated from sdk-root/src/data/codecommit/2015-04-13/paginators-1.json
return [ 'pagination' => [ 'DescribePullRequestEvents' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', ], 'GetCommentsForComparedCommit' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', ], 'GetCommentsForPullRequest' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', ], 'GetDifferences' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListBranches' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'result_key' => 'branches', ], 'ListPullRequests' => [ 'input_token' => 'nextToken', 'limit_key' => 'maxResults', 'output_token' => 'nextToken', ], 'ListRepositories' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'result_key' => 'repositories', ], ],];
